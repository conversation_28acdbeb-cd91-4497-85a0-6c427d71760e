<?php
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

// بيانات الاتصال
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$database = 'kidzrcle_rwda';
$host = 'localhost';

echo "<h3>بيانات الاتصال:</h3>";
echo "<p>المضيف: $host</p>";
echo "<p>قاعدة البيانات: $database</p>";
echo "<p>اسم المستخدم: $user</p>";

// محاولة الاتصال
$con = new mysqli($host, $user, $pass, $database);

if ($con->connect_error) {
    echo "<p style='color: red;'>❌ فشل الاتصال: " . $con->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ تم الاتصال بنجاح!</p>";
}

$con->set_charset("utf8");

// اختبار الجداول المطلوبة
$tables = ['users_tb', 'stud_tb', 'stud_pay'];

echo "<h3>اختبار وجود الجداول:</h3>";
foreach ($tables as $table) {
    $result = $con->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ الجدول $table موجود</p>";
        
        // عدد السجلات في كل جدول
        $count_result = $con->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p style='margin-left: 20px;'>عدد السجلات: $count</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ الجدول $table غير موجود</p>";
    }
}

// اختبار بيانات المستخدمين
echo "<h3>اختبار بيانات المستخدمين:</h3>";
$sql = "SELECT id_user, user_name FROM users_tb LIMIT 5";
$result = $con->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['id_user']}</td><td>{$row['user_name']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد بيانات مستخدمين</p>";
}

// اختبار بيانات الطلاب
echo "<h3>اختبار بيانات الطلاب:</h3>";
$sql = "SELECT COUNT(*) as total FROM stud_tb";
$result = $con->query($sql);
if ($result) {
    $total = $result->fetch_assoc()['total'];
    echo "<p>إجمالي عدد الطلاب: $total</p>";
}

// اختبار بيانات الدفع
echo "<h3>اختبار بيانات الدفع:</h3>";
$sql = "SELECT COUNT(*) as total FROM stud_pay";
$result = $con->query($sql);
if ($result) {
    $total = $result->fetch_assoc()['total'];
    echo "<p>إجمالي عدد سجلات الدفع: $total</p>";
}

// اختبار الاستعلام المركب
echo "<h3>اختبار الاستعلام المركب:</h3>";
$sql = "SELECT COUNT(*) as total 
        FROM stud_tb, stud_pay, users_tb 
        WHERE stud_tb.id = stud_pay.id_stud 
        AND users_tb.id_user = stud_tb.userID";
$result = $con->query($sql);
if ($result) {
    $total = $result->fetch_assoc()['total'];
    echo "<p style='color: green;'>✅ الاستعلام المركب يعمل بنجاح</p>";
    echo "<p>عدد السجلات المترابطة: $total</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في الاستعلام المركب: " . $con->error . "</p>";
}

// اختبار استعلام محدد لمستخدم
echo "<h3>اختبار استعلام لمستخدم محدد:</h3>";
$sql = "SELECT id_user FROM users_tb LIMIT 1";
$result = $con->query($sql);
if ($result && $result->num_rows > 0) {
    $user_id = $result->fetch_assoc()['id_user'];
    echo "<p>اختبار المستخدم رقم: $user_id</p>";
    
    $sql2 = "SELECT COUNT(*) as count 
             FROM stud_tb, stud_pay, users_tb 
             WHERE stud_tb.id = stud_pay.id_stud 
             AND users_tb.id_user = stud_tb.userID 
             AND users_tb.id_user = $user_id";
    $result2 = $con->query($sql2);
    if ($result2) {
        $count = $result2->fetch_assoc()['count'];
        echo "<p>عدد طلاب هذا المستخدم: $count</p>";
    }
}

$con->close();
echo "<h3 style='color: green;'>انتهى الاختبار بنجاح!</h3>";
?>
