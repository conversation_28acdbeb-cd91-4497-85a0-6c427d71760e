/* تصميم عام للطلاب - متجاوب للهاتف */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(45deg, #2c3e50, #3498db) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 0.8rem 0;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 20px;
    margin: 0 0.2rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* قسم الترحيب */
.welcome-section {
    padding: 2rem 1rem;
}

.welcome-card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 2rem;
}

.welcome-header {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-header h1 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.welcome-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* معلومات الطالب */
.student-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.info-card, .subscription-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.info-card h3, .subscription-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.8rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background: white;
    border-radius: 10px;
    border-right: 4px solid #3498db;
}

.info-item .label {
    font-weight: bold;
    color: #2c3e50;
}

.info-item .value {
    color: #34495e;
}

/* حالة الاشتراك */
.subscription-info {
    text-align: center;
}

.status-badge {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.status-active {
    background: #d4edda;
    color: #155724;
    border: 2px solid #28a745;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffc107;
}

.status-expired {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #dc3545;
}

.subscription-details {
    text-align: right;
    margin-top: 1rem;
}

.subscription-details p {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* قسم الميزات */
.features-section {
    padding: 0 1rem 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    text-decoration: none;
    color: inherit;
    border-color: #3498db;
}

.feature-card.leave-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #3498db, #2c3e50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.feature-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: bold;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
    font-size: 1rem;
}

/* تصميم متجاوب للهاتف */
@media (max-width: 768px) {
    .welcome-card {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .welcome-header h1 {
        font-size: 1.6rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .welcome-section {
        padding: 1rem 0.5rem;
    }
    
    .welcome-card {
        padding: 1rem;
        margin: 0.5rem;
    }
    
    .feature-card {
        padding: 1rem;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .feature-card h3 {
        font-size: 1.1rem;
    }
    
    .feature-card p {
        font-size: 0.9rem;
    }
}

/* تأثيرات إضافية */
.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* أزرار مخصصة */
.btn-primary-custom {
    background: linear-gradient(45deg, #3498db, #2c3e50);
    border: none;
    border-radius: 25px;
    padding: 0.8rem 2rem;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}
