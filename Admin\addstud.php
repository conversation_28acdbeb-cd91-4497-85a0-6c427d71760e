<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أضافة طالب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>

  
    <form method="POST">
 <div class='contin'>

 <div class='input-box'>
        <input type="hidden" placeholder=  "معرف الطالب" name="id_note" required value=   <?php 
    $sql='SELECT id_note FROM stud_tb';
    $resss=mysqli_query($con,$sql);
    while($row=mysqli_fetch_assoc($resss)){
       $id_n=$row['id_note'];  
    }
    if(empty($id_n)&& $id_n=null){
        echo 0;
    }else{
         echo $id_n+1;
    }
    
    ?>
 >
        <label for="id_note"> رقم الوصل <label>
        <input type="number" placeholder="  رقم وصل الاشتراك" name="id_pay" required>
        <label for="name">اسم الطالب <label>
        <input type="text" name="name" required>
        <label for="age"> العمر <label>
        <input type="number" name="age" required>
        <label for="sex" id='sex'> الجنس  </label>
        <div class="chek" required>
         <label for="">
         ذكر<input type="radio" name="sex" value="ذكر" id="email">
         </label>
        <label for="">
        انثى<input type="radio" name="sex" value="انثى" id=femail>
        
        </label> 
        </div>
        <label for="catg2" id='catg'>صنف التسجيل </label>
        <div class="chek2" required>
         <label for="">
         روضة<input type="radio" name="catg" value="روضة" id="email">
         </label>
        <label for="">
        حضانة <input type="radio" name="catg" value="حضانة" id=femail>
        </label> 
        <label for="">
         تحضيري <input type="radio" name="catg" value="تحضيري" id=femail>
        
        </label> 
        <label for="">
        تمهيدي <input type="radio" name="catg" value="تمهيدي" id=femail>
        
        </label> 
        </div>
        <label for="loc" >موقع السكن   <label>
        <input type="text" placeholder="بلوك - عمارة " name="loc" required>

        <label for="p_name">اسم ولي الامر <label>
        <input type="text" placeholder="اسم ولي الامر" name="p_name" required>

        <label for="p_phone">رقم ولي الامر <label>
        <input type="text" placeholder=" 07xx-xxxx-xxxx " name="p_phone" maxlength="11">
        <label for="datein">تاريخ التسجيل <label>
        <input type="date"  name="datein" required> 
        <label for="cash_stud"> قيمة الاشتراك <label>
        <input type="number" placeholder=" المبلغ  IQD " name="cash_stud" required>

        <label for="health_status">الحالة الصحية <label>
        <textarea name="health_status" placeholder="اكتب الحالة الصحية للطالب (أي أمراض أو حساسية أو ملاحظات طبية)" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;"></textarea>

        <label for="registration_status">حالة التسجيل <label>
        <select name="registration_status" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;">
            <option value="">اختر حالة التسجيل</option>
            <option value="تسجيل جديد">تسجيل جديد</option>
            <option value="مشترك">مشترك</option>
        </select>

        <label for="userID">اختر المستخدم <label>
        <select name="userID" id="selc2" placeholder='اختر مستخدم' style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;"">
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>
           
        </select>
        <div>
        <button class=btn name="addS" id="23">حفظ </button>
        </div>
       
        
  </dvi>
  <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC,done){
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)";
    }, 4900);
    setInterval(()=>{
        if(done==1){
            window.location.href="../Users/<USER>";
        }else{

        }
    },5200)
   
   
    
}
  </script>
<?php
$datenow=date('Y-m-d');

$pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda',$user,$pass);
if(isset($_POST['addS'])){
    $id_stud=$_POST['id_note'];
    $chek=$pdo->prepare("SELECT * FROM `stud_tb` WHERE id_note = ?");
    $chek->execute([$id_stud]);
    $res=$chek->rowCount();
    if($res>0){
        $msg1=" ! انتبه ";
        $msg2="يرجى عدم تكرار  البيانات "; 
        $iconC="fa-solid fa-circle-info";
       echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','0')</script>";
    }elseif(!isset($_POST['sex'])){
        $msg1=" ! انتبه ";
        $msg2="يجب اختيار جنس الطالب";
        $iconC="fa-solid fa-circle-info";
        echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','0')</script>";
    
    
    
     }elseif(!isset($_POST['catg'])){
        $msg1=" ! انتبه ";
        $msg2="يجب اختيار صنف التعليم ";
        $iconC="fa-solid fa-circle-info";
        echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','0')</script>";
     }else{
    $id_note=$_POST['id_note'];
    $name=$_POST['name'];
    $age=$_POST['age'];
    $sex=$_POST['sex'];
    $catg=$_POST['catg'];
    $datein=$_POST['datein'];
    $p_name=$_POST['p_name'];
    $p_phone=$_POST['p_phone'];
    $cash_stud=$_POST['cash_stud'];
    $date_exp=date('Y-m-d', strtotime($datein. ' + 30 days'));
    $userID=$_POST['userID'];
    $loc=$_POST['loc'];
    $id_pay=$_POST['id_pay'];
    $health_status=$_POST['health_status'];
    $registration_status=$_POST['registration_status'];
    if(strlen($name)<1){
        $msg1=" ! انتبه ";
        $msg2="يرجى التاكد من اسم الطالب ";
        $iconC="fa-solid fa-circle-info";
       echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','0')</script>";
    }else{



        $query="INSERT INTO stud_tb(id_note,userID,name,age,sex,catg,datein,p_name,p_phone,loc,student_status,health_status,registration_status)VALUES('$id_note','$userID','$name','$age','$sex','$catg','$datein','$p_name','$p_phone','$loc','جديد','$health_status','$registration_status') ";
        $res1=mysqli_query($con,$query);
      
        if($res1==1){
            $sql="SELECT id FROM stud_tb WHERE id_note='$id_note'";
            $ress=mysqli_query($con,$sql);
            $row=mysqli_fetch_assoc($ress);
            $stud_id=$row['id'];
            $query2="INSERT INTO stud_pay(cash_stud,date_exp,id_stud,id_pay) VALUES ('$cash_stud','$date_exp','$stud_id','$id_pay') " ;
            $res2=mysqli_query($con,$query2);
            if($res2){
                $msg1=" ! تمت ";
                $msg2="تم اضافة بيانات الطالب بنجاح";
                $iconC="fa fa-circle-check";
                echo "<script>StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC','1')</script>";
               
            } 
        }
    }
    }

     }



     


?>
  
   </body>
   
</html>