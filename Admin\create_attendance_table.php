<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

echo "<h2>إنشاء جدول حضور الموظفين</h2>";

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

if ($con) {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // التحقق من وجود جدول employee_attendance
    $check_table = $con->query("SHOW TABLES LIKE 'employee_attendance'");
    
    if ($check_table && $check_table->num_rows > 0) {
        echo "<p style='color: blue;'>ℹ️ جدول employee_attendance موجود بالفعل</p>";
        
        // عرض هيكل الجدول
        $structure = $con->query("DESCRIBE employee_attendance");
        echo "<h3>هيكل الجدول:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr>";
        while ($col = $structure->fetch_assoc()) {
            echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>";
        }
        echo "</table>";
        
        // عرض عدد السجلات
        $count_result = $con->query("SELECT COUNT(*) as total FROM employee_attendance");
        $count = $count_result->fetch_assoc()['total'];
        echo "<p><strong>عدد سجلات الحضور:</strong> $count</p>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ جدول employee_attendance غير موجود</p>";
        
        if (isset($_POST['create_table'])) {
            // إنشاء جدول حضور الموظفين
            $sql = "CREATE TABLE employee_attendance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                employee_id VARCHAR(50) NOT NULL,
                employee_name VARCHAR(255) NOT NULL,
                attendance_date DATE NOT NULL,
                check_in_time TIME DEFAULT NULL,
                check_out_time TIME DEFAULT NULL,
                status ENUM('حاضر', 'غائب', 'متأخر', 'إجازة', 'عطلة', 'انصراف مبكر') DEFAULT 'غائب',
                notes TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_attendance (employee_id, attendance_date),
                INDEX idx_date (attendance_date),
                INDEX idx_employee (employee_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci";
            
            if ($con->query($sql)) {
                echo "<p style='color: green;'>✅ تم إنشاء جدول employee_attendance بنجاح</p>";
                echo "<script>location.reload();</script>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إنشاء الجدول: " . $con->error . "</p>";
            }
        }
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='create_table' style='background: blue; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px;'>إنشاء جدول حضور الموظفين</button>";
        echo "</form>";
    }
    
    // إضافة بيانات تجريبية
    if (isset($_POST['add_sample_data'])) {
        // جلب الموظفين
        $employees_result = $con->query("SELECT * FROM employ_tb LIMIT 5");
        if ($employees_result && $employees_result->num_rows > 0) {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            
            while ($employee = $employees_result->fetch_assoc()) {
                // تحديد العمود المناسب للمعرف
                $emp_id = $employee['id'] ?? $employee['emp_id'] ?? $employee['f_name'];
                $emp_name = $employee['f_name'];
                
                // إضافة حضور اليوم
                $status_today = ['حاضر', 'متأخر', 'غائب'][rand(0, 2)];
                $time_today = '08:' . str_pad(rand(0, 59), 2, '0', STR_PAD_LEFT);
                
                $stmt = $con->prepare("INSERT IGNORE INTO employee_attendance (employee_id, employee_name, attendance_date, check_in_time, status) VALUES (?, ?, ?, ?, ?)");
                $stmt->bind_param("sssss", $emp_id, $emp_name, $today, $time_today, $status_today);
                $stmt->execute();
                
                // إضافة حضور أمس
                $status_yesterday = ['حاضر', 'متأخر', 'غائب'][rand(0, 2)];
                $time_yesterday = '08:' . str_pad(rand(0, 59), 2, '0', STR_PAD_LEFT);
                
                $stmt = $con->prepare("INSERT IGNORE INTO employee_attendance (employee_id, employee_name, attendance_date, check_in_time, status) VALUES (?, ?, ?, ?, ?)");
                $stmt->bind_param("sssss", $emp_id, $emp_name, $yesterday, $time_yesterday, $status_yesterday);
                $stmt->execute();
            }
            
            echo "<p style='color: green;'>✅ تم إضافة بيانات تجريبية للحضور</p>";
            echo "<script>setTimeout(() => location.reload(), 1000);</script>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد موظفين في جدول employ_tb</p>";
        }
    }
    
    if ($check_table && $check_table->num_rows > 0) {
        echo "<hr>";
        echo "<form method='POST'>";
        echo "<button type='submit' name='add_sample_data' style='background: green; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px;'>إضافة بيانات تجريبية</button>";
        echo "</form>";
        
        // عرض عينة من البيانات
        $sample_data = $con->query("SELECT * FROM employee_attendance ORDER BY attendance_date DESC, employee_name LIMIT 10");
        if ($sample_data && $sample_data->num_rows > 0) {
            echo "<h3>عينة من سجلات الحضور:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>اسم الموظف</th><th>التاريخ</th><th>وقت الحضور</th><th>الحالة</th><th>ملاحظات</th></tr>";
            while ($record = $sample_data->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['employee_name']) . "</td>";
                echo "<td>" . $record['attendance_date'] . "</td>";
                echo "<td>" . ($record['check_in_time'] ?: 'لم يسجل') . "</td>";
                echo "<td>" . $record['status'] . "</td>";
                echo "<td>" . htmlspecialchars($record['notes'] ?: 'لا توجد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    $con->close();
} else {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات</p>";
}

echo "<hr>";
echo "<p><a href='employee_attendance.php'>🔗 الذهاب إلى صفحة حضور الموظفين</a></p>";
echo "<p><a href='home.php'>🏠 العودة إلى الصفحة الرئيسية</a></p>";
?>
