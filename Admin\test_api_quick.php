<?php
// اختبار سريع لـ API
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار سريع لـ API حضور الطلاب</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .test-box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    button:hover { background: #0056b3; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow: auto; }
</style>";

// اختبار 1: فحص وجود الملف
echo "<div class='test-box'>";
echo "<h3>1. فحص وجود ملف API:</h3>";
if (file_exists('student_attendance_data.php')) {
    echo "<p class='success'>✅ ملف student_attendance_data.php موجود</p>";
} else {
    echo "<p class='error'>❌ ملف student_attendance_data.php غير موجود</p>";
}
echo "</div>";

// اختبار 2: فحص قاعدة البيانات
echo "<div class='test-box'>";
echo "<h3>2. فحص قاعدة البيانات:</h3>";
include "addon/dbcon.php";
if ($con) {
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    $tables_check = [
        'stud_tb' => 'جدول الطلاب',
        'stat' => 'جدول الحضور',
        'users_tb' => 'جدول المستخدمين'
    ];
    
    foreach ($tables_check as $table => $desc) {
        $result = $con->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $count = $con->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p class='success'>✅ $desc ($table): $count سجل</p>";
        } else {
            echo "<p class='error'>❌ $desc ($table): غير موجود</p>";
        }
    }
} else {
    echo "<p class='error'>❌ فشل الاتصال بقاعدة البيانات</p>";
}
echo "</div>";

// اختبار 3: اختبار API مباشرة
echo "<div class='test-box'>";
echo "<h3>3. اختبار API مباشرة:</h3>";

$test_date = date('Y-m-d');
echo "<p><strong>تاريخ الاختبار:</strong> $test_date</p>";

echo "<button onclick='testAPI()'>اختبار API</button>";
echo "<button onclick='testAPIWithCategory()'>اختبار مع صف محدد</button>";
echo "<div id='api-result'></div>";

echo "<script>
function testAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    const url = 'student_attendance_data.php?date=$test_date';
    console.log('Testing URL:', url);
    
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            
            try {
                const data = JSON.parse(text);
                console.log('Parsed data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = '<p style=\"color: green;\">✅ API يعمل بنجاح!</p>' +
                        '<p><strong>عدد الطلاب:</strong> ' + (data.students ? data.students.length : 0) + '</p>' +
                        '<p><strong>إجمالي في قاعدة البيانات:</strong> ' + (data.total_count || 0) + '</p>' +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في API: ' + (data.error || 'خطأ غير معروف') + '</p>' +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            } catch (e) {
                console.error('JSON parse error:', e);
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في تحليل JSON: ' + e.message + '</p>' +
                    '<p><strong>الاستجابة الخام:</strong></p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في الشبكة: ' + error.message + '</p>';
        });
}

function testAPIWithCategory() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<p>جاري اختبار مع صف محدد...</p>';
    
    const url = 'student_attendance_data.php?date=$test_date&category=صف الروضة';
    console.log('Testing URL with category:', url);
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                
                if (data.success) {
                    resultDiv.innerHTML = '<p style=\"color: green;\">✅ API مع الصف يعمل بنجاح!</p>' +
                        '<p><strong>الصف المحدد:</strong> صف الروضة</p>' +
                        '<p><strong>عدد الطلاب:</strong> ' + (data.students ? data.students.length : 0) + '</p>' +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ: ' + (data.error || 'خطأ غير معروف') + '</p>';
                }
            } catch (e) {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في JSON: ' + e.message + '</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ: ' + error.message + '</p>';
        });
}
</script>";
echo "</div>";

// اختبار 4: فحص الصفوف الموجودة
echo "<div class='test-box'>";
echo "<h3>4. فحص الصفوف الموجودة في قاعدة البيانات:</h3>";

if ($con) {
    $categories_query = "SELECT DISTINCT catg FROM stud_tb WHERE catg IS NOT NULL AND catg != '' ORDER BY catg";
    $categories_result = $con->query($categories_query);
    
    if ($categories_result && $categories_result->num_rows > 0) {
        echo "<p class='info'>الصفوف الموجودة في قاعدة البيانات:</p>";
        echo "<ul>";
        while ($row = $categories_result->fetch_assoc()) {
            $count = $con->query("SELECT COUNT(*) as count FROM stud_tb WHERE catg = '" . $row['catg'] . "'")->fetch_assoc()['count'];
            echo "<li><strong>" . htmlspecialchars($row['catg']) . "</strong> ($count طالب)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='error'>❌ لا توجد صفوف في قاعدة البيانات</p>";
    }
}
echo "</div>";

echo "<div class='test-box info'>";
echo "<h3>الروابط المفيدة:</h3>";
echo "<ul>";
echo "<li><a href='attandec.php' target='_blank'>الصفحة الرئيسية المحدثة</a></li>";
echo "<li><a href='student_attendance_data.php?date=$test_date' target='_blank'>API مباشرة</a></li>";
echo "<li><a href='fix_attendance.php' target='_blank'>ملف الإصلاح الشامل</a></li>";
echo "</ul>";
echo "</div>";

if ($con) {
    $con->close();
}
?>
