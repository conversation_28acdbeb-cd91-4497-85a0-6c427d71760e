<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || ($_SESSION['user']->role !== "Admin" && $_SESSION['user']->role !== "Mod")) {
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بالوصول']);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

try {
    $role = isset($_GET['role']) ? $_GET['role'] : $_SESSION['user']->role;
    $detailed = isset($_GET['detailed']) && $_GET['detailed'] === 'true';
    
    // إنشاء جدول الإشعارات إذا لم يكن موجوداً
    $create_table_sql = "
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type VARCHAR(50) DEFAULT 'info',
            target_role VARCHAR(20) DEFAULT 'Admin',
            related_id INT DEFAULT NULL,
            related_type VARCHAR(50) DEFAULT NULL,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $con->query($create_table_sql);
    
    if ($detailed) {
        // جلب الإشعارات التفصيلية
        $sql = "SELECT * FROM notifications 
                WHERE target_role IN ('$role', 'All') 
                ORDER BY created_at DESC 
                LIMIT 50";
        $result = $con->query($sql);
        
        $notifications = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $notifications[] = [
                    'id' => $row['id'],
                    'title' => $row['title'],
                    'message' => $row['message'],
                    'type' => $row['type'],
                    'is_read' => $row['is_read'],
                    'created_at' => date('d/m/Y H:i', strtotime($row['created_at'])),
                    'related_id' => $row['related_id'],
                    'related_type' => $row['related_type']
                ];
            }
        }
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications
        ]);
    } else {
        // جلب عدد الإشعارات غير المقروءة فقط
        $sql = "SELECT COUNT(*) as unread_count FROM notifications 
                WHERE target_role IN ('$role', 'All') AND is_read = 0";
        $result = $con->query($sql);
        
        $unread_count = 0;
        if ($result) {
            $row = $result->fetch_assoc();
            $unread_count = $row['unread_count'];
        }
        
        echo json_encode([
            'success' => true,
            'unread_count' => $unread_count
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في جلب الإشعارات: ' . $e->getMessage()
    ]);
}

$con->close();
?>
