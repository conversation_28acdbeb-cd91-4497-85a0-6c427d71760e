<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>  الطلاب منتهي الاشتراك </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">


    
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <div style="margin: 20px; text-align: center;">
        <h2>الطلاب منتهي الاشتراك</h2>
        <button class="btn btn-primary text-light" onclick="printExpiredStudentsTable()">طباعة</button>
    </div>
    <table class="table" id="Table">
  <thead>
    <tr>
    <th scope="col">العمليات</th>
      <th scope="col"> مستخدم الحضانة  </th>
      <th scope="col"> الايام المتبقية </th>
      <th scope="col">حالة الاشتراك</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ النفاذ </th>
      <th scope="col">تاريخ الاشتراك</th>
      <th scope="col">رقم ولي الامر</th>
      <th scope="col">اسم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">السكن</th>
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
      <th scope="col">اسم الطالب  </th>
      <th scope="col">رقم  الوصل </th>
      
    </tr>
  </thead>
  <tbody id="myTable">
    <?php
    $datenow=date('Y-m-d');
      $sql="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp)<='$datenow' AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.id_user={$_SESSION['user']->id_user}";
      $result=mysqli_query($con,$sql);
      if($result){
       while($row=mysqli_fetch_assoc($result)) {
          $id=$row['id'];
          $id_pay=$row['id_pay'];
          $id_note=$row['id_note'];
          $name=$row['name'];
          $age=$row['age'];
          $sex=$row['sex'];
          $catg=$row['catg'];
          $datein=$row['datein'];
          $p_name=$row['p_name'];
          $p_phone=$row['p_phone'];
          $loc=$row['loc'];
          $date_exp=$row['date_exp'];
          $cash_stud=number_format($row['cash_stud']);
          $user_name=$row['user_name'];
          $id_pay=$row['id_pay'];
          $date_in=strtotime(date('y-m-d'));
          $date_out=strtotime($date_exp);
          $stat=$date_out-$date_in;
          $cek=floor($stat/(60*60*24));
          if($cek<=0){
            $mes='<h3 class=exp>منتهي</h3>';
        }elseif($cek<=10 & $cek>0){
            $mes='<h3 class=soon >قريبا</h3>';
         }else{
            $mes='<h3 class=still >فعال</h3>';
        }

         ?><tr id="tr_<?php echo $id ?>">
          <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" name="update" > <a style="text-decoration: none;color:whitesmoke;" href="editstud.php?id=<?php echo $id?>">تعديل</a>  </button> 
          <button type="button" class="btn btn-secondary mb-1" id="renew_btn" name="remove"><a style="text-decoration: none;color:whitesmoke;" href="renew.php?renewId=<?php echo $id?>">تجديد </a> </button>
          <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove" onclick="deletdata(<?php echo $id ?>)">حذف </button></td>
          <td> <?php echo $user_name?> </td>
          <td> <?php echo $cek?> </td>
          <td> <?php echo $mes?> </td>
          <td> IQD <?php echo $cash_stud?>  </td>
          <td><?php echo $date_exp?>  </td>
          <td><?php echo $datein?></td>
          <td><?php echo $p_phone?></td>
          <td><?php echo $p_name?></td>
          <td><?php echo $catg?></td>
          <td><?php echo $loc?>  </td>
          <td><?php echo $sex?></td>
          <td><?php echo $age?></td>
          <td><?php echo $name?></td>
          <td><?php echo $id_pay?></td>
          
          </tr>
          <?php
         
         }
      }
  

    ?>
   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
    <script>
      function deletdata(id){
        $("#deletmodle").addClass("active");
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
          $.ajax({url:'addon/reomves.php',
          method:"POST",
          data:({removeid:id}),
          success:function(response){
            console.log(id)
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          setInterval(function () {
            window.location.reload();
           },2000);
         
        }
        });
        });
        }
    </script>
   </body>
   <script>
  $(document).ready(function () {
    $("#Table").DataTable();
  });
</script>

<script>
function printExpiredStudentsTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول وإزالة عمود العمليات
    var table = document.getElementById('Table').cloneNode(true);

    // إزالة عمود العمليات من الرأس والصفوف
    var rows = table.querySelectorAll('tr');
    rows.forEach(function(row) {
        var cells = row.querySelectorAll('th, td');
        // إزالة العمود الأول (العمليات) إذا وجد
        if (cells.length > 0 && (cells[0].textContent.includes('العمليات') || cells[0].querySelector('button') || cells[0].querySelector('a'))) {
            cells[0].remove();
        }
    });

    var tableContent = table.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير الطلاب منتهي الاشتراك</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 10px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 6px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .still {
                color: green;
                font-weight: bold;
            }

            .exp {
                color: red;
                font-weight: bold;
            }

            .soon {
                color: orange;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير الطلاب منتهي الاشتراك</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tableContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>

</html>