<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        // مصرح له
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
    }
}else{
    header("location:http://localhost/roda/login.php",true);
    die("");
}

include "dbcon.php";

// التحقق من البيانات المرسلة سواء POST أو GET
$user_id = 0;
if(isset($_POST['id']) && $_POST['id'] != '0'){
    $user_id = intval($_POST['id']);
    // echo "<!-- POST ID: $user_id -->"; // للتشخيص
} elseif(isset($_GET['id']) && $_GET['id'] != '0'){
    $user_id = intval($_GET['id']);
    // echo "<!-- GET ID: $user_id -->"; // للتشخيص
}

if($user_id > 0){
    $search = $user_id;

    $sql="SELECT * FROM depit_tb,users_tb WHERE depit_tb.userID=users_tb.id_user AND users_tb.id_user=$search ORDER BY depit_date DESC";
    $result=mysqli_query($con,$sql);

    $total_expenses = 0; // متغير لحساب المجموع

    if(mysqli_num_rows($result)>0){
        while($row=mysqli_fetch_assoc($result)) {
            $id=$row['id'];
            $depit_note=$row['depit_note'];
            $depit_cash_raw=$row['depit_cash']; // القيمة الخام للحساب
            $depit_cash=number_format($row['depit_cash']); // القيمة المنسقة للعرض
            $depit_date=$row['depit_date'];
            $user_name=$row['user_name'];

            $total_expenses += $depit_cash_raw; // إضافة للمجموع

            echo '<tr id="tr_'.$id.'">
                <td>
                    <button type="button" class="btn btn-secondary mb-1">
                        <a href="edit_depit.php?id='.$id.'" style="text-decoration: none;color:white;">تعديل</a>
                    </button>
                    <button type="button" class="btn btn-danger mb-1" onclick="deletdata('.$id.')">حذف</button>
                </td>
                <td>'.$user_name.'</td>
                <td>'.$depit_date.'</td>
                <td>IQD '.$depit_cash.'</td>
                <td>'.$depit_note.'</td>
            </tr>';
        }

        // إضافة صف المجموع الكلي
        echo '<tr style="background-color: #f8f9fa; font-weight: bold; border-top: 3px solid #007bff;">
            <td colspan="3" style="text-align: center; font-size: 16px; color: #007bff;">
                <i class="fas fa-calculator"></i> المجموع الكلي للمصاريف
            </td>
            <td style="font-size: 18px; color: #28a745;">IQD '.number_format($total_expenses).'</td>
            <td style="text-align: center; color: #6c757d;">إجمالي جميع المصاريف</td>
        </tr>';
    }else{
        echo "<tr><td colspan='5' style='font-size: 20px; text-align: center; padding: 30px; color: #6c757d;'>
            <i class='fas fa-receipt' style='font-size: 40px; margin-bottom: 15px;'></i><br>
            لا توجد مصروفات لهذا المستخدم
        </td></tr>";
    }
}else{
    echo "<tr><td colspan='5' style='font-size: 18px; text-align: center; padding: 30px; color: #6c757d;'>
        <i class='fas fa-user-check' style='font-size: 40px; margin-bottom: 15px;'></i><br>
        يرجى اختيار مستخدم لعرض المصاريف
    </td></tr>";
}
?>