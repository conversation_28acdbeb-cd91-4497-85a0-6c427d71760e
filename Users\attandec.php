<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "User") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>حضور الطلاب</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
  <script src="js/all.min.js"></script>
  <link rel="icon" href="css/icon.ico">
  <script src="js/jquery.min.js"></script>
  <script src="js/jquery.dataTables.min.js"></script>
  <link rel="stylesheet" href="css/jquery.dataTables.min.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>
  <?php include "addon/dbcon.php" ?>
</head>

<body id="td">
<div class="search">
    <button class="btn btn-secondary text-light ml-10" id="show" type="submit"> عرض حضور اليوم</button> 
  </div>

  <form action="" method="POST">
    <div class="search">

      <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected disabled>اختر صنف تدريس </option>
        <option value="روضة" <?php echo (isset($_POST['users']) && $_POST['users'] == 'روضة') ? 'selected' : ''; ?>>روضة</option>
        <option value="حضانة" <?php echo (isset($_POST['users']) && $_POST['users'] == 'حضانة') ? 'selected' : ''; ?>>حضانة</option>
        <option value="تمهيدي" <?php echo (isset($_POST['users']) && $_POST['users'] == 'تمهيدي') ? 'selected' : ''; ?>>تمهيدي</option>
        <option value="تحضيري" <?php echo (isset($_POST['users']) && $_POST['users'] == 'تحضيري') ? 'selected' : ''; ?>>تحضيري</option>
      </select>

      <button name="show" type="submit" class="btn btn-secondary text-light">اظهار</button>
      <button type="button" class="btn btn-primary text-light" onclick="printAttendanceTable()">طباعة</button>
     
     

    </div>
  </form>
  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-circle-info"></i>
      </div>
      <div class="container-22">
        <p class="p1">Done !</p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>

  <table class="table" id="Table">
    <thead>
      <tr>
        <th scope="col"> عمليات الحضور </th>
        <th scope="col"> عمليات الغياب </th>
        <th scope="col"> حالة الاشتراك </th>
        <th scope="col"> مستخدم الحضانة </th>
        <th scope="col">صنف التسجيل</th>
        <th scope="col"> التاريخ  </th>
        <th scope="col">اسم الطالب </th>

      </tr>
    </thead>

    <tbody id="myTable">

<?php
$datenow = date('Y-m-d');

if (isset($_POST['users'])) {
  $cagtselc = $_POST['users'];
  
  // استعلام محدث ليجيب كل الطلاب بغض النظر عن حالة الاشتراك
  $sql = "SELECT stud_pay.date_exp, stud_tb.name, stud_tb.catg, stud_tb.p_name, users_tb.user_name, stud_tb.id 
          FROM stud_tb 
          LEFT JOIN users_tb ON stud_tb.userID = users_tb.id_user 
          LEFT JOIN stud_pay ON stud_pay.id_stud = stud_tb.id 
          WHERE stud_tb.catg = '$cagtselc' 
          AND users_tb.id_user = {$_SESSION['user']->id_user}
          ORDER BY stud_tb.name";
          
  $result = mysqli_query($con, $sql);
  
  if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
      $id = $row['id'];
      
      // التحقق من وجود سجل حضور اليوم
      $today_attendance_check = "SELECT * FROM stat WHERE data_stat = '$datenow' AND id_stud = '$id'";
      $attendance_result = mysqli_query($con, $today_attendance_check);
      $already_marked = mysqli_num_rows($attendance_result) > 0;
      
      // التحقق من حالة الاشتراك
      $subscription_status = "غير محدد";
      $status_color = "#6c757d"; // رمادي افتراضي
      
      if ($row['date_exp']) {
        $expiry_date = new DateTime($row['date_exp']);
        $current_date = new DateTime();
        
        if ($expiry_date > $current_date) {
          $subscription_status = "نشط";
          $status_color = "#28a745"; // أخضر
        } else {
          $subscription_status = "منتهي";
          $status_color = "#dc3545"; // أحمر
        }
      }
      
      // تحديد لون الصف بناءً على حالة الحضور اليوم
      $row_style = "";
      $checkbox_disabled = "";
      if ($already_marked) {
        $attendance_data = mysqli_fetch_assoc($attendance_result);
        if ($attendance_data['stat_stud'] == 'حاضر') {
          $row_style = "background-color: #54ff0047;";
        } else {
          $row_style = "background-color: #dc354547;";
        }
        $checkbox_disabled = "disabled";
      }
?>

      <tr id="tr_<?php echo $id ?>" class="sm" style="<?php echo $row_style; ?>" value="حاضر">
        <td>
          <form action="" id="frm">
            <div class="attandes">
              <input type="checkbox" name="selc[]" id="attend_<?php echo $id ?>" value="حاضر" <?php echo $checkbox_disabled; ?>>
              <label for="">حاضر</label>
              <?php if ($already_marked && $attendance_data['stat_stud'] == 'حاضر'): ?>
                <small class="text-success">✓ مسجل</small>
              <?php endif; ?>
            </div>
          </form>
        </td>
        <td>
          <form action="" id="frm2">
            <div class="attandes">
              <input type="checkbox" name="selc_absent[]" id="absent_<?php echo $id ?>" value="غائب" <?php echo $checkbox_disabled; ?>>
              <label for="">غائب</label>
              <?php if ($already_marked && $attendance_data['stat_stud'] == 'غائب'): ?>
                <small class="text-danger">✓ مسجل</small>
              <?php endif; ?>
            </div>
          </form>
        </td>
        <td>
          <span class="badge" style="background-color: <?php echo $status_color; ?>; color: white; padding: 5px 10px; border-radius: 15px;">
            <?php echo $subscription_status; ?>
          </span>
          <?php if ($subscription_status != "غير محدد"): ?>
            <br><small style="color: #6c757d;">انتهاء: <?php echo date('Y-m-d', strtotime($row['date_exp'])); ?></small>
          <?php endif; ?>
        </td>
        <td> <?php echo $row['user_name'] ?> </td>
        <td><?php echo $row['catg'] ?></td>
        <td><?php echo $datenow ?></td>
        <td><?php echo $row['name']  ?></td>
      </tr>

<?php
    }
  }
}
?>

</tbody>
</table>
<script>
 // تسجيل الحضور
 $('input[name="selc[]"]').click(function(){
    if($(this).is(':checked')) 
    {
      var x = this.id.replace('attend_', '');
      var y = $(this).val();
      // إلغاء تحديد الغياب إذا تم تحديد الحضور
      $('#absent_' + x).prop('checked', false);
      sent(x, y);
    }
    else 
    {
      let x = 0;
      return x;
    }
  });

  // تسجيل الغياب
  $('input[name="selc_absent[]"]').click(function(){
    if($(this).is(':checked') && !$(this).prop('disabled')) 
    {
      var x = this.id.replace('absent_', '');
      var y = $(this).val();
      // إلغاء تحديد الحضور إذا تم تحديد الغياب
      $('#attend_' + x).prop('checked', false);
      sent(x, y);
    }
    else 
    {
      let x = 0;
      return x;
    }
  });

  function sent(x,y){
    console.log(x,y)
    $.ajax({
                method: "post",
                url: "addon/code.php",
                data: {
                    id:x,
                    stat:y
                },
                success: function (data) {
                    console.log(data)
                    if(data==1){
                        StudToast('8px solid rgb(247, 167, 22)','#f7a716'," ! انتبه ","   الطالب قد تم تسجيله لهذا اليوم ","fa-solid fa-circle-info")
                        jQuery("#tr_"+x).css("background","#ff4e0069");
                        // تعطيل الـ checkboxes بدلاً من إخفاء الصف
                        jQuery("#attend_"+x).prop('disabled', true);
                        jQuery("#absent_"+x).prop('disabled', true);
                    }else{
                        if(y === 'حاضر') {
                            StudToast('8px solid rgb(3, 188, 77)','rgb(3, 188, 77)'," ! تمت ","   تم تسجيل الحضور بنجاح ","fa fa-circle-check")
                            jQuery("#tr_"+x).css("background","#54ff0047");
                            jQuery("#attend_"+x).after('<small class="text-success">✓ مسجل</small>');
                        } else {
                            StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! تمت ","   تم تسجيل الغياب بنجاح ","fa fa-circle-check")
                            jQuery("#tr_"+x).css("background","#dc354547");
                            jQuery("#absent_"+x).after('<small class="text-danger">✓ مسجل</small>');
                        }
                        // تعطيل الـ checkboxes بعد التسجيل
                        jQuery("#attend_"+x).prop('disabled', true);
                        jQuery("#absent_"+x).prop('disabled', true);
                    }
                    
                }
            });
  }
</script>
<script>
  $("#show").click(()=>{
    console.log("clicked")
    location.href="attandec_in.php";
  })
</script>

<script>
  $(document).ready(function() {
    $("#Table").DataTable();
  });
</script>
<script>
  let x;
  let toast = document.getElementById("toast2");
  p1 = document.querySelector(".p1");
  p2 = document.querySelector(".p2");

  function StudToast(ts, ic, tx1, tx2, icC) {
    let icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className = icC;
    toast.style.borderRight = ts;
    icon.style.color = ic;
    p1.innerText = tx1;
    p2.innerText = tx2;
    toast.style.transition = '1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition = '1s';
    x = setTimeout(() => {
      toast.style.transform = "translateX(-500px)"
    }, 4200);
  }
</script>

<script>
function printAttendanceTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول وإزالة عمود العمليات
    var table = document.getElementById('Table');
    if (!table) {
        alert('لا توجد بيانات للطباعة');
        return;
    }

    var tableClone = table.cloneNode(true);

    // إزالة عمود العمليات من الرأس والصفوف
    var rows = tableClone.querySelectorAll('tr');
    rows.forEach(function(row) {
        var cells = row.querySelectorAll('th, td');
        // إزالة العمود الأول (العمليات) إذا وجد
        if (cells.length > 0 && (cells[0].textContent.includes('العمليات') || cells[0].querySelector('button') || cells[0].querySelector('a'))) {
            cells[0].remove();
        }
    });

    var tableContent = tableClone.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير حضور الطلاب</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .attendance-present {
                color: green;
                font-weight: bold;
            }

            .attendance-absent {
                color: red;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير حضور الطلاب</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tableContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>

</body>
</html>