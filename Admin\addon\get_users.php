<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo '<option value="0">غير مصرح</option>';
    exit();
}

include "dbcon.php";

// التحقق من الاتصال
if (!$con) {
    echo '<option value="0">خطأ في الاتصال</option>';
    exit();
}

// جلب المستخدمين
$users_query = "SELECT DISTINCT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = mysqli_query($con, $users_query);

$options = '';
if ($users_result && mysqli_num_rows($users_result) > 0) {
    while ($user = mysqli_fetch_assoc($users_result)) {
        $options .= '<option value="' . htmlspecialchars($user['id_user']) . '">' . htmlspecialchars($user['user_name']) . '</option>';
    }
} else {
    $options = '<option value="0">لا توجد مستخدمين</option>';
}

echo $options;

mysqli_close($con);
?>
