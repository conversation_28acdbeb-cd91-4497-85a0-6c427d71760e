<!-- مكون الإشعارات -->
<div class="notifications-container">
    <!-- زر الإشعارات -->
    <div class="notification-bell" onclick="toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
    </div>
    
    <!-- قائمة الإشعارات -->
    <div class="notifications-dropdown" id="notificationsDropdown">
        <div class="notifications-header">
            <h3>الإشعارات</h3>
            <button class="mark-all-read-btn" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i>
                تحديد الكل كمقروء
            </button>
        </div>
        
        <div class="notifications-list" id="notificationsList">
            <div class="loading-notifications">
                <i class="fas fa-spinner fa-spin"></i>
                جاري التحميل...
            </div>
        </div>
        
        <div class="notifications-footer">
            <button class="load-more-btn" onclick="loadMoreNotifications()" id="loadMoreBtn" style="display: none;">
                تحميل المزيد
            </button>
        </div>
    </div>
</div>

<style>
.notifications-container {
    position: relative;
    display: inline-block;
    z-index: 99999;
}

.notification-bell {
    position: relative;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    color: white;
    font-size: 1.2rem;
}

.notification-bell:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.notifications-dropdown {
    position: fixed;
    top: 70px;
    left: 50px;
    width: 350px;
    max-height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 999999;
    display: none;
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

.notifications-dropdown.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.notifications-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.mark-all-read-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mark-all-read-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.notifications-list {
    max-height: 350px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 8px;
    height: 8px;
    background: #2196f3;
    border-radius: 50%;
    transform: translateY(-50%);
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-message {
    color: #666;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 5px;
}

.notification-time {
    color: #999;
    font-size: 0.7rem;
}

.notification-type-icon {
    display: inline-block;
    margin-left: 5px;
    width: 20px;
    text-align: center;
}

.type-leave_request { color: #ff9800; }
.type-need_request { color: #4caf50; }
.type-leave_response { color: #2196f3; }
.type-need_response { color: #9c27b0; }
.type-general { color: #607d8b; }

.loading-notifications {
    text-align: center;
    padding: 30px;
    color: #666;
}

.empty-notifications {
    text-align: center;
    padding: 30px;
    color: #999;
}

.empty-notifications i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.notifications-footer {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.load-more-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .notifications-dropdown {
        width: 300px;
        left: 10px;
        top: 60px;
    }

    .notification-item {
        padding: 12px 15px;
    }

    .notifications-header {
        padding: 12px 15px;
    }
}

/* إضافة أنماط للتأكد من الظهور الصحيح */
.notifications-wrapper {
    position: relative !important;
}

.notification-bell {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #007bff !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

.notification-bell:hover {
    background: #007bff !important;
    color: white !important;
}

/* تحسين عرض القائمة المنسدلة */
.notifications-dropdown.show {
    display: block !important;
    animation: slideDown 0.3s ease !important;
}

/* التأكد من عدم تداخل العناصر */
body {
    position: relative;
}

nav {
    position: relative;
    z-index: 1000;
}
</style>

<script>
let notificationsOpen = false;
let notificationsOffset = 0;
const notificationsLimit = 10;

// تحميل عدد الإشعارات غير المقروءة
function loadNotificationCount() {
    fetch('notifications_api.php?action=get_count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => console.error('Error:', error));
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadge');
    if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'block';
    } else {
        badge.style.display = 'none';
    }
}

// تبديل عرض الإشعارات
function toggleNotifications() {
    const dropdown = document.getElementById('notificationsDropdown');
    
    if (!notificationsOpen) {
        dropdown.classList.add('show');
        notificationsOpen = true;
        loadNotifications(true);
    } else {
        dropdown.classList.remove('show');
        notificationsOpen = false;
    }
}

// تحميل الإشعارات
function loadNotifications(reset = false) {
    if (reset) {
        notificationsOffset = 0;
        document.getElementById('notificationsList').innerHTML = '<div class="loading-notifications"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    }
    
    fetch(`notifications_api.php?action=get_notifications&limit=${notificationsLimit}&offset=${notificationsOffset}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications, reset);
                notificationsOffset += notificationsLimit;
                
                // إظهار/إخفاء زر تحميل المزيد
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (data.notifications.length === notificationsLimit) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error:', error));
}

// عرض الإشعارات
function displayNotifications(notifications, reset = false) {
    const container = document.getElementById('notificationsList');
    
    if (reset) {
        container.innerHTML = '';
    }
    
    if (notifications.length === 0 && reset) {
        container.innerHTML = `
            <div class="empty-notifications">
                <i class="fas fa-bell-slash"></i>
                <div>لا توجد إشعارات</div>
            </div>
        `;
        return;
    }
    
    notifications.forEach(notification => {
        const item = createNotificationItem(notification);
        container.appendChild(item);
    });
}

// إنشاء عنصر إشعار
function createNotificationItem(notification) {
    const item = document.createElement('div');
    item.className = `notification-item ${notification.is_read == 0 ? 'unread' : ''}`;
    item.onclick = () => markNotificationAsRead(notification.id);
    
    const typeIcons = {
        'leave_request': 'fa-calendar-alt',
        'need_request': 'fa-clipboard-list',
        'leave_response': 'fa-reply',
        'need_response': 'fa-check-circle',
        'general': 'fa-info-circle'
    };
    
    item.innerHTML = `
        <div class="notification-title">
            <i class="fas ${typeIcons[notification.type]} notification-type-icon type-${notification.type}"></i>
            ${notification.title}
        </div>
        <div class="notification-message">${notification.message}</div>
        <div class="notification-time">${notification.time_ago}</div>
    `;
    
    return item;
}

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId) {
    const formData = new FormData();
    formData.append('notification_id', notificationId);
    
    fetch('notifications_api.php?action=mark_read', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            // تحديث العنصر في الواجهة
            const items = document.querySelectorAll('.notification-item');
            items.forEach(item => {
                if (item.onclick.toString().includes(notificationId)) {
                    item.classList.remove('unread');
                }
            });
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('notifications_api.php?action=mark_all_read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            loadNotifications(true);
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحميل المزيد من الإشعارات
function loadMoreNotifications() {
    loadNotifications(false);
}

// إغلاق الإشعارات عند النقر خارجها
document.addEventListener('click', function(event) {
    const container = document.querySelector('.notifications-container');
    if (!container.contains(event.target) && notificationsOpen) {
        document.getElementById('notificationsDropdown').classList.remove('show');
        notificationsOpen = false;
    }
});

// تحميل عدد الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadNotificationCount();
    
    // تحديث العدد كل 30 ثانية
    setInterval(loadNotificationCount, 30000);
});
</script>
