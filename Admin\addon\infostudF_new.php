<?php
session_start();

// Include database connection
include "dbcon.php";

// Check session and user role
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo "<td colspan=17 style='text-align: center; padding: 30px; color: #dc3545;'>";
    echo "غير مصرح لك بالوصول";
    echo "</td>";
    exit();
}

// Check if the id parameter is set
if (!isset($_POST['id']) || $_POST['id'] == '0' || empty($_POST['id'])) {
    echo "<td colspan=17 style='font-size: 25px; text-align: center; padding: 50px;'>";
    echo "يرجى اختيار مستخدم من القائمة أعلاه لعرض بيانات الطلاب";
    echo "</td>";
    exit();
}

$search = intval($_POST['id']); // تحويل إلى رقم صحيح للأمان

try {
    // حساب الإحصائيات أولاً
    $datenow = date('Y-m-d');
    
    $sql_stats = "SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN p.date_exp IS NOT NULL AND DATEDIFF(p.date_exp, '$datenow') > 10 THEN 1 ELSE 0 END) as active_count,
                    SUM(CASE WHEN p.date_exp IS NOT NULL AND DATEDIFF(p.date_exp, '$datenow') <= 0 THEN 1 ELSE 0 END) as expired_count,
                    SUM(CASE WHEN p.date_exp IS NOT NULL AND DATEDIFF(p.date_exp, '$datenow') BETWEEN 1 AND 10 THEN 1 ELSE 0 END) as soon_count,
                    SUM(CASE WHEN DATE_FORMAT(s.datein, '%Y-%m') = '" . date('Y-m') . "' THEN 1 ELSE 0 END) as new_count
                  FROM stud_tb s
                  LEFT JOIN stud_pay p ON s.id = p.id_stud
                  WHERE s.userID = $search";
    
    $stats_result = mysqli_query($con, $sql_stats);
    if ($stats_result) {
        $stats = mysqli_fetch_assoc($stats_result);
        $total_count = $stats['total_count'];
        $active_count = $stats['active_count'];
        $expired_count = $stats['expired_count'];
        $soon_count = $stats['soon_count'];
        $new_count = $stats['new_count'];
    } else {
        $total_count = $active_count = $expired_count = $soon_count = $new_count = 0;
    }

    // إرسال الإحصائيات إلى JavaScript
    $statistics = array(
        'active' => $active_count,
        'expired' => $expired_count,
        'soon' => $soon_count,
        'new' => $new_count,
        'total' => $total_count
    );
    
    echo "<script>updateStatistics(" . json_encode($statistics) . ");</script>";

    // الاستعلام الرئيسي لجلب بيانات الطلاب
    $sql = "SELECT s.*, 
                   COALESCE(p.cash_stud, 0) as cash_stud, 
                   COALESCE(p.date_exp, '') as date_exp, 
                   COALESCE(p.id_pay, '') as id_pay, 
                   COALESCE(u.user_name, '') as user_name,
                   COALESCE(s.health_status, 'لا توجد ملاحظات') as health_status,
                   COALESCE(s.registration_status, 'تسجيل جديد') as registration_status
            FROM stud_tb s
            LEFT JOIN stud_pay p ON p.id_stud = s.id 
            LEFT JOIN users_tb u ON s.userID = u.id_user 
            WHERE s.userID = $search 
            ORDER BY s.datein DESC";
    
    $result = mysqli_query($con, $sql);

    if (!$result) {
        throw new Exception("خطأ في الاستعلام: " . mysqli_error($con));
    }

    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // استخراج البيانات
            $id = $row['id'];
            $id_note = $row['id_note'];
            $name = $row['name'];
            $age = $row['age'];
            $sex = $row['sex'];
            $catg = $row['catg'];
            $datein = $row['datein'];
            $p_name = $row['p_name'] ?? '';
            $p_phone = $row['p_phone'];
            $loc = $row['loc'];
            $cash_stud = number_format($row['cash_stud']);
            $date_exp = $row['date_exp'];
            $user_name = $row['user_name'];
            $id_pay = $row['id_pay'];
            $health_status = $row['health_status'];
            $registration_status = $row['registration_status'];

            // حساب الأيام المتبقية وحالة الاشتراك
            $datenow = date('Y-m-d');
            if (!empty($date_exp)) {
                $days_remaining = (strtotime($date_exp) - strtotime($datenow)) / (60 * 60 * 24);
                $days_remaining = round($days_remaining);
                
                if ($days_remaining > 10) {
                    $regStatus = "فعال";
                    $classStatus = "text-success";
                } elseif ($days_remaining > 0) {
                    $regStatus = "قريب الانتهاء";
                    $classStatus = "text-warning";
                } else {
                    $regStatus = "منتهي";
                    $classStatus = "text-danger";
                }
            } else {
                $days_remaining = 0;
                $regStatus = "غير محدد";
                $classStatus = "text-muted";
            }

            // عرض البيانات
            echo '<tr>
                    <td>
                        <a href="editstud.php?id='.$id.'" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="deletstud.php?id='.$id.'" class="btn btn-danger btn-sm" onclick="return confirm(\'هل أنت متأكد من الحذف؟\')">
                            <i class="fas fa-trash"></i> حذف
                        </a>
                    </td>
                    <td>'.$days_remaining.' يوم</td>
                    <td><p class="'.$classStatus.'">'.$regStatus.'</p></td>
                    <td>'.$cash_stud.' IQD</td>
                    <td>'.$date_exp.'</td>
                    <td>'.$datein.'</td>
                    <td>'.$p_phone.'</td>
                    <td>'.$catg.'</td>
                    <td>'.$sex.'</td>
                    <td>'.$age.'</td>
                    <td>'.$loc.'</td>
                    <td>'.$name.'</td>
                    <td>'.$id_pay.'</td>
                    <td>'.((strlen($health_status) > 50) ? substr($health_status, 0, 50) . '...' : htmlspecialchars($health_status)).'</td>
                    <td><span class="badge '.($registration_status == 'تسجيل جديد' ? 'bg-primary' : 'bg-success').'">'.$registration_status.'</span></td>
                </tr>';
        }
    } else {
        echo "<td colspan=17 style='font-size: 20px; text-align: center; padding: 40px; color: #666;'>";
        echo "<i class='fas fa-user-times' style='font-size: 50px; color: #ffc107; margin-bottom: 20px;'></i><br>";
        echo "لا توجد بيانات طلاب لهذا المستخدم<br>";
        echo "<small style='font-size: 14px; color: #999;'>المستخدم المختار (ID: $search) لا يحتوي على أي طلاب مسجلين</small>";
        echo "</td>";
        echo "<script>reomtost()</script>";
    }

} catch (Exception $e) {
    echo "<td colspan=17 style='font-size: 18px; text-align: center; padding: 30px; color: #dc3545;'>";
    echo "<i class='fas fa-exclamation-triangle' style='font-size: 40px; margin-bottom: 15px;'></i><br>";
    echo "<strong>حدث خطأ في تحميل البيانات</strong><br>";
    echo "<small style='font-size: 14px; margin-top: 10px; display: block;'>";
    echo "تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "</small>";
    echo "</td>";
    echo "<script>reomtost()</script>";
}
?>
