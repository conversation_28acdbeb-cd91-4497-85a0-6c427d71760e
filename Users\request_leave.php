<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

$message = '';
$messageType = '';

if(isset($_POST['submit_leave'])){
    try {
        $user_id = $_SESSION['user']->id_user;
        $employee_name = trim($_POST['employee_name']);
        $leave_type = $_POST['leave_type'];
        $leave_details = trim($_POST['leave_details']);
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $request_date = date('Y-m-d');
        
        // حساب عدد الأيام
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $days_count = $start->diff($end)->days + 1;
        
        if(empty($employee_name) || empty($leave_details) || empty($start_date) || empty($end_date)) {
            $message = "يرجى ملء جميع الحقول المطلوبة";
            $messageType = 'error';
        } elseif($start_date > $end_date) {
            $message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية";
            $messageType = 'error';
        } else {
            $stmt = $con->prepare("INSERT INTO leave_requests (user_id, employee_name, request_date, leave_type, leave_details, start_date, end_date, days_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("issssssi", $user_id, $employee_name, $request_date, $leave_type, $leave_details, $start_date, $end_date, $days_count);
            
            if($stmt->execute()) {
                $message = "تم إرسال طلب الإجازة بنجاح";
                $messageType = 'success';

                // الحصول على معرف الطلب الجديد
                $leave_id = $con->insert_id;

                // إرسال إشعار للإدارة والمدقق باستخدام النظام الجديد
                include "../Admin/add_notification.php";
                addLeaveRequestNotification($con, $_SESSION['user']->user_name, $leave_type, $start_date, $end_date, $leave_id);
            } else {
                $message = "حدث خطأ أثناء إرسال الطلب";
                $messageType = 'error';
            }
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب إجازة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .request-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .request-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .form-select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }
        
        .back-btn {
            display: inline-block;
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: linear-gradient(45deg, #5a6268, #3d4043);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .days-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
            text-align: center;
            font-weight: bold;
            color: #1976d2;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="request-container">
        <a href="home.php" class="back-btn">
            <i class="fas fa-arrow-right"></i> العودة للرئيسية
        </a>
        
        <div class="request-card">
            <h2 class="card-title">
                <i class="fas fa-calendar-alt"></i>
                طلب إجازة جديد
            </h2>
            
            <?php if($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" id="leaveForm">
                <div class="form-group">
                    <label class="form-label">اسم الموظف *</label>
                    <input type="text" name="employee_name" class="form-control" placeholder="أدخل اسم الموظف" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">تاريخ البداية *</label>
                        <input type="date" name="start_date" class="form-control" id="startDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تاريخ النهاية *</label>
                        <input type="date" name="end_date" class="form-control" id="endDate" required>
                    </div>
                </div>
                
                <div id="daysInfo" class="days-info" style="display: none;">
                    عدد أيام الإجازة: <span id="daysCount">0</span> يوم
                </div>
                
                <div class="form-group">
                    <label class="form-label">نوع الإجازة *</label>
                    <select name="leave_type" class="form-select" required>
                        <option value="">اختر نوع الإجازة</option>
                        <option value="مرضية">مرضية</option>
                        <option value="عرضية">عرضية</option>
                        <option value="طارئة">طارئة</option>
                        <option value="زمنية">زمنية</option>
                        <option value="ظروف أخرى">ظروف أخرى</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">تفاصيل الإجازة *</label>
                    <textarea name="leave_details" class="form-control" rows="5" placeholder="اكتب تفاصيل وأسباب طلب الإجازة..." required></textarea>
                </div>
                
                <button type="submit" name="submit_leave" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الطلب
                </button>
            </form>
        </div>
    </div>
    
    <script>
        function calculateDays() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const daysInfo = document.getElementById('daysInfo');
            const daysCount = document.getElementById('daysCount');
            
            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const timeDiff = end.getTime() - start.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                
                if (daysDiff > 0) {
                    daysCount.textContent = daysDiff;
                    daysInfo.style.display = 'block';
                } else {
                    daysInfo.style.display = 'none';
                }
            } else {
                daysInfo.style.display = 'none';
            }
        }
        
        document.getElementById('startDate').addEventListener('change', calculateDays);
        document.getElementById('endDate').addEventListener('change', calculateDays);
        
        // تعيين الحد الأدنى للتاريخ إلى اليوم الحالي
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('startDate').min = today;
        document.getElementById('endDate').min = today;
    </script>
</body>
</html>
