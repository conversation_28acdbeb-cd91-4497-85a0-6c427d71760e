<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "../db_connection.php";

// الحصول على السنة المطلوبة
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

// استعلام الإيرادات الشهرية
$monthly_revenue_query = "
    SELECT 
        MONTH(stud_pay.datein) as month,
        MONTHNAME(stud_pay.datein) as month_name,
        COUNT(*) as transactions_count,
        SUM(stud_pay.cash_stud) as total_revenue,
        AVG(stud_pay.cash_stud) as avg_revenue
    FROM stud_pay 
    INNER JOIN stud_tb ON stud_pay.id_stud = stud_tb.id
    WHERE YEAR(stud_pay.datein) = $year 
    AND stud_pay.cash_stud > 0
    GROUP BY MONTH(stud_pay.datein)
    ORDER BY MONTH(stud_pay.datein)
";

$monthly_result = $conn->query($monthly_revenue_query);

// حساب الإجماليات
$total_year_revenue = 0;
$total_transactions = 0;
$monthly_data = [];

if ($monthly_result && $monthly_result->num_rows > 0) {
    while ($row = $monthly_result->fetch_assoc()) {
        $monthly_data[] = $row;
        $total_year_revenue += $row['total_revenue'];
        $total_transactions += $row['transactions_count'];
    }
}

// أسماء الشهور بالعربية
$arabic_months = [
    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الإيرادات الشهرية - <?= $year ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .report-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
        @media print { .no-print { display: none !important; } body { background: white !important; } }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- Header -->
        <div class="report-header text-center">
            <h1><i class="fas fa-chart-line"></i> تقرير الإيرادات الشهرية</h1>
            <h3>روضة الأطفال - عام <?= $year ?></h3>
            <p class="mb-0">تاريخ التقرير: <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Controls -->
        <div class="row no-print mb-4">
            <div class="col-md-6">
                <form method="GET" class="d-flex">
                    <select name="year" class="form-select me-2">
                        <?php for ($y = date('Y'); $y >= 2020; $y--): ?>
                            <option value="<?= $y ?>" <?= $y == $year ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                    <button type="submit" class="btn btn-primary">عرض</button>
                </form>
            </div>
            <div class="col-md-6 text-end">
                <button onclick="window.print()" class="btn btn-success">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="../accounting_system.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                    <h4 class="text-success"><?= number_format($total_year_revenue) ?></h4>
                    <p class="text-muted">إجمالي الإيرادات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-receipt fa-3x text-primary mb-3"></i>
                    <h4 class="text-primary"><?= number_format($total_transactions) ?></h4>
                    <p class="text-muted">عدد المعاملات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                    <h4 class="text-info"><?= $total_transactions > 0 ? number_format($total_year_revenue / $total_transactions) : 0 ?></h4>
                    <p class="text-muted">متوسط المعاملة</p>
                </div>
            </div>
        </div>

        <!-- Monthly Data Table -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-table"></i> البيانات الشهرية التفصيلية</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الشهر</th>
                            <th>عدد المعاملات</th>
                            <th>إجمالي الإيرادات</th>
                            <th>متوسط المعاملة</th>
                            <th>النسبة من الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        // إنشاء مصفوفة لجميع الشهور
                        $all_months_data = [];
                        for ($m = 1; $m <= 12; $m++) {
                            $all_months_data[$m] = [
                                'month' => $m,
                                'month_name' => $arabic_months[$m],
                                'transactions_count' => 0,
                                'total_revenue' => 0,
                                'avg_revenue' => 0
                            ];
                        }
                        
                        // ملء البيانات الموجودة
                        foreach ($monthly_data as $data) {
                            $all_months_data[$data['month']] = [
                                'month' => $data['month'],
                                'month_name' => $arabic_months[$data['month']],
                                'transactions_count' => $data['transactions_count'],
                                'total_revenue' => $data['total_revenue'],
                                'avg_revenue' => $data['avg_revenue']
                            ];
                        }
                        
                        foreach ($all_months_data as $data): 
                            $percentage = $total_year_revenue > 0 ? ($data['total_revenue'] / $total_year_revenue) * 100 : 0;
                        ?>
                        <tr>
                            <td><strong><?= $data['month_name'] ?></strong></td>
                            <td><?= number_format($data['transactions_count']) ?></td>
                            <td class="text-success"><strong><?= number_format($data['total_revenue']) ?></strong></td>
                            <td><?= number_format($data['avg_revenue']) ?></td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: <?= $percentage ?>%">
                                        <?= number_format($percentage, 1) ?>%
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-success">
                        <tr>
                            <th>الإجمالي</th>
                            <th><?= number_format($total_transactions) ?></th>
                            <th><?= number_format($total_year_revenue) ?></th>
                            <th><?= $total_transactions > 0 ? number_format($total_year_revenue / $total_transactions) : 0 ?></th>
                            <th>100%</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Chart -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-chart-area"></i> الرسم البياني الشهري</h5>
            <canvas id="monthlyChart" height="100"></canvas>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Monthly Revenue Chart
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [<?php foreach ($all_months_data as $data) echo "'" . $data['month_name'] . "',"; ?>],
                datasets: [{
                    label: 'الإيرادات الشهرية',
                    data: [<?php foreach ($all_months_data as $data) echo $data['total_revenue'] . ","; ?>],
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'الإيرادات الشهرية لعام <?= $year ?>'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>

<?php $conn->close(); ?>
