<?php
include "addon/dbcon.php";

echo "<h2>تفعيل تسجيل الدخول لجميع الطلاب</h2>";

// جلب جميع الطلاب من قاعدة البيانات
$sql = "SELECT id, name, p_phone, datein FROM stud_tb ORDER BY id";
$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<h3>الطلاب المتاحون للتسجيل:</h3>";
    echo "<div style='background: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-family: Arial, sans-serif;'>";
    echo "<tr style='background: #3498db; color: white;'>
            <th style='padding: 12px; text-align: center;'>رقم الطالب</th>
            <th style='padding: 12px; text-align: center;'>اسم الطالب</th>
            <th style='padding: 12px; text-align: center;'>رقم هاتف ولي الأمر</th>
            <th style='padding: 12px; text-align: center;'>تاريخ التسجيل</th>
            <th style='padding: 12px; text-align: center;'>كلمات المرور المتاحة</th>
          </tr>";
    
    $count = 0;
    while ($student = mysqli_fetch_assoc($result)) {
        $count++;
        $bg_color = $count % 2 == 0 ? '#f8f9fa' : 'white';
        
        echo "<tr style='background: $bg_color;'>";
        echo "<td style='padding: 10px; text-align: center; font-weight: bold; color: #2c3e50;'>" . $student['id'] . "</td>";
        echo "<td style='padding: 10px; text-align: center; color: #2c3e50;'>" . htmlspecialchars($student['name']) . "</td>";
        echo "<td style='padding: 10px; text-align: center; color: #2c3e50;'>" . ($student['p_phone'] ?? 'غير محدد') . "</td>";
        echo "<td style='padding: 10px; text-align: center; color: #2c3e50;'>" . ($student['datein'] ?? 'غير محدد') . "</td>";
        echo "<td style='padding: 10px; text-align: right; font-size: 0.9rem;'>";
        echo "<div style='display: flex; flex-direction: column; gap: 5px;'>";
        echo "<span style='background: #e8f5e8; padding: 3px 8px; border-radius: 5px; color: #2d5a2d;'>• <strong>123456</strong> (كلمة مرور افتراضية)</span>";
        echo "<span style='background: #e8f4fd; padding: 3px 8px; border-radius: 5px; color: #1e4a72;'>• <strong>" . $student['id'] . "</strong> (رقم الطالب)</span>";
        echo "<span style='background: #fff3cd; padding: 3px 8px; border-radius: 5px; color: #856404;'>• <strong>" . htmlspecialchars($student['name']) . "</strong> (اسم الطالب)</span>";
        if (!empty($student['p_phone'])) {
            echo "<span style='background: #f8d7da; padding: 3px 8px; border-radius: 5px; color: #721c24;'>• <strong>" . $student['p_phone'] . "</strong> (رقم الهاتف)</span>";
        }
        if (!empty($student['datein'])) {
            echo "<span style='background: #d1ecf1; padding: 3px 8px; border-radius: 5px; color: #0c5460;'>• <strong>" . $student['datein'] . "</strong> (تاريخ التسجيل)</span>";
        }
        echo "</div>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; padding: 20px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin-bottom: 15px;'>✅ تم العثور على $count طالب</h4>";
    echo "<p style='color: #155724; margin: 5px 0;'><strong>جميع الطلاب يمكنهم الآن تسجيل الدخول باستخدام:</strong></p>";
    echo "<ul style='color: #155724; margin: 10px 0; padding-right: 20px;'>";
    echo "<li><strong>رقم الطالب:</strong> أي رقم من العمود الأول</li>";
    echo "<li><strong>كلمة المرور:</strong> أي من الخيارات المذكورة في العمود الأخير</li>";
    echo "</ul>";
    echo "</div>";
    
    // إنشاء الجداول المطلوبة
    echo "<h3>إعداد الجداول المطلوبة:</h3>";
    
    // جدول إجازات الطلاب
    $create_student_leaves = "CREATE TABLE IF NOT EXISTS `student_leaves` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `student_id` int(11) NOT NULL,
      `student_name` varchar(255) NOT NULL,
      `leave_type` varchar(100) NOT NULL,
      `start_date` date NOT NULL,
      `end_date` date NOT NULL,
      `reason` text NOT NULL,
      `request_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `status` varchar(50) NOT NULL DEFAULT 'قيد المراجعة',
      `admin_response` text,
      `response_by` int(11),
      `response_date` datetime,
      PRIMARY KEY (`id`),
      KEY `student_id` (`student_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_student_leaves)) {
        echo "<p style='color: green;'>✅ جدول student_leaves جاهز</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في جدول student_leaves: " . mysqli_error($con) . "</p>";
    }
    
    // جدول الأخبار
    $create_news = "CREATE TABLE IF NOT EXISTS `news_announcements` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `title` varchar(255) NOT NULL,
      `content` text NOT NULL,
      `type` varchar(100) DEFAULT 'إعلان',
      `priority` varchar(50) DEFAULT 'متوسطة',
      `status` varchar(50) DEFAULT 'نشط',
      `created_by` int(11),
      `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_news)) {
        echo "<p style='color: green;'>✅ جدول news_announcements جاهز</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في جدول news_announcements: " . mysqli_error($con) . "</p>";
    }
    
    // جدول العطل
    $create_holidays = "CREATE TABLE IF NOT EXISTS `official_holidays` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `holiday_name` varchar(255) NOT NULL,
      `holiday_date` date NOT NULL,
      `description` text,
      `duration_days` int(11) DEFAULT 1,
      `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_holidays)) {
        echo "<p style='color: green;'>✅ جدول official_holidays جاهز</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في جدول official_holidays: " . mysqli_error($con) . "</p>";
    }
    
    // إضافة عمود student_request إلى جدول leave_requests إذا كان موجوداً
    $check_leave_requests = "SHOW TABLES LIKE 'leave_requests'";
    $table_result = mysqli_query($con, $check_leave_requests);
    
    if (mysqli_num_rows($table_result) > 0) {
        $check_column = "SHOW COLUMNS FROM leave_requests LIKE 'student_request'";
        $column_result = mysqli_query($con, $check_column);
        
        if (mysqli_num_rows($column_result) == 0) {
            $add_column = "ALTER TABLE leave_requests ADD COLUMN student_request tinyint(1) DEFAULT 0";
            if (mysqli_query($con, $add_column)) {
                echo "<p style='color: green;'>✅ تم إضافة عمود student_request إلى جدول leave_requests</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ تعذر إضافة عمود student_request: " . mysqli_error($con) . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود student_request موجود بالفعل</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ جدول leave_requests غير موجود - سيتم إنشاؤه تلقائياً عند الحاجة</p>";
    }
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 10px; padding: 20px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ لا توجد بيانات طلاب</h4>";
    echo "<p style='color: #721c24;'>لم يتم العثور على أي طلاب في قاعدة البيانات. يرجى التأكد من وجود بيانات في جدول stud_tb</p>";
    echo "</div>";
}

echo "<br><div style='background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 10px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #0066cc;'>🔗 روابط مفيدة:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;'>";
echo "<a href='simple_login.php' style='background: #3498db; color: white; padding: 12px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: bold;'>🚀 تسجيل الدخول</a>";
echo "<a href='test_leave_request.php' style='background: #e74c3c; color: white; padding: 12px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: bold;'>📝 اختبار طلب إجازة</a>";
echo "<a href='../Admin/student_app_control.php' style='background: #27ae60; color: white; padding: 12px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: bold;'>⚙️ تحكم الأدمن</a>";
echo "<a href='index.php' style='background: #f39c12; color: white; padding: 12px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: bold;'>🏠 الصفحة الرئيسية</a>";
echo "</div>";
echo "</div>";

mysqli_close($con);
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        margin: 0;
    }
    
    h2, h3 {
        color: white;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 20px;
    }
    
    table {
        font-size: 0.9rem;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    th {
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    td, th {
        border: 1px solid #dee2e6 !important;
    }
    
    a {
        transition: all 0.3s ease;
    }
    
    a:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        text-decoration: none !important;
    }
    
    p {
        margin: 8px 0;
        line-height: 1.6;
    }
    
    ul {
        line-height: 1.8;
    }
    
    @media (max-width: 768px) {
        body {
            padding: 10px;
        }
        
        table {
            font-size: 0.8rem;
        }
        
        th, td {
            padding: 8px !important;
        }
        
        .responsive-grid {
            grid-template-columns: 1fr !important;
        }
    }
</style>
