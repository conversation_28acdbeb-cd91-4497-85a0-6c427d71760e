<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

// جلب العطل الرسمية من قاعدة البيانات
$sql = "SELECT * FROM official_holidays ORDER BY holiday_date ASC";
$result = mysqli_query($con, $sql);
$holidays = [];
if ($result) {
    $holidays = mysqli_fetch_all($result, MYSQLI_ASSOC);
}

// إذا لم توجد بيانات في قاعدة البيانات، استخدم بيانات افتراضية
if (empty($holidays)) {
    $holidays = [
        [
            'holiday_name' => 'رأس السنة الميلادية',
            'holiday_date' => '2024-01-01',
            'description' => 'عطلة رأس السنة الميلادية',
            'duration_days' => 1
        ],
        [
            'holiday_name' => 'عيد الفطر المبارك',
            'holiday_date' => '2024-04-10',
            'description' => 'عطلة عيد الفطر المبارك',
            'duration_days' => 3
        ],
        [
            'holiday_name' => 'عيد الأضحى المبارك',
            'holiday_date' => '2024-06-16',
            'description' => 'عطلة عيد الأضحى المبارك',
            'duration_days' => 4
        ],
        [
            'holiday_name' => 'رأس السنة الهجرية',
            'holiday_date' => '2024-07-07',
            'description' => 'عطلة رأس السنة الهجرية',
            'duration_days' => 1
        ],
        [
            'holiday_name' => 'المولد النبوي الشريف',
            'holiday_date' => '2024-09-15',
            'description' => 'عطلة المولد النبوي الشريف',
            'duration_days' => 1
        ],
        [
            'holiday_name' => 'عطلة نصف السنة',
            'holiday_date' => '2024-12-25',
            'description' => 'عطلة نهاية العام الدراسي',
            'duration_days' => 7
        ]
    ];
}

// تصنيف العطل (قادمة، حالية، منتهية)
$current_date = new DateTime();
$upcoming_holidays = [];
$current_holidays = [];
$past_holidays = [];

foreach ($holidays as $holiday) {
    $holiday_date = new DateTime($holiday['holiday_date']);
    $duration = isset($holiday['duration_days']) ? (int)$holiday['duration_days'] : 1;
    $end_date = clone $holiday_date;
    $end_date->add(new DateInterval('P' . ($duration - 1) . 'D'));
    
    if ($current_date >= $holiday_date && $current_date <= $end_date) {
        $current_holidays[] = $holiday;
    } elseif ($current_date < $holiday_date) {
        $upcoming_holidays[] = $holiday;
    } else {
        $past_holidays[] = $holiday;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 العطل الرسمية - أكاديمية كيدز</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/student-style.css">
    <link rel="icon" href="css/icon.ico">
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container-fluid">
        <div class="holidays-section">
            <div class="holidays-header">
                <div class="header-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h1>العطل الرسمية</h1>
                <p>جدول العطل والإجازات الرسمية للعام الدراسي</p>
            </div>
            
            <!-- العطل الحالية -->
            <?php if (!empty($current_holidays)): ?>
                <div class="holidays-category">
                    <div class="category-header current">
                        <i class="fas fa-calendar-check"></i>
                        <h2>العطل الحالية</h2>
                    </div>
                    <div class="holidays-grid">
                        <?php foreach ($current_holidays as $holiday): ?>
                            <div class="holiday-card current-holiday">
                                <div class="holiday-badge">
                                    <i class="fas fa-calendar-day"></i>
                                    جاري الآن
                                </div>
                                <div class="holiday-content">
                                    <h3><?php echo htmlspecialchars($holiday['holiday_name']); ?></h3>
                                    <div class="holiday-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo date('Y/m/d', strtotime($holiday['holiday_date'])); ?>
                                    </div>
                                    <?php if (isset($holiday['duration_days']) && $holiday['duration_days'] > 1): ?>
                                        <div class="holiday-duration">
                                            <i class="fas fa-clock"></i>
                                            <?php echo $holiday['duration_days']; ?> أيام
                                        </div>
                                    <?php endif; ?>
                                    <div class="holiday-description">
                                        <?php echo htmlspecialchars($holiday['description']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- العطل القادمة -->
            <?php if (!empty($upcoming_holidays)): ?>
                <div class="holidays-category">
                    <div class="category-header upcoming">
                        <i class="fas fa-calendar-plus"></i>
                        <h2>العطل القادمة</h2>
                    </div>
                    <div class="holidays-grid">
                        <?php foreach ($upcoming_holidays as $holiday): 
                            $holiday_date = new DateTime($holiday['holiday_date']);
                            $days_until = $current_date->diff($holiday_date)->days;
                        ?>
                            <div class="holiday-card upcoming-holiday">
                                <div class="holiday-badge">
                                    <i class="fas fa-hourglass-half"></i>
                                    بعد <?php echo $days_until; ?> يوم
                                </div>
                                <div class="holiday-content">
                                    <h3><?php echo htmlspecialchars($holiday['holiday_name']); ?></h3>
                                    <div class="holiday-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo date('Y/m/d', strtotime($holiday['holiday_date'])); ?>
                                    </div>
                                    <?php if (isset($holiday['duration_days']) && $holiday['duration_days'] > 1): ?>
                                        <div class="holiday-duration">
                                            <i class="fas fa-clock"></i>
                                            <?php echo $holiday['duration_days']; ?> أيام
                                        </div>
                                    <?php endif; ?>
                                    <div class="holiday-description">
                                        <?php echo htmlspecialchars($holiday['description']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- العطل المنتهية -->
            <?php if (!empty($past_holidays)): ?>
                <div class="holidays-category">
                    <div class="category-header past">
                        <i class="fas fa-calendar-minus"></i>
                        <h2>العطل المنتهية</h2>
                    </div>
                    <div class="holidays-grid">
                        <?php foreach (array_reverse($past_holidays) as $holiday): ?>
                            <div class="holiday-card past-holiday">
                                <div class="holiday-badge">
                                    <i class="fas fa-check-circle"></i>
                                    انتهت
                                </div>
                                <div class="holiday-content">
                                    <h3><?php echo htmlspecialchars($holiday['holiday_name']); ?></h3>
                                    <div class="holiday-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo date('Y/m/d', strtotime($holiday['holiday_date'])); ?>
                                    </div>
                                    <?php if (isset($holiday['duration_days']) && $holiday['duration_days'] > 1): ?>
                                        <div class="holiday-duration">
                                            <i class="fas fa-clock"></i>
                                            <?php echo $holiday['duration_days']; ?> أيام
                                        </div>
                                    <?php endif; ?>
                                    <div class="holiday-description">
                                        <?php echo htmlspecialchars($holiday['description']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (empty($holidays)): ?>
                <div class="no-holidays">
                    <div class="no-holidays-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <h3>لا توجد عطل محددة</h3>
                    <p>لم يتم تحديد أي عطل رسمية حتى الآن</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.holiday-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('animate-in');
            });
        });
    </script>
    
    <style>
        .holidays-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .holidays-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .holidays-header h1 {
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .holidays-header p {
            color: rgba(255,255,255,0.9);
        }
        
        .holidays-category {
            margin-bottom: 3rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 15px;
            color: white;
            font-weight: bold;
        }
        
        .category-header.current {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .category-header.upcoming {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .category-header.past {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .category-header h2 {
            margin: 0;
            font-size: 1.3rem;
        }
        
        .holidays-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .holiday-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .holiday-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .holiday-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
        }
        
        .current-holiday::before {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .upcoming-holiday::before {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .past-holiday::before {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .holiday-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }
        
        .current-holiday .holiday-badge {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .upcoming-holiday .holiday-badge {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .past-holiday .holiday-badge {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .holiday-content {
            margin-top: 2rem;
        }
        
        .holiday-content h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .holiday-date, .holiday-duration {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.8rem;
            color: #34495e;
            font-weight: 500;
        }
        
        .holiday-date i, .holiday-duration i {
            color: #3498db;
            width: 20px;
        }
        
        .holiday-description {
            color: #7f8c8d;
            line-height: 1.6;
            font-style: italic;
        }
        
        .no-holidays {
            text-align: center;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .no-holidays-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
        }
        
        .no-holidays h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .no-holidays p {
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .holidays-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .holiday-card {
                padding: 1rem;
            }
            
            .category-header {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }
            
            .holiday-badge {
                position: static;
                display: inline-block;
                margin-bottom: 1rem;
            }
            
            .holiday-content {
                margin-top: 0;
            }
        }
    </style>
</body>
</html>
