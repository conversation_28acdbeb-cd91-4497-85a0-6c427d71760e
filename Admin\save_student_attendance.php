<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح', 'success' => false]);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة', 'success' => false]);
    exit();
}

try {
    // جلب البيانات من الطلب
    $student_id = $_POST['student_id'] ?? '';
    $student_name = $_POST['student_name'] ?? '';
    $attendance_date = $_POST['attendance_date'] ?? '';
    $status = $_POST['status'] ?? '';
    $check_in_time = $_POST['check_in_time'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $leave_start_date = $_POST['leave_start_date'] ?? '';
    $leave_end_date = $_POST['leave_end_date'] ?? '';

    // التحقق من البيانات المطلوبة
    if (empty($student_id) || empty($student_name) || empty($attendance_date) || empty($status)) {
        echo json_encode(['error' => 'البيانات المطلوبة مفقودة', 'success' => false]);
        exit();
    }

    // التحقق من صحة التاريخ
    if (!DateTime::createFromFormat('Y-m-d', $attendance_date)) {
        echo json_encode(['error' => 'تاريخ غير صحيح', 'success' => false]);
        exit();
    }

    // التحقق من وجود الطالب
    $student_check = $con->prepare("SELECT id, name FROM stud_tb WHERE id = ?");
    $student_check->bind_param("i", $student_id);
    $student_check->execute();
    $student_result = $student_check->get_result();
    
    if ($student_result->num_rows === 0) {
        echo json_encode(['error' => 'الطالب غير موجود', 'success' => false]);
        exit();
    }

    // إعداد التاريخ والوقت للحضور
    $attendance_datetime = $attendance_date;
    if (!empty($check_in_time) && $status !== 'غائب' && $status !== 'إجازة') {
        $attendance_datetime = $attendance_date . ' ' . $check_in_time . ':00';
    }

    // التحقق من وجود سجل حضور سابق لنفس اليوم
    $existing_check = $con->prepare("SELECT id FROM stat WHERE id_stud = ? AND DATE(data_stat) = ?");
    $existing_check->bind_param("is", $student_id, $attendance_date);
    $existing_check->execute();
    $existing_result = $existing_check->get_result();

    if ($existing_result->num_rows > 0) {
        // تحديث السجل الموجود
        $update_stmt = $con->prepare("UPDATE stat SET stat_stud = ?, data_stat = ? WHERE id_stud = ? AND DATE(data_stat) = ?");
        $update_stmt->bind_param("ssis", $status, $attendance_datetime, $student_id, $attendance_date);
        
        if ($update_stmt->execute()) {
            // إضافة ملاحظات إذا كانت موجودة
            if (!empty($notes)) {
                // يمكن إضافة جدول منفصل للملاحظات أو إضافة عمود notes لجدول stat
                // هنا سنتجاهل الملاحظات مؤقتاً لأن جدول stat الحالي لا يحتوي على عمود notes
            }

            // معالجة الإجازات إذا كانت الحالة إجازة
            if ($status === 'إجازة' && !empty($leave_start_date) && !empty($leave_end_date)) {
                // يمكن إضافة منطق لحفظ بيانات الإجازة في جدول منفصل
                // هنا سنتجاهل هذا مؤقتاً
            }

            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث بيانات الحضور بنجاح',
                'student_id' => $student_id,
                'student_name' => $student_name,
                'status' => $status,
                'date' => $attendance_date
            ]);
        } else {
            echo json_encode(['error' => 'فشل في تحديث بيانات الحضور', 'success' => false]);
        }
    } else {
        // إدراج سجل جديد
        $insert_stmt = $con->prepare("INSERT INTO stat (id_stud, stat_stud, data_stat) VALUES (?, ?, ?)");
        $insert_stmt->bind_param("iss", $student_id, $status, $attendance_datetime);
        
        if ($insert_stmt->execute()) {
            // إضافة ملاحظات إذا كانت موجودة
            if (!empty($notes)) {
                // يمكن إضافة جدول منفصل للملاحظات أو إضافة عمود notes لجدول stat
                // هنا سنتجاهل الملاحظات مؤقتاً لأن جدول stat الحالي لا يحتوي على عمود notes
            }

            // معالجة الإجازات إذا كانت الحالة إجازة
            if ($status === 'إجازة' && !empty($leave_start_date) && !empty($leave_end_date)) {
                // يمكن إضافة منطق لحفظ بيانات الإجازة في جدول منفصل
                // هنا سنتجاهل هذا مؤقتاً
            }

            echo json_encode([
                'success' => true,
                'message' => 'تم حفظ بيانات الحضور بنجاح',
                'student_id' => $student_id,
                'student_name' => $student_name,
                'status' => $status,
                'date' => $attendance_date
            ]);
        } else {
            echo json_encode(['error' => 'فشل في حفظ بيانات الحضور', 'success' => false]);
        }
    }

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'details' => $e->getMessage(),
        'success' => false
    ]);
}

// إغلاق الاتصال
$con->close();
?>
