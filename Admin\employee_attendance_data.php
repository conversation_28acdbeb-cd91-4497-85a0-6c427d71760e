<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// معالجة المعاملات
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$selected_employee = isset($_GET['employee']) ? $_GET['employee'] : '0';

try {
    // التحقق من هيكل جدول الموظفين
    $columns_query = "SHOW COLUMNS FROM employ_tb";
    $columns_result = $con->query($columns_query);
    $columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }

    // تحديد العمود المناسب للمعرف في جدول employ_tb
    $id_column = 'id_employ'; // العمود الصحيح في جدول employ_tb
    if (in_array('id_employ', $columns)) {
        $id_column = 'id_employ';
    } elseif (in_array('id', $columns)) {
        $id_column = 'id';
    } elseif (in_array('emp_id', $columns)) {
        $id_column = 'emp_id';
    }

    // جلب إجمالي عدد الموظفين
    $total_query = "SELECT COUNT(*) as total FROM employ_tb";
    $total_result = $con->query($total_query);
    $total_count = $total_result->fetch_assoc()['total'];

    // جلب الموظفين مع بيانات الحضور من جدول stat2
    $employees_with_attendance = [];

    if ($selected_employee != '0') {
        // موظف محدد - البحث في جميع الأعمدة المحتملة مع ربط المستخدم
        $emp_query = "SELECT e.$id_column as emp_id, e.f_name, e.job, u.user_name
                      FROM employ_tb e
                      LEFT JOIN users_tb u ON e.userID = u.id_user
                      WHERE e.$id_column = ? OR e.f_name = ? OR e.job LIKE ?";
        $stmt = $con->prepare($emp_query);
        $search_term = "%$selected_employee%";
        $stmt->bind_param("sss", $selected_employee, $selected_employee, $search_term);
        $stmt->execute();
        $emp_result = $stmt->get_result();

        // إذا لم نجد نتائج، جرب البحث بطريقة أخرى
        if ($emp_result->num_rows == 0) {
            $emp_query = "SELECT e.$id_column as emp_id, e.f_name, e.job, u.user_name
                          FROM employ_tb e
                          LEFT JOIN users_tb u ON e.userID = u.id_user
                          ORDER BY e.f_name";
            $emp_result = $con->query($emp_query);
        }
    } else {
        // جميع الموظفين مع ربط المستخدم
        $emp_query = "SELECT e.$id_column as emp_id, e.f_name, e.job, u.user_name
                      FROM employ_tb e
                      LEFT JOIN users_tb u ON e.userID = u.id_user
                      ORDER BY e.f_name";
        $emp_result = $con->query($emp_query);
    }

    if ($emp_result) {
        while ($employee = $emp_result->fetch_assoc()) {
            // جلب بيانات الحضور من جدول stat2 لهذا الموظف في التاريخ المحدد
            $attendance_query = "SELECT stat_employee, data_stat, leave_start_date, leave_end_date
                                FROM stat2
                                WHERE id_employee = ? AND DATE(data_stat) = ?
                                ORDER BY data_stat DESC
                                LIMIT 1";
            $att_stmt = $con->prepare($attendance_query);
            $att_stmt->bind_param("ss", $employee['emp_id'], $selected_date);
            $att_stmt->execute();
            $attendance_result = $att_stmt->get_result();
            $attendance_data = $attendance_result->fetch_assoc();

            // تحديد وقت الحضور بناءً على البيانات الموجودة
            $check_in_time = null;
            if ($attendance_data) {
                // استخراج الوقت من data_stat إذا كان يحتوي على وقت
                $datetime = $attendance_data['data_stat'];
                if (strpos($datetime, ' ') !== false) {
                    $check_in_time = date('H:i', strtotime($datetime));
                } else {
                    // وقت افتراضي بناءً على الحالة
                    switch($attendance_data['stat_employee']) {
                        case 'حاضر':
                            $check_in_time = '08:00';
                            break;
                        case 'متأخر':
                            $check_in_time = '09:15';
                            break;
                        default:
                            $check_in_time = null;
                    }
                }
            }

            $employees_with_attendance[] = [
                'id' => $employee['emp_id'],
                'f_name' => $employee['f_name'],
                'job' => $employee['job'] ?? 'غير محدد',
                'user_name' => $employee['user_name'] ?? 'غير محدد',
                'attendance' => $attendance_data ? [
                    'status' => $attendance_data['stat_employee'],
                    'check_in_time' => $check_in_time,
                    'data_stat' => $attendance_data['data_stat'],
                    'leave_start_date' => $attendance_data['leave_start_date'],
                    'leave_end_date' => $attendance_data['leave_end_date']
                ] : null
            ];
        }
    }

    // التحقق من وجود جدول stat2
    $attendance_table_exists = false;
    $check_attendance_table = $con->query("SHOW TABLES LIKE 'stat2'");
    if ($check_attendance_table && $check_attendance_table->num_rows > 0) {
        $attendance_table_exists = true;
    }

    // إرجاع البيانات
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'employees' => $employees_with_attendance,
        'total_count' => $total_count,
        'selected_date' => $selected_date,
        'selected_employee' => $selected_employee,
        'id_column' => $id_column,
        'available_columns' => $columns,
        'attendance_table_exists' => $attendance_table_exists,
        'success' => true
    ], JSON_UNESCAPED_UNICODE);

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في جلب البيانات',
        'details' => $e->getMessage(),
        'success' => false
    ], JSON_UNESCAPED_UNICODE);
}

// إغلاق الاتصال
$con->close();
?>
