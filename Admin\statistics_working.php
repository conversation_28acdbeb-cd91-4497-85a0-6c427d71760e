<?php
session_start();

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location:../login.php");
    exit();
}

if (isset($_GET['exitbtn'])) {
    header("location:../logout.php");
    exit();
}

include "addon/dbcon.php";

$search = isset($_GET['search']) ? (int)$_GET['search'] : 0;
$datenow = date('Y-m-d');

// جلب جميع المستخدمين
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = mysqli_query($con, $users_query);

// تحديد شرط المستخدم
if ($search > 0) {
    $user_condition = "AND users_tb.id_user = $search";
    $selected_user_query = "SELECT user_name FROM users_tb WHERE id_user = $search";
    $selected_user_result = mysqli_query($con, $selected_user_query);
    $selected_user_name = $selected_user_result ? mysqli_fetch_assoc($selected_user_result)['user_name'] : 'غير معروف';
} else {
    $user_condition = "";
    $selected_user_name = "جميع المستخدمين";
}

// الاستعلامات الأساسية
$sql_total = "SELECT COUNT(*) as total_count FROM stud_tb, users_tb 
             WHERE users_tb.id_user = stud_tb.userID $user_condition";
$result_total = mysqli_query($con, $sql_total);
$total_count = $result_total ? mysqli_fetch_assoc($result_total)['total_count'] : 0;

$sql_active = "SELECT COUNT(*) as active_count FROM stud_tb, stud_pay, users_tb 
              WHERE stud_tb.id = stud_pay.id_stud 
              AND users_tb.id_user = stud_tb.userID 
              $user_condition
              AND DATEDIFF(stud_pay.date_exp, '$datenow') > 3";
$result_active = mysqli_query($con, $sql_active);
$active_count = $result_active ? mysqli_fetch_assoc($result_active)['active_count'] : 0;

$sql_expired = "SELECT COUNT(*) as expired_count FROM stud_tb, stud_pay, users_tb 
               WHERE stud_tb.id = stud_pay.id_stud 
               AND users_tb.id_user = stud_tb.userID 
               $user_condition
               AND DATEDIFF(stud_pay.date_exp, '$datenow') <= 0";
$result_expired = mysqli_query($con, $sql_expired);
$expired_count = $result_expired ? mysqli_fetch_assoc($result_expired)['expired_count'] : 0;

$sql_soon = "SELECT COUNT(*) as soon_count FROM stud_tb, stud_pay, users_tb 
            WHERE stud_tb.id = stud_pay.id_stud 
            AND users_tb.id_user = stud_tb.userID 
            $user_condition
            AND DATEDIFF(stud_pay.date_exp, '$datenow') BETWEEN 1 AND 3";
$result_soon = mysqli_query($con, $sql_soon);
$soon_count = $result_soon ? mysqli_fetch_assoc($result_soon)['soon_count'] : 0;

$sql_new = "SELECT COUNT(*) as new_count FROM stud_tb, users_tb 
           WHERE users_tb.id_user = stud_tb.userID 
           $user_condition
           AND MONTH(stud_tb.date_add) = MONTH('$datenow') 
           AND YEAR(stud_tb.date_add) = YEAR('$datenow')";
$result_new = mysqli_query($con, $sql_new);
$new_count = $result_new ? mysqli_fetch_assoc($result_new)['new_count'] : 0;

// إحصائيات المصاريف
$expenses_query = "SELECT COUNT(*) as count, COALESCE(SUM(depit_amount), 0) as total FROM depit_tb d 
                  JOIN users_tb u ON d.user_id = u.id_user 
                  WHERE 1=1 " . ($search > 0 ? "AND d.user_id = $search" : "");
$expenses_result = mysqli_query($con, $expenses_query);
if ($expenses_result) {
    $expenses_data = mysqli_fetch_assoc($expenses_result);
    $total_expenses_count = $expenses_data['count'];
    $total_expenses_amount = $expenses_data['total'];
} else {
    $total_expenses_count = 0;
    $total_expenses_amount = 0;
}

// إحصائيات الفئات
$categories = ['روضة', 'حضانة', 'تمهيدي', 'تحضيري'];
$category_stats = [];
foreach ($categories as $category) {
    $cat_query = "SELECT COUNT(*) as count FROM stud_tb, users_tb 
                  WHERE users_tb.id_user = stud_tb.userID 
                  AND stud_tb.catg = '$category' 
                  $user_condition";
    $cat_result = mysqli_query($con, $cat_query);
    $category_stats[$category] = $cat_result ? mysqli_fetch_assoc($cat_result)['count'] : 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات - أكاديمية الأطفال</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/admin_navbar.css">
    <link rel="icon" href="css/icon.ico">
    
    <?php include "addon/topbar.php"; ?>
    
    <style>
        .statistics-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .user-selector {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .user-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .category-stats {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .category-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e0e0e0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .print-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .print-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .selected-user-info {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        /* ألوان مختلفة للبطاقات */
        .stat-card.active { border-left: 5px solid #28a745; }
        .stat-card.expired { border-left: 5px solid #dc3545; }
        .stat-card.soon { border-left: 5px solid #ffc107; }
        .stat-card.new { border-left: 5px solid #17a2b8; }
        .stat-card.total { border-left: 5px solid #6f42c1; }
        .stat-card.expenses { border-left: 5px solid #fd7e14; }

        .stat-card.active .stat-icon { color: #28a745; }
        .stat-card.expired .stat-icon { color: #dc3545; }
        .stat-card.soon .stat-icon { color: #ffc107; }
        .stat-card.new .stat-icon { color: #17a2b8; }
        .stat-card.total .stat-icon { color: #6f42c1; }
        .stat-card.expenses .stat-icon { color: #fd7e14; }

        @media (max-width: 768px) {
            .statistics-container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .stat-card {
                padding: 20px;
            }
            
            .stat-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="statistics-container">
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> إحصائيات أكاديمية الأطفال</h1>
            <p>تقرير شامل عن حالة الطلاب والمصاريف</p>
        </div>

        <div class="user-selector">
            <h3><i class="fas fa-user-friends"></i> اختيار المستخدم</h3>
            <form method="GET" action="">
                <select name="search" onchange="this.form.submit()">
                    <option value="0">جميع المستخدمين</option>
                    <?php while ($user_row = mysqli_fetch_assoc($users_result)): ?>
                        <option value="<?php echo $user_row['id_user']; ?>" 
                                <?php echo ($search == $user_row['id_user']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($user_row['user_name']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </form>
        </div>

        <?php if ($search > 0): ?>
            <div class="selected-user-info">
                <h2><i class="fas fa-user"></i> إحصائيات المستخدم: <?php echo htmlspecialchars($selected_user_name); ?></h2>
                <button class="print-btn" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة الإحصائيات
                </button>
            </div>
        <?php endif; ?>

        <div class="stats-grid">
            <div class="stat-card total" onclick="showDetails('total')">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_count); ?></div>
                <div class="stat-label">إجمالي الطلاب</div>
            </div>

            <div class="stat-card active" onclick="showDetails('active')">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo number_format($active_count); ?></div>
                <div class="stat-label">الطلاب النشطون</div>
            </div>

            <div class="stat-card expired" onclick="showDetails('expired')">
                <div class="stat-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number"><?php echo number_format($expired_count); ?></div>
                <div class="stat-label">منتهي الاشتراك</div>
            </div>

            <div class="stat-card soon" onclick="showDetails('soon')">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?php echo number_format($soon_count); ?></div>
                <div class="stat-label">قريب الانتهاء</div>
            </div>

            <div class="stat-card new" onclick="showDetails('new')">
                <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stat-number"><?php echo number_format($new_count); ?></div>
                <div class="stat-label">تسجيل جديد</div>
            </div>

            <div class="stat-card expenses" onclick="showExpenseDetails()">
                <div class="stat-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_expenses_count); ?></div>
                <div class="stat-label">إجمالي المصاريف</div>
            </div>

            <div class="stat-card expenses" onclick="showExpenseDetails()">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_expenses_amount); ?></div>
                <div class="stat-label">المبلغ الإجمالي</div>
            </div>
        </div>

        <div class="category-stats">
            <h3><i class="fas fa-layer-group"></i> إحصائيات الفئات</h3>
            <div class="category-grid">
                <?php foreach ($category_stats as $category => $count): ?>
                    <div class="category-item" onclick="showCategoryDetails('<?php echo $category; ?>')">
                        <h4><?php echo htmlspecialchars($category); ?></h4>
                        <div class="stat-number" style="font-size: 1.5rem; color: #667eea;">
                            <?php echo number_format($count); ?>
                        </div>
                        <div class="stat-label">طالب</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="print-btn" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <a href="home.php" class="print-btn" style="background: #6c757d;">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <script>
        // وظيفة عرض تفاصيل الطلاب
        function showDetails(type) {
            const search = <?php echo $search; ?>;
            let url = '';

            switch(type) {
                case 'total':
                    url = search > 0 ? `allstud.php?search=${search}` : 'allstud.php';
                    break;
                case 'active':
                    url = search > 0 ? `new_stud.php?search=${search}` : 'new_stud.php';
                    break;
                case 'expired':
                    url = search > 0 ? `expired_stud.php?search=${search}` : 'expired_stud.php';
                    break;
                case 'soon':
                    url = search > 0 ? `expried_soon.php?search=${search}` : 'expried_soon.php';
                    break;
                case 'new':
                    url = search > 0 ? `new_stud.php?search=${search}` : 'new_stud.php';
                    break;
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        // وظيفة عرض تفاصيل المصاريف
        function showExpenseDetails() {
            const search = <?php echo $search; ?>;
            window.open(search > 0 ? `info_depit.php?search=${search}` : 'info_depit.php', '_blank');
        }

        // وظيفة عرض تفاصيل الفئات
        function showCategoryDetails(category) {
            const search = <?php echo $search; ?>;
            let url = '';

            switch(category) {
                case 'روضة':
                    url = search > 0 ? `rodaStud.php?search=${search}` : 'rodaStud.php';
                    break;
                case 'حضانة':
                    url = search > 0 ? `hadanaStud.php?search=${search}` : 'hadanaStud.php';
                    break;
                case 'تمهيدي':
                    url = search > 0 ? `tamhediStud.php?search=${search}` : 'tamhediStud.php';
                    break;
                case 'تحضيري':
                    url = search > 0 ? `tadeery.php?search=${search}` : 'tadeery.php';
                    break;
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        // تأثيرات تفاعلية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.stat-card').forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;

                // تأثير العد التصاعدي للأرقام
                const numberElement = card.querySelector('.stat-number');
                if (numberElement) {
                    const targetNumber = parseInt(numberElement.textContent.replace(/,/g, ''));

                    if (!isNaN(targetNumber) && targetNumber > 0) {
                        let currentNumber = 0;
                        const increment = targetNumber / 50;
                        const timer = setInterval(() => {
                            currentNumber += increment;
                            if (currentNumber >= targetNumber) {
                                currentNumber = targetNumber;
                                clearInterval(timer);
                            }
                            numberElement.textContent = Math.floor(currentNumber).toLocaleString();
                        }, 30);
                    }
                }
            });

            console.log('📊 صفحة الإحصائيات جاهزة!');
        });
    </script>
</body>
</html>
