<?php
// ملف إصلاح مشاكل حضور الطلاب
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>إصلاح مشاكل حضور الطلاب</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>";

// 1. فحص الملفات المطلوبة
echo "<div class='box'>";
echo "<h3>1. فحص الملفات:</h3>";

$required_files = [
    'addon/dbcon.php' => 'ملف الاتصال بقاعدة البيانات',
    'student_attendance_data.php' => 'ملف API البيانات',
    'save_student_attendance.php' => 'ملف حفظ البيانات',
    'attandec.php' => 'الصفحة الرئيسية',
    'attandec_simple.php' => 'الصفحة المبسطة'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description ($file) موجود</p>";
    } else {
        echo "<p class='error'>❌ $description ($file) غير موجود</p>";
    }
}
echo "</div>";

// 2. فحص قاعدة البيانات
echo "<div class='box'>";
echo "<h3>2. فحص قاعدة البيانات:</h3>";

include "addon/dbcon.php";

if ($con) {
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    $tables = ['stud_tb', 'stat', 'users_tb'];
    foreach ($tables as $table) {
        $result = $con->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $count = $con->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p class='success'>✅ جدول $table موجود ($count سجل)</p>";
        } else {
            echo "<p class='error'>❌ جدول $table غير موجود</p>";
        }
    }
} else {
    echo "<p class='error'>❌ فشل الاتصال بقاعدة البيانات</p>";
}
echo "</div>";

// 3. إنشاء ملف API مبسط
echo "<div class='box'>";
echo "<h3>3. إنشاء ملف API مبسط:</h3>";

$simple_api_content = '<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION["user"]) || $_SESSION["user"]->role !== "Admin") {
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode(["error" => "غير مصرح", "success" => false], JSON_UNESCAPED_UNICODE);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

if (!$con) {
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode(["error" => "فشل في الاتصال بقاعدة البيانات", "success" => false], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $selected_date = $_GET["date"] ?? date("Y-m-d");
    
    // جلب عدد الطلاب
    $total_result = $con->query("SELECT COUNT(*) as total FROM stud_tb");
    $total_count = $total_result->fetch_assoc()["total"];
    
    // جلب الطلاب
    $students_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name 
                      FROM stud_tb s
                      LEFT JOIN users_tb u ON s.userID = u.id_user
                      ORDER BY s.name
                      LIMIT 50";
    
    $students_result = $con->query($students_query);
    $students = [];
    
    if ($students_result) {
        while ($student = $students_result->fetch_assoc()) {
            // جلب الحضور
            $att_query = "SELECT stat_stud, data_stat FROM stat WHERE id_stud = ? AND DATE(data_stat) = ? LIMIT 1";
            $att_stmt = $con->prepare($att_query);
            $attendance = null;
            
            if ($att_stmt) {
                $att_stmt->bind_param("is", $student["id"], $selected_date);
                $att_stmt->execute();
                $att_result = $att_stmt->get_result();
                $attendance = $att_result->fetch_assoc();
            }
            
            $students[] = [
                "id" => $student["id"],
                "name" => $student["name"],
                "catg" => $student["catg"],
                "p_name" => $student["p_name"],
                "user_name" => $student["user_name"] ?? "غير محدد",
                "attendance" => $attendance ? [
                    "status" => $attendance["stat_stud"],
                    "check_in_time" => "08:00",
                    "data_stat" => $attendance["data_stat"]
                ] : null
            ];
        }
    }
    
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode([
        "students" => $students,
        "total_count" => $total_count,
        "selected_date" => $selected_date,
        "success" => true
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode([
        "error" => "خطأ: " . $e->getMessage(),
        "success" => false
    ], JSON_UNESCAPED_UNICODE);
}

$con->close();
?>';

if (file_put_contents('student_attendance_data_simple.php', $simple_api_content)) {
    echo "<p class='success'>✅ تم إنشاء ملف API مبسط: student_attendance_data_simple.php</p>";
} else {
    echo "<p class='error'>❌ فشل في إنشاء ملف API مبسط</p>";
}
echo "</div>";

// 4. اختبار API المبسط
echo "<div class='box'>";
echo "<h3>4. اختبار API المبسط:</h3>";

$test_url = "student_attendance_data_simple.php?date=" . date('Y-m-d');
echo "<p><a href='$test_url' target='_blank'>🔗 اختبار API المبسط</a></p>";

echo "<button onclick='testSimpleAPI()'>اختبار مباشر</button>";
echo "<div id='simple-api-result'></div>";

echo "<script>
function testSimpleAPI() {
    const resultDiv = document.getElementById('simple-api-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    fetch('$test_url')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    resultDiv.innerHTML = '<p style=\"color: green;\">✅ API يعمل بنجاح!</p><p>عدد الطلاب: ' + data.students.length + '</p>';
                } else {
                    resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ: ' + data.error + '</p>';
                }
            } catch (e) {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في JSON</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في الشبكة: ' + error.message + '</p>';
        });
}
</script>";
echo "</div>";

// 5. إنشاء صفحة عمل مضمونة
echo "<div class='box'>";
echo "<h3>5. إنشاء صفحة عمل مضمونة:</h3>";

$working_page_content = '<?php
session_start();
if (!isset($_SESSION["user"]) || $_SESSION["user"]->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

include "addon/dbcon.php";

$selected_date = $_GET["date"] ?? date("Y-m-d");
$attendance_data = [];

if (isset($_GET["show"])) {
    $query = "SELECT s.name, s.catg, s.p_name, u.user_name, stat.stat_stud, stat.data_stat
              FROM stud_tb s
              LEFT JOIN users_tb u ON s.userID = u.id_user
              INNER JOIN stat ON s.id = stat.id_stud
              WHERE DATE(stat.data_stat) = ?
              ORDER BY s.name";
    
    $stmt = $con->prepare($query);
    if ($stmt) {
        $stmt->bind_param("s", $selected_date);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $attendance_data[] = $row;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>حضور الطلاب - نسخة مضمونة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .search-box { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
        th { background: #f8f9fa; }
        .status-present { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; }
        .status-absent { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; }
        .status-late { background: #ffc107; color: black; padding: 5px 10px; border-radius: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>حضور الطلاب - نسخة مضمونة</h1>
        </div>
        
        <div class="search-box">
            <form method="GET">
                <div class="row">
                    <div class="col-md-4">
                        <label>التاريخ:</label>
                        <input type="date" name="date" class="form-control" value="<?= $selected_date ?>">
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label><br>
                        <button type="submit" name="show" class="btn btn-primary">عرض الحضور</button>
                    </div>
                </div>
            </form>
        </div>
        
        <?php if (!empty($attendance_data)): ?>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>اسم الطالب</th>
                    <th>الصف</th>
                    <th>ولي الأمر</th>
                    <th>المستخدم</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($attendance_data as $index => $record): ?>
                <tr>
                    <td><?= $index + 1 ?></td>
                    <td><?= htmlspecialchars($record["name"]) ?></td>
                    <td><?= htmlspecialchars($record["catg"]) ?></td>
                    <td><?= htmlspecialchars($record["p_name"]) ?></td>
                    <td><?= htmlspecialchars($record["user_name"] ?? "غير محدد") ?></td>
                    <td>
                        <span class="status-<?= $record["stat_stud"] == "حاضر" ? "present" : ($record["stat_stud"] == "غائب" ? "absent" : "late") ?>">
                            <?= htmlspecialchars($record["stat_stud"]) ?>
                        </span>
                    </td>
                    <td><?= date("d/m/Y", strtotime($record["data_stat"])) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <p><strong>إجمالي السجلات: <?= count($attendance_data) ?></strong></p>
        <?php elseif (isset($_GET["show"])): ?>
        <div class="alert alert-info">لا توجد سجلات حضور للتاريخ المحدد</div>
        <?php endif; ?>
    </div>
</body>
</html>';

if (file_put_contents('attandec_working.php', $working_page_content)) {
    echo "<p class='success'>✅ تم إنشاء صفحة عمل مضمونة: attandec_working.php</p>";
    echo "<p><a href='attandec_working.php' target='_blank'>🔗 فتح الصفحة المضمونة</a></p>";
} else {
    echo "<p class='error'>❌ فشل في إنشاء الصفحة المضمونة</p>";
}
echo "</div>";

echo "<div class='box info'>";
echo "<h3>الخلاصة والحلول:</h3>";
echo "<ol>";
echo "<li><strong>الصفحة المضمونة:</strong> <a href='attandec_working.php'>attandec_working.php</a> - تعمل بدون JavaScript</li>";
echo "<li><strong>الصفحة المبسطة:</strong> <a href='attandec_simple.php'>attandec_simple.php</a> - نسخة مبسطة من التصميم الجديد</li>";
echo "<li><strong>API مبسط:</strong> <a href='student_attendance_data_simple.php'>student_attendance_data_simple.php</a> - API مبسط للاختبار</li>";
echo "<li><strong>الصفحة الأصلية:</strong> <a href='attandec.php'>attandec.php</a> - النسخة المحدثة مع التشخيص المحسن</li>";
echo "</ol>";
echo "<p><strong>ملاحظة:</strong> إذا كانت الصفحة المضمونة تعمل، فالمشكلة في JavaScript أو AJAX. إذا لم تعمل، فالمشكلة في قاعدة البيانات أو PHP.</p>";
echo "</div>";
?>
