<?php
// تحسين الاتصال بقاعدة البيانات
$user = "kidzrcle_rwda";
$pass = "kidzrcle_rwda";
$host = "localhost";
$database = "kidzrcle_rwda";

$con = new mysqli($host, $user, $pass, $database);

// التحقق من الاتصال
if ($con->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $con->connect_error);
}

// تحديد ترميز UTF-8
$con->set_charset("utf8");

// التحقق من وجود المعاملات
if (!isset($_GET['useratt']) || !isset($_GET['dateatt'])) {
    die("معاملات مفقودة");
}

$user2 = $_GET['useratt'];
$date2 = $_GET['dateatt'];

// استعلام محسن مع معالجة الأخطاء وإضافة حالة الحضور
$sql = "SELECT users_tb.user_name, stud_tb.name, stud_tb.p_name, stud_tb.catg,
                stat.data_stat, stat.stat_stud, stud_pay.date_exp, stud_pay.cash_stud
        FROM users_tb
        INNER JOIN stud_tb ON users_tb.id_user = stud_tb.userID
        INNER JOIN stat ON stud_tb.id = stat.id_stud
        LEFT JOIN stud_pay ON stud_tb.id = stud_pay.id_stud
        WHERE users_tb.id_user = ? AND stat.data_stat = ?";

// استخدام prepared statements لمنع SQL injection
$stmt = $con->prepare($sql);
if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $con->error);
}

$stmt->bind_param("is", $user2, $date2);
$stmt->execute();
$result = $stmt->get_result();

if (!$result) {
    die("خطأ في تنفيذ الاستعلام: " . $con->error);
}

$fileName = "حضور_الطلاب_" . date("Y-m-d") . ".csv";

// تحديد headers للتحميل مع دعم أفضل للعربية
header("Content-Type: application/vnd.ms-excel; charset=utf-8");
header("Content-Disposition: attachment; filename=\"$fileName\"");
header("Pragma: no-cache");
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");

// إنشاء output stream
$output = fopen('php://output', 'w');

// إضافة BOM للدعم الكامل للعربية في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// إضافة معلومات التقرير
fputcsv($output, ['تقرير حضور الطلاب', '', '', '', '', '', '', '', '']);
fputcsv($output, ['التاريخ: ' . $date2, '', '', '', '', '', '', '', '']);
fputcsv($output, ['تاريخ التصدير: ' . date('Y-m-d H:i:s'), '', '', '', '', '', '', '', '']);
fputcsv($output, ['', '', '', '', '', '', '', '', '']); // سطر فارغ

// كتابة رؤوس الأعمدة مع ترتيب منطقي
$headers = [
    'اسم الطالب',
    'اسم ولي الأمر',
    'صنف الدراسة',
    'تاريخ الحضور',
    'حالة الحضور',
    'اسم المستخدم',
    'قيمة الاشتراك',
    'تاريخ انتهاء الاشتراك',
    'حالة الاشتراك'
];
fputcsv($output, $headers);

// التحقق من وجود نتائج
if ($result->num_rows == 0) {
    fputcsv($output, ['لا توجد بيانات للتاريخ والمستخدم المحددين', '', '', '', '', '', '', '', '']);
} else {
    // جلب البيانات
    while ($row = $result->fetch_assoc()) {
        // تحديد حالة الاشتراك
        $subscription_status = 'غير محدد';
        if (!empty($row['date_exp'])) {
            $expiry_date = new DateTime($row['date_exp']);
            $current_date = new DateTime();

            if ($expiry_date > $current_date) {
                $subscription_status = 'نشط';
            } else {
                $subscription_status = 'منتهي';
            }
        }

        // تحديد حالة الحضور بشكل أوضح
        $attendance_status = 'غير محدد';
        if (!empty($row['stat_stud'])) {
            switch($row['stat_stud']) {
                case '1':
                case 'حاضر':
                    $attendance_status = 'حاضر';
                    break;
                case '0':
                case 'غائب':
                    $attendance_status = 'غائب';
                    break;
                case 'إجازة':
                    $attendance_status = 'إجازة';
                    break;
                default:
                    $attendance_status = $row['stat_stud'];
            }
        }

        // تنسيق قيمة الاشتراك
        $cash_formatted = !empty($row['cash_stud']) ? number_format($row['cash_stud']) . ' IQD' : 'غير محدد';

        // تنسيق تاريخ انتهاء الاشتراك
        $expiry_formatted = !empty($row['date_exp']) ? $row['date_exp'] : 'غير محدد';

        $rowData = [
            $row['name'] ?? 'غير محدد',
            $row['p_name'] ?? 'غير محدد',
            $row['catg'] ?? 'غير محدد',
            $row['data_stat'] ?? 'غير محدد',
            $attendance_status,
            $row['user_name'] ?? 'غير محدد',
            $cash_formatted,
            $expiry_formatted,
            $subscription_status
        ];

        fputcsv($output, $rowData);
    }
}

fclose($output);
$stmt->close();
$con->close();
exit();
?>