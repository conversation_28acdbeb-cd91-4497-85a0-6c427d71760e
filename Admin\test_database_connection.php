<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/topbar.php";
include "addon/dbcon.php";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 50px auto;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .success-box {
            background: #f0fdf4;
            border: 1px solid #10b981;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #065f46;
        }
        
        .error-box {
            background: #fef2f2;
            border: 1px solid #ef4444;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #991b1b;
        }
        
        .info-box {
            background: #f0f9ff;
            border: 1px solid #3b82f6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #1e40af;
        }
        
        .code-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .test-button {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: linear-gradient(45deg, #c82333, #e8690b);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table th, table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        
        table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-ok {
            color: #10b981;
            font-weight: bold;
        }
        
        .status-error {
            color: #ef4444;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔍 اختبار الاتصال بقاعدة البيانات</h1>
            <p>فحص شامل لقاعدة البيانات والحقول الجديدة</p>
        </div>

        <div class="test-card">
            <h3>📊 اختبار الاتصال بقاعدة البيانات</h3>
            <?php
            if ($con) {
                echo '<div class="success-box">';
                echo '<h5><i class="fas fa-check-circle"></i> نجح الاتصال بقاعدة البيانات!</h5>';
                echo '<p>تم الاتصال بقاعدة البيانات بنجاح.</p>';
                echo '</div>';
            } else {
                echo '<div class="error-box">';
                echo '<h5><i class="fas fa-times-circle"></i> فشل الاتصال بقاعدة البيانات!</h5>';
                echo '<p>خطأ: ' . mysqli_connect_error() . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-card">
            <h3>🗄️ فحص هيكل جدول الطلاب</h3>
            <?php
            $table_check = mysqli_query($con, "DESCRIBE stud_tb");
            if ($table_check) {
                echo '<div class="success-box">';
                echo '<h5><i class="fas fa-table"></i> هيكل جدول stud_tb:</h5>';
                echo '<table>';
                echo '<tr><th>اسم الحقل</th><th>نوع البيانات</th><th>يمكن أن يكون فارغ</th><th>القيمة الافتراضية</th></tr>';
                
                $health_status_exists = false;
                $registration_status_exists = false;
                
                while ($row = mysqli_fetch_assoc($table_check)) {
                    echo '<tr>';
                    echo '<td>' . $row['Field'] . '</td>';
                    echo '<td>' . $row['Type'] . '</td>';
                    echo '<td>' . $row['Null'] . '</td>';
                    echo '<td>' . ($row['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                    
                    if ($row['Field'] == 'health_status') $health_status_exists = true;
                    if ($row['Field'] == 'registration_status') $registration_status_exists = true;
                }
                echo '</table>';
                echo '</div>';
                
                // فحص الحقول الجديدة
                echo '<div class="info-box">';
                echo '<h5><i class="fas fa-search"></i> فحص الحقول الجديدة:</h5>';
                echo '<p><strong>health_status:</strong> <span class="' . ($health_status_exists ? 'status-ok">موجود ✓' : 'status-error">غير موجود ✗') . '</span></p>';
                echo '<p><strong>registration_status:</strong> <span class="' . ($registration_status_exists ? 'status-ok">موجود ✓' : 'status-error">غير موجود ✗') . '</span></p>';
                echo '</div>';
                
                if (!$health_status_exists || !$registration_status_exists) {
                    echo '<div class="error-box">';
                    echo '<h5><i class="fas fa-exclamation-triangle"></i> الحقول الجديدة غير موجودة!</h5>';
                    echo '<p>يجب تشغيل كود SQL التالي لإضافة الحقول المفقودة:</p>';
                    echo '<div class="code-box">';
                    if (!$health_status_exists) {
                        echo "ALTER TABLE `stud_tb` ADD `health_status` TEXT NULL DEFAULT NULL COMMENT 'الحالة الصحية للطالب' AFTER `student_status`;<br>";
                    }
                    if (!$registration_status_exists) {
                        echo "ALTER TABLE `stud_tb` ADD `registration_status` VARCHAR(50) NULL DEFAULT 'تسجيل جديد' COMMENT 'حالة التسجيل' AFTER `health_status`;";
                    }
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<div class="error-box">';
                echo '<h5><i class="fas fa-times-circle"></i> خطأ في فحص الجدول!</h5>';
                echo '<p>خطأ: ' . mysqli_error($con) . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-card">
            <h3>👥 فحص جدول المستخدمين</h3>
            <?php
            $users_query = "SELECT id_user, user_name, role FROM users_tb ORDER BY id_user";
            $users_result = mysqli_query($con, $users_query);
            
            if ($users_result) {
                echo '<div class="success-box">';
                echo '<h5><i class="fas fa-users"></i> قائمة المستخدمين:</h5>';
                echo '<table>';
                echo '<tr><th>معرف المستخدم</th><th>اسم المستخدم</th><th>الدور</th><th>عدد الطلاب</th></tr>';
                
                while ($user = mysqli_fetch_assoc($users_result)) {
                    // عد الطلاب لكل مستخدم
                    $student_count_query = "SELECT COUNT(*) as count FROM stud_tb WHERE userID = " . $user['id_user'];
                    $student_count_result = mysqli_query($con, $student_count_query);
                    $student_count = $student_count_result ? mysqli_fetch_assoc($student_count_result)['count'] : 0;
                    
                    echo '<tr>';
                    echo '<td>' . $user['id_user'] . '</td>';
                    echo '<td>' . $user['user_name'] . '</td>';
                    echo '<td>' . $user['role'] . '</td>';
                    echo '<td>' . $student_count . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                echo '</div>';
            } else {
                echo '<div class="error-box">';
                echo '<h5><i class="fas fa-times-circle"></i> خطأ في جلب المستخدمين!</h5>';
                echo '<p>خطأ: ' . mysqli_error($con) . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-card">
            <h3>🧪 اختبار الاستعلام الرئيسي</h3>
            <?php
            // اختبار الاستعلام مع أول مستخدم
            $first_user_query = "SELECT id_user FROM users_tb WHERE role = 'User' LIMIT 1";
            $first_user_result = mysqli_query($con, $first_user_query);
            
            if ($first_user_result && mysqli_num_rows($first_user_result) > 0) {
                $first_user = mysqli_fetch_assoc($first_user_result);
                $test_user_id = $first_user['id_user'];
                
                echo '<div class="info-box">';
                echo '<h5><i class="fas fa-flask"></i> اختبار الاستعلام مع المستخدم ID: ' . $test_user_id . '</h5>';
                
                $test_sql = "SELECT stud_tb.*, stud_pay.*, users_tb.user_name,
                            IFNULL(stud_tb.health_status, 'لا توجد ملاحظات') as health_status,
                            IFNULL(stud_tb.registration_status, 'تسجيل جديد') as registration_status
                            FROM stud_tb 
                            INNER JOIN stud_pay ON stud_pay.id_stud = stud_tb.id 
                            INNER JOIN users_tb ON stud_tb.userID = users_tb.id_user 
                            WHERE users_tb.id_user = $test_user_id 
                            ORDER BY stud_tb.datein DESC LIMIT 5";
                
                $test_result = mysqli_query($con, $test_sql);
                
                if ($test_result) {
                    $row_count = mysqli_num_rows($test_result);
                    echo '<p class="status-ok">✓ نجح الاستعلام! عدد النتائج: ' . $row_count . '</p>';
                    
                    if ($row_count > 0) {
                        echo '<table>';
                        echo '<tr><th>اسم الطالب</th><th>العمر</th><th>الصنف</th><th>الحالة الصحية</th><th>حالة التسجيل</th></tr>';
                        while ($student = mysqli_fetch_assoc($test_result)) {
                            echo '<tr>';
                            echo '<td>' . $student['name'] . '</td>';
                            echo '<td>' . $student['age'] . '</td>';
                            echo '<td>' . $student['catg'] . '</td>';
                            echo '<td>' . substr($student['health_status'], 0, 30) . '...</td>';
                            echo '<td>' . $student['registration_status'] . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                    } else {
                        echo '<p>لا توجد بيانات طلاب لهذا المستخدم.</p>';
                    }
                } else {
                    echo '<p class="status-error">✗ فشل الاستعلام: ' . mysqli_error($con) . '</p>';
                    echo '<div class="code-box">' . $test_sql . '</div>';
                }
                echo '</div>';
            } else {
                echo '<div class="error-box">';
                echo '<h5><i class="fas fa-exclamation-triangle"></i> لا توجد مستخدمين!</h5>';
                echo '<p>لا يمكن اختبار الاستعلام بدون مستخدمين.</p>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-card">
            <h3>🔗 روابط الاختبار</h3>
            <div style="text-align: center;">
                <a href="infost.php" class="test-button" target="_blank">
                    <i class="fas fa-users"></i>
                    اختبار صفحة الطلاب
                </a>
                <a href="addstud.php" class="test-button" target="_blank">
                    <i class="fas fa-user-plus"></i>
                    اختبار إضافة طالب
                </a>
                <a href="../add_new_fields.sql" class="test-button" target="_blank">
                    <i class="fas fa-database"></i>
                    ملف SQL
                </a>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/all.min.js"></script>
</body>
</html>
