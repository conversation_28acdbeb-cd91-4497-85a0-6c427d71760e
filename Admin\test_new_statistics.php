<?php
session_start();

// تحقق من صلاحية المستخدم
if(!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin"){
    echo "غير مصرح لك بالوصول لهذه الصفحة";
    exit();
}

include "addon/dbcon.php";

echo "<h2>اختبار الإحصائيات الجديدة</h2>";

// اختبار الاستعلامات
$search = 1; // افتراض أن المستخدم رقم 1 موجود
$datenow = date('Y-m-d');

echo "<h3>التاريخ الحالي: $datenow</h3>";
echo "<h3>اختبار المستخدم رقم: $search</h3>";

// 1. عدد الطلاب الفعالين
$sql_active = "SELECT COUNT(*) as active_count FROM stud_tb, stud_pay, users_tb 
              WHERE stud_tb.id = stud_pay.id_stud 
              AND users_tb.id_user = stud_tb.userID 
              AND users_tb.id_user = $search
              AND DATEDIFF(stud_pay.date_exp, '$datenow') > 10";
$result_active = mysqli_query($con, $sql_active);
if ($result_active) {
    $active_count = mysqli_fetch_assoc($result_active)['active_count'];
    echo "<p style='color: green;'>✅ الطلاب الفعالين: $active_count</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في استعلام الطلاب الفعالين: " . mysqli_error($con) . "</p>";
}

// 2. عدد الطلاب المنتهي اشتراكهم
$sql_expired = "SELECT COUNT(*) as expired_count FROM stud_tb, stud_pay, users_tb 
               WHERE stud_tb.id = stud_pay.id_stud 
               AND users_tb.id_user = stud_tb.userID 
               AND users_tb.id_user = $search
               AND DATEDIFF(stud_pay.date_exp, '$datenow') <= 0";
$result_expired = mysqli_query($con, $sql_expired);
if ($result_expired) {
    $expired_count = mysqli_fetch_assoc($result_expired)['expired_count'];
    echo "<p style='color: red;'>🔴 المنتهي اشتراكهم: $expired_count</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في استعلام المنتهي اشتراكهم: " . mysqli_error($con) . "</p>";
}

// 3. عدد الطلاب قريب الانتهاء
$sql_soon = "SELECT COUNT(*) as soon_count FROM stud_tb, stud_pay, users_tb 
            WHERE stud_tb.id = stud_pay.id_stud 
            AND users_tb.id_user = stud_tb.userID 
            AND users_tb.id_user = $search
            AND DATEDIFF(stud_pay.date_exp, '$datenow') BETWEEN 1 AND 10";
$result_soon = mysqli_query($con, $sql_soon);
if ($result_soon) {
    $soon_count = mysqli_fetch_assoc($result_soon)['soon_count'];
    echo "<p style='color: orange;'>🟡 قريب الانتهاء: $soon_count</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في استعلام قريب الانتهاء: " . mysqli_error($con) . "</p>";
}

// 4. عدد الطلاب التسجيل الجديد
$current_month = date('Y-m');
$sql_new = "SELECT COUNT(*) as new_count FROM stud_tb, users_tb 
           WHERE users_tb.id_user = stud_tb.userID 
           AND users_tb.id_user = $search
           AND DATE_FORMAT(stud_tb.datein, '%Y-%m') = '$current_month'";
$result_new = mysqli_query($con, $sql_new);
if ($result_new) {
    $new_count = mysqli_fetch_assoc($result_new)['new_count'];
    echo "<p style='color: blue;'>🔵 التسجيل الجديد (الشهر الحالي): $new_count</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في استعلام التسجيل الجديد: " . mysqli_error($con) . "</p>";
}

// 5. المجموع الكلي
$sql_total = "SELECT COUNT(*) as total_count FROM stud_tb, users_tb 
             WHERE users_tb.id_user = stud_tb.userID 
             AND users_tb.id_user = $search";
$result_total = mysqli_query($con, $sql_total);
if ($result_total) {
    $total_count = mysqli_fetch_assoc($result_total)['total_count'];
    echo "<p style='color: purple;'>🟣 المجموع الكلي: $total_count</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في استعلام المجموع الكلي: " . mysqli_error($con) . "</p>";
}

// اختبار JSON
$statistics = array(
    'active' => $active_count ?? 0,
    'expired' => $expired_count ?? 0,
    'soon' => $soon_count ?? 0,
    'new' => $new_count ?? 0,
    'total' => $total_count ?? 0
);

echo "<h3>البيانات بصيغة JSON:</h3>";
echo "<pre>" . json_encode($statistics, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";

// عرض بعض البيانات التفصيلية
echo "<h3>تفاصيل إضافية:</h3>";
echo "<p>الشهر الحالي: $current_month</p>";

// عرض أسماء المستخدمين المتاحين
$sql_users = "SELECT id_user, user_name FROM users_tb ORDER BY user_name";
$result_users = mysqli_query($con, $sql_users);
if ($result_users) {
    echo "<h4>المستخدمين المتاحين:</h4>";
    echo "<ul>";
    while ($user = mysqli_fetch_assoc($result_users)) {
        echo "<li>ID: {$user['id_user']} - الاسم: {$user['user_name']}</li>";
    }
    echo "</ul>";
}
?>
