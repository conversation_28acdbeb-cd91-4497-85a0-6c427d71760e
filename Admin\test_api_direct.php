<?php
// اختبار مباشر لـ API حضور الطلاب
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار API حضور الطلاب</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>";

// اختبار 1: استدعاء API مباشرة
echo "<h3>1. اختبار API مباشرة:</h3>";

$test_date = date('Y-m-d');
$api_url = "student_attendance_data.php?date=$test_date";

echo "<p>رابط API: <a href='$api_url' target='_blank'>$api_url</a></p>";

// محاولة استدعاء API
ob_start();
include 'student_attendance_data.php';
$api_output = ob_get_clean();

echo "<h4>نتيجة API:</h4>";
echo "<pre>" . htmlspecialchars($api_output) . "</pre>";

// اختبار 2: تحليل JSON
echo "<h3>2. تحليل JSON:</h3>";
$json_data = json_decode($api_output, true);

if ($json_data) {
    echo "<p class='success'>✅ JSON صحيح</p>";
    echo "<h4>محتوى البيانات:</h4>";
    echo "<ul>";
    echo "<li><strong>النجاح:</strong> " . ($json_data['success'] ? 'نعم' : 'لا') . "</li>";
    echo "<li><strong>عدد الطلاب:</strong> " . ($json_data['total_count'] ?? 'غير محدد') . "</li>";
    echo "<li><strong>عدد النتائج:</strong> " . (isset($json_data['students']) ? count($json_data['students']) : 0) . "</li>";
    echo "</ul>";
    
    if (isset($json_data['students']) && count($json_data['students']) > 0) {
        echo "<h4>عينة من الطلاب:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الصف</th><th>ولي الأمر</th><th>المستخدم</th><th>الحضور</th></tr>";
        
        $count = 0;
        foreach ($json_data['students'] as $student) {
            if ($count >= 5) break; // عرض أول 5 فقط
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($student['id']) . "</td>";
            echo "<td>" . htmlspecialchars($student['name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['catg']) . "</td>";
            echo "<td>" . htmlspecialchars($student['p_name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['user_name']) . "</td>";
            echo "<td>" . ($student['attendance'] ? htmlspecialchars($student['attendance']['status']) : 'لا يوجد') . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    }
    
    if (isset($json_data['error'])) {
        echo "<p class='error'>❌ خطأ: " . htmlspecialchars($json_data['error']) . "</p>";
        if (isset($json_data['details'])) {
            echo "<p class='error'>التفاصيل: " . htmlspecialchars($json_data['details']) . "</p>";
        }
    }
} else {
    echo "<p class='error'>❌ JSON غير صحيح</p>";
    echo "<p>خطأ JSON: " . json_last_error_msg() . "</p>";
}

// اختبار 3: اختبار قاعدة البيانات مباشرة
echo "<h3>3. اختبار قاعدة البيانات مباشرة:</h3>";

include "addon/dbcon.php";

if ($con) {
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار عدد الطلاب
    $students_count = $con->query("SELECT COUNT(*) as count FROM stud_tb")->fetch_assoc()['count'];
    echo "<p>عدد الطلاب: <strong>$students_count</strong></p>";
    
    // اختبار عدد سجلات الحضور
    $attendance_count = $con->query("SELECT COUNT(*) as count FROM stat")->fetch_assoc()['count'];
    echo "<p>عدد سجلات الحضور: <strong>$attendance_count</strong></p>";
    
    // اختبار الاستعلام المستخدم في API
    $test_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name 
                   FROM stud_tb s
                   LEFT JOIN users_tb u ON s.userID = u.id_user
                   ORDER BY s.name
                   LIMIT 3";
    
    echo "<h4>اختبار الاستعلام:</h4>";
    echo "<pre>" . htmlspecialchars($test_query) . "</pre>";
    
    $result = $con->query($test_query);
    if ($result) {
        echo "<p class='success'>✅ الاستعلام ناجح</p>";
        echo "<p>عدد النتائج: " . $result->num_rows . "</p>";
        
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>الاسم</th><th>الصف</th><th>ولي الأمر</th><th>المستخدم</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['catg']) . "</td>";
                echo "<td>" . htmlspecialchars($row['p_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['user_name'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ فشل الاستعلام: " . $con->error . "</p>";
    }
} else {
    echo "<p class='error'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

// اختبار 4: اختبار JavaScript
echo "<h3>4. اختبار JavaScript:</h3>";
echo "<button onclick='testFetch()'>اختبار Fetch</button>";
echo "<div id='fetch-result'></div>";

echo "<script>
function testFetch() {
    const resultDiv = document.getElementById('fetch-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    fetch('student_attendance_data.php?date=$test_date')
        .then(response => {
            console.log('Response:', response);
            return response.text();
        })
        .then(text => {
            console.log('Text:', text);
            try {
                const data = JSON.parse(text);
                resultDiv.innerHTML = '<p style=\"color: green;\">✅ Fetch ناجح</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في JSON</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في Fetch: ' + error.message + '</p>';
        });
}
</script>";

echo "<hr>";
echo "<h3>الروابط المفيدة:</h3>";
echo "<ul>";
echo "<li><a href='attandec.php'>الصفحة الرئيسية (المحدثة)</a></li>";
echo "<li><a href='attandec_simple.php'>الصفحة المبسطة</a></li>";
echo "<li><a href='debug_student_attendance.php'>صفحة التشخيص</a></li>";
echo "<li><a href='student_attendance_data.php?date=$test_date'>API مباشرة</a></li>";
echo "</ul>";
?>
