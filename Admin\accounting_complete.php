<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال المباشر بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

// التحقق من الاتصال
if ($con->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $con->connect_error);
}

// حساب الإحصائيات
$stats = [
    'revenue' => 0,
    'expenses' => 0,
    'salaries' => 0,
    'revenue_count' => 0,
    'expenses_count' => 0,
    'salaries_count' => 0
];

// إجمالي الإيرادات
$revenue_query = "SELECT COUNT(*) as count, SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
$revenue_result = mysqli_query($con, $revenue_query);
if ($revenue_result && $row = mysqli_fetch_assoc($revenue_result)) {
    $stats['revenue'] = $row['total'] ? intval($row['total']) : 0;
    $stats['revenue_count'] = $row['count'] ? intval($row['count']) : 0;
}

// إجمالي المصروفات
$expenses_query = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
$expenses_result = mysqli_query($con, $expenses_query);
if ($expenses_result && $row = mysqli_fetch_assoc($expenses_result)) {
    $stats['expenses'] = $row['total'] ? intval($row['total']) : 0;
    $stats['expenses_count'] = $row['count'] ? intval($row['count']) : 0;
}

// إجمالي الرواتب
$salaries_query = "SELECT COUNT(*) as count, SUM(salary) as total FROM employ_tb WHERE salary > 0";
$salaries_result = mysqli_query($con, $salaries_query);
if ($salaries_result && $row = mysqli_fetch_assoc($salaries_result)) {
    $stats['salaries'] = $row['total'] ? intval($row['total']) : 0;
    $stats['salaries_count'] = $row['count'] ? intval($row['count']) : 0;
}

// حساب صافي الربح
$net_profit = $stats['revenue'] - $stats['expenses'] - $stats['salaries'];

// جلب المستخدمين
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = mysqli_query($con, $users_query);

// معالجة الفلاتر
$selected_type = isset($_GET['type']) ? $_GET['type'] : 'all';
$selected_user = isset($_GET['user_id']) ? $_GET['user_id'] : '0';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.revenue { border-left-color: #28a745; }
        .stat-card.expenses { border-left-color: #dc3545; }
        .stat-card.salaries { border-left-color: #ffc107; }
        .stat-card.profit { border-left-color: #007bff; }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .filters-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .data-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table th {
            background: #343a40;
            color: white;
            border: none;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-calculator"></i> نظام المحاسبة الشامل</h1>
            <p class="mb-0">إدارة متكاملة للحسابات والمالية</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card revenue">
                <i class="fas fa-arrow-up" style="font-size: 3rem; color: #28a745; margin-bottom: 15px;"></i>
                <div class="stat-value text-success"><?= number_format($stats['revenue']) ?></div>
                <div class="stat-title">إجمالي الإيرادات</div>
                <small><?= $stats['revenue_count'] ?> معاملة</small>
            </div>
            
            <div class="stat-card expenses">
                <i class="fas fa-arrow-down" style="font-size: 3rem; color: #dc3545; margin-bottom: 15px;"></i>
                <div class="stat-value text-danger"><?= number_format($stats['expenses']) ?></div>
                <div class="stat-title">إجمالي المصروفات</div>
                <small><?= $stats['expenses_count'] ?> معاملة</small>
            </div>
            
            <div class="stat-card salaries">
                <i class="fas fa-users" style="font-size: 3rem; color: #ffc107; margin-bottom: 15px;"></i>
                <div class="stat-value text-warning"><?= number_format($stats['salaries']) ?></div>
                <div class="stat-title">إجمالي الرواتب</div>
                <small><?= $stats['salaries_count'] ?> موظف</small>
            </div>
            
            <div class="stat-card profit">
                <i class="fas fa-chart-line" style="font-size: 3rem; color: #007bff; margin-bottom: 15px;"></i>
                <div class="stat-value <?= $net_profit >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($net_profit) ?></div>
                <div class="stat-title">صافي الربح</div>
                <small>دينار عراقي</small>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h5><i class="fas fa-filter"></i> فلاتر البحث والتصفية</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع البيانات:</label>
                    <select name="type" class="form-select">
                        <option value="all" <?= $selected_type == 'all' ? 'selected' : '' ?>>جميع البيانات</option>
                        <option value="revenue" <?= $selected_type == 'revenue' ? 'selected' : '' ?>>الإيرادات</option>
                        <option value="expenses" <?= $selected_type == 'expenses' ? 'selected' : '' ?>>المصروفات</option>
                        <option value="salaries" <?= $selected_type == 'salaries' ? 'selected' : '' ?>>الرواتب</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">المستخدم:</label>
                    <select name="user_id" class="form-select">
                        <option value="0">جميع المستخدمين</option>
                        <?php
                        if ($users_result && mysqli_num_rows($users_result) > 0) {
                            while ($user = mysqli_fetch_assoc($users_result)) {
                                $selected = ($selected_user == $user['id_user']) ? 'selected' : '';
                                echo '<option value="' . $user['id_user'] . '" ' . $selected . '>' . htmlspecialchars($user['user_name']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">من تاريخ:</label>
                    <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ:</label>
                    <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-custom w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- Data Display -->
        <div class="data-section">
            <?php
            // عرض البيانات حسب النوع المحدد
            if ($selected_type == 'all') {
                // عرض ملخص لجميع البيانات
                ?>
                <h5><i class="fas fa-chart-bar"></i> عرض شامل للمعاملات المالية</h5>
                
                <div class="row">
                    <!-- آخر الإيرادات -->
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-arrow-up"></i> آخر الإيرادات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $user_condition = ($selected_user != '0') ? " AND stud_tb.userID = '$selected_user'" : "";
                                $recent_revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name 
                                                        FROM stud_tb, stud_pay, users_tb 
                                                        WHERE stud_pay.id_stud = stud_tb.id 
                                                        AND stud_tb.userID = users_tb.id_user 
                                                        $user_condition 
                                                        ORDER BY stud_pay.datein DESC LIMIT 5";
                                $recent_revenue_result = mysqli_query($con, $recent_revenue_query);
                                
                                if ($recent_revenue_result && mysqli_num_rows($recent_revenue_result) > 0) {
                                    while ($item = mysqli_fetch_assoc($recent_revenue_result)) {
                                        echo '<div class="mb-2 p-2 border-start border-success border-3 bg-light">';
                                        echo '<strong>' . htmlspecialchars($item['name']) . '</strong><br>';
                                        echo '<span class="text-success">IQD ' . number_format($item['cash_stud']) . '</span><br>';
                                        echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['datein'])) . '</small>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<p class="text-muted text-center">لا توجد إيرادات</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- آخر المصروفات -->
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0"><i class="fas fa-arrow-down"></i> آخر المصروفات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $user_condition = ($selected_user != '0') ? " AND depit_tb.userID = '$selected_user'" : "";
                                $recent_expenses_query = "SELECT depit_tb.depit_note, depit_tb.depit_cash, depit_tb.depit_date, users_tb.user_name 
                                                         FROM users_tb, depit_tb 
                                                         WHERE depit_tb.userID = users_tb.id_user 
                                                         $user_condition 
                                                         ORDER BY depit_tb.depit_date DESC LIMIT 5";
                                $recent_expenses_result = mysqli_query($con, $recent_expenses_query);
                                
                                if ($recent_expenses_result && mysqli_num_rows($recent_expenses_result) > 0) {
                                    while ($item = mysqli_fetch_assoc($recent_expenses_result)) {
                                        echo '<div class="mb-2 p-2 border-start border-danger border-3 bg-light">';
                                        echo '<strong>' . htmlspecialchars($item['depit_note']) . '</strong><br>';
                                        echo '<span class="text-danger">IQD ' . number_format($item['depit_cash']) . '</span><br>';
                                        echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['depit_date'])) . '</small>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<p class="text-muted text-center">لا توجد مصروفات</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- آخر الرواتب -->
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-users"></i> آخر الرواتب</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $user_condition = ($selected_user != '0') ? " AND employ_tb.userID = '$selected_user'" : "";
                                $recent_salaries_query = "SELECT employ_tb.f_name, employ_tb.job, employ_tb.salary, employ_tb.date_start, users_tb.user_name 
                                                         FROM users_tb, employ_tb 
                                                         WHERE employ_tb.userID = users_tb.id_user 
                                                         $user_condition 
                                                         ORDER BY employ_tb.date_start DESC LIMIT 5";
                                $recent_salaries_result = mysqli_query($con, $recent_salaries_query);
                                
                                if ($recent_salaries_result && mysqli_num_rows($recent_salaries_result) > 0) {
                                    while ($item = mysqli_fetch_assoc($recent_salaries_result)) {
                                        echo '<div class="mb-2 p-2 border-start border-warning border-3 bg-light">';
                                        echo '<strong>' . htmlspecialchars($item['f_name']) . '</strong><br>';
                                        echo '<small>' . htmlspecialchars($item['job']) . '</small><br>';
                                        echo '<span class="text-warning">IQD ' . number_format($item['salary']) . '</span>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<p class="text-muted text-center">لا توجد رواتب</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
            } elseif ($selected_type == 'revenue') {
                // عرض الإيرادات المفصلة
                ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-arrow-up"></i> الإيرادات المفصلة</h5>
                </div>

                <?php
                $user_condition = ($selected_user != '0') ? " AND stud_tb.userID = '$selected_user'" : "";
                $date_condition = "";
                if ($date_from && $date_to) {
                    $date_condition = " AND DATE(stud_tb.datein) BETWEEN '$date_from' AND '$date_to'";
                }

                $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name, stud_pay.id_pay
                                  FROM stud_tb, stud_pay, users_tb
                                  WHERE stud_pay.id_stud = stud_tb.id
                                  AND stud_tb.userID = users_tb.id_user
                                  $user_condition $date_condition
                                  ORDER BY stud_pay.datein DESC";

                $revenue_result = mysqli_query($con, $revenue_query);

                if ($revenue_result && mysqli_num_rows($revenue_result) > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-child"></i> اسم الطالب</th>';
                    echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                    echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                    echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                    echo '<th><i class="fas fa-receipt"></i> رقم الوصل</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = mysqli_fetch_assoc($revenue_result)) {
                        $total += $row['cash_stud'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['name']) . '</strong></td>';
                        echo '<td><span class="badge bg-success">IQD ' . number_format($row['cash_stud']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '<td><span class="badge bg-info">' . $row['id_pay'] . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-success">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="3">' . mysqli_num_rows($revenue_result) . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد إيرادات</h5>';
                    echo '<p>لم يتم العثور على إيرادات للفترة المحددة</p>';
                    echo '</div>';
                }

            } elseif ($selected_type == 'expenses') {
                // عرض المصروفات المفصلة
                ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-arrow-down"></i> المصروفات المفصلة</h5>
                </div>

                <?php
                $user_condition = ($selected_user != '0') ? " AND depit_tb.userID = '$selected_user'" : "";
                $date_condition = "";
                if ($date_from && $date_to) {
                    $date_condition = " AND DATE(depit_tb.depit_date) BETWEEN '$date_from' AND '$date_to'";
                }

                $expenses_query = "SELECT depit_tb.*, users_tb.user_name
                                   FROM users_tb, depit_tb
                                   WHERE depit_tb.userID = users_tb.id_user
                                   $user_condition $date_condition
                                   ORDER BY depit_tb.depit_date DESC";

                $expenses_result = mysqli_query($con, $expenses_query);

                if ($expenses_result && mysqli_num_rows($expenses_result) > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-sticky-note"></i> وصف المصروف</th>';
                    echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                    echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                    echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = mysqli_fetch_assoc($expenses_result)) {
                        $total += $row['depit_cash'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['depit_note']) . '</strong></td>';
                        echo '<td><span class="badge bg-danger">IQD ' . number_format($row['depit_cash']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-danger">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="2">' . mysqli_num_rows($expenses_result) . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد مصروفات</h5>';
                    echo '<p>لم يتم العثور على مصروفات للفترة المحددة</p>';
                    echo '</div>';
                }

            } elseif ($selected_type == 'salaries') {
                // عرض الرواتب المفصلة
                ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-users"></i> الرواتب المفصلة</h5>
                </div>

                <?php
                $user_condition = ($selected_user != '0') ? " AND employ_tb.userID = '$selected_user'" : "";

                $salaries_query = "SELECT employ_tb.*, users_tb.user_name
                                   FROM users_tb, employ_tb
                                   WHERE employ_tb.userID = users_tb.id_user
                                   $user_condition
                                   ORDER BY employ_tb.date_start DESC";

                $salaries_result = mysqli_query($con, $salaries_query);

                if ($salaries_result && mysqli_num_rows($salaries_result) > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-user"></i> اسم الموظف</th>';
                    echo '<th><i class="fas fa-briefcase"></i> المنصب</th>';
                    echo '<th><i class="fas fa-money-bill"></i> الراتب</th>';
                    echo '<th><i class="fas fa-calendar"></i> تاريخ البداية</th>';
                    echo '<th><i class="fas fa-user-tie"></i> المستخدم</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = mysqli_fetch_assoc($salaries_result)) {
                        $total += $row['salary'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['f_name']) . '</strong></td>';
                        echo '<td>' . htmlspecialchars($row['job']) . '</td>';
                        echo '<td><span class="badge bg-warning text-dark">IQD ' . number_format($row['salary']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['date_start'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-warning">';
                    echo '<th colspan="2">الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="2">' . mysqli_num_rows($salaries_result) . ' موظف</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد رواتب</h5>';
                    echo '<p>لم يتم العثور على رواتب</p>';
                    echo '</div>';
                }
            }
            ?>
        </div>

        <!-- Back to Home Button -->
        <div class="text-center mt-4">
            <a href="home.php" class="btn btn-secondary btn-custom">
                <i class="fas fa-home"></i> العودة إلى الصفحة الرئيسية
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
mysqli_close($con);
?>
