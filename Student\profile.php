<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

$student = $_SESSION['student'];
$message = '';
$messageType = '';

// جلب معلومات الطالب المفصلة
$sql = "SELECT s.*, sp.date_exp, sp.cash_stud, u.user_name 
        FROM stud_tb s 
        LEFT JOIN stud_pay sp ON s.id = sp.id_stud 
        LEFT JOIN users_tb u ON s.userID = u.id_user 
        WHERE s.id = " . $student->id;
$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $student_info = mysqli_fetch_assoc($result);
} else {
    $student_info = [
        'name' => $student->name,
        'age' => $student->age ?? 'غير محدد',
        'sex' => $student->sex ?? 'غير محدد',
        'catg' => $student->catg ?? 'غير محدد',
        'p_name' => $student->p_name ?? 'غير محدد',
        'p_phone' => $student->p_phone ?? 'غير محدد',
        'loc' => 'غير محدد',
        'datein' => 'غير محدد',
        'date_exp' => null,
        'cash_stud' => null,
        'user_name' => 'غير محدد'
    ];
}

// حساب حالة الاشتراك
$subscription_status = 'غير محدد';
$days_remaining = 0;
if (!empty($student_info['date_exp'])) {
    $expiry_date = new DateTime($student_info['date_exp']);
    $current_date = new DateTime();
    $interval = $current_date->diff($expiry_date);
    $days_remaining = $expiry_date > $current_date ? $interval->days : -$interval->days;
    
    if ($days_remaining > 10) {
        $subscription_status = 'نشط';
    } elseif ($days_remaining > 0) {
        $subscription_status = 'قريب الانتهاء';
    } else {
        $subscription_status = 'منتهي';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - <?php echo htmlspecialchars($student->name); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .profile-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .profile-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .profile-header {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #3498db, #2c3e50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }
        
        .profile-name {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .profile-role {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .profile-status {
            display: inline-block;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        
        .status-expired {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .info-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.8rem;
        }
        
        .card-title i {
            color: #3498db;
            font-size: 1.5rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.8rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #3498db;
        }
        
        .info-label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .info-value {
            color: #34495e;
            font-weight: 500;
        }
        
        .subscription-details {
            text-align: center;
            margin-top: 1rem;
        }
        
        .days-remaining {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        
        .days-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .quick-actions {
            grid-column: 1 / -1;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.8rem;
            padding: 1.5rem;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            text-decoration: none;
            border-radius: 15px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .action-btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .action-btn.danger:hover {
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .action-text {
            font-weight: bold;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .profile-header {
                padding: 1.5rem;
            }
            
            .profile-avatar {
                width: 80px;
                height: 80px;
                font-size: 2rem;
            }
            
            .profile-name {
                font-size: 1.5rem;
            }
            
            .info-card {
                padding: 1.5rem;
            }
            
            .info-item {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="profile-section">
        <div class="profile-container">
            <!-- رأس الملف الشخصي -->
            <div class="profile-header animate-in">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h1 class="profile-name"><?php echo htmlspecialchars($student_info['name']); ?></h1>
                <p class="profile-role">طالب في نظام إدارة الحضانة</p>
                <div class="profile-status status-<?php echo $subscription_status == 'نشط' ? 'active' : ($subscription_status == 'قريب الانتهاء' ? 'warning' : 'expired'); ?>">
                    <?php echo $subscription_status; ?>
                </div>
            </div>
            
            <!-- محتوى الملف الشخصي -->
            <div class="profile-content">
                <!-- المعلومات الشخصية -->
                <div class="info-card animate-in">
                    <h2 class="card-title">
                        <i class="fas fa-user-circle"></i>
                        المعلومات الشخصية
                    </h2>
                    
                    <div class="info-item">
                        <span class="info-label">رقم الطالب:</span>
                        <span class="info-value"><?php echo $student->id; ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">العمر:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['age']); ?> سنة</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الجنس:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['sex']); ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الصنف:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['catg']); ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">السكن:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['loc'] ?? 'غير محدد'); ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">تاريخ التسجيل:</span>
                        <span class="info-value"><?php echo date('Y/m/d', strtotime($student_info['datein'])); ?></span>
                    </div>
                </div>
                
                <!-- معلومات ولي الأمر والاشتراك -->
                <div class="info-card animate-in">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i>
                        معلومات ولي الأمر والاشتراك
                    </h2>
                    
                    <div class="info-item">
                        <span class="info-label">اسم ولي الأمر:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['p_name']); ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">رقم الهاتف:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student_info['p_phone']); ?></span>
                    </div>
                    
                    <?php if (!empty($student_info['date_exp'])): ?>
                        <div class="info-item">
                            <span class="info-label">تاريخ انتهاء الاشتراك:</span>
                            <span class="info-value"><?php echo date('Y/m/d', strtotime($student_info['date_exp'])); ?></span>
                        </div>
                        
                        <div class="subscription-details">
                            <div class="days-remaining"><?php echo $days_remaining > 0 ? $days_remaining : 0; ?></div>
                            <div class="days-label">يوم متبقي</div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($student_info['cash_stud'])): ?>
                        <div class="info-item">
                            <span class="info-label">قيمة الاشتراك:</span>
                            <span class="info-value"><?php echo number_format($student_info['cash_stud']); ?> IQD</span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- الإجراءات السريعة -->
                <div class="quick-actions animate-in">
                    <h2 class="card-title">
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h2>
                    
                    <div class="actions-grid">
                        <a href="request_leave.php" class="action-btn">
                            <div class="action-icon">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <span class="action-text">طلب إجازة</span>
                        </a>
                        
                        <a href="my_leaves.php" class="action-btn">
                            <div class="action-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <span class="action-text">إجازاتي</span>
                        </a>
                        
                        <a href="news.php" class="action-btn">
                            <div class="action-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <span class="action-text">الأخبار</span>
                        </a>
                        
                        <a href="logout.php" class="action-btn danger" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <div class="action-icon">
                                <i class="fas fa-sign-out-alt"></i>
                            </div>
                            <span class="action-text">تسجيل الخروج</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات بصرية
            const cards = document.querySelectorAll('.animate-in');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
