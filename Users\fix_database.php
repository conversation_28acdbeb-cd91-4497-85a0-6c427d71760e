<?php
// ملف إصلاح قاعدة البيانات - إضافة أعمدة تعليقات المستخدم
include "addon/dbcon.php";

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><meta charset='UTF-8'><title>إصلاح قاعدة البيانات</title>";
echo "<style>body{font-family:Arial;direction:rtl;margin:20px;background:#f5f5f5;}";
echo ".container{background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;background:#d4edda;padding:10px;border-radius:5px;margin:10px 0;}";
echo ".error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:5px;margin:10px 0;}";
echo "h1{color:#007bff;}</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح قاعدة البيانات</h1>";

// إضافة عمود user_comment
echo "<h3>1. إضافة عمود user_comment:</h3>";
$sql1 = "ALTER TABLE needs_requests ADD COLUMN user_comment TEXT NULL";
if (mysqli_query($con, $sql1)) {
    echo "<div class='success'>✅ تم إضافة عمود user_comment بنجاح</div>";
} else {
    if (strpos(mysqli_error($con), "Duplicate column name") !== false) {
        echo "<div class='success'>ℹ️ عمود user_comment موجود بالفعل</div>";
    } else {
        echo "<div class='error'>❌ خطأ: " . mysqli_error($con) . "</div>";
    }
}

// إضافة عمود user_update_date
echo "<h3>2. إضافة عمود user_update_date:</h3>";
$sql2 = "ALTER TABLE needs_requests ADD COLUMN user_update_date DATETIME NULL";
if (mysqli_query($con, $sql2)) {
    echo "<div class='success'>✅ تم إضافة عمود user_update_date بنجاح</div>";
} else {
    if (strpos(mysqli_error($con), "Duplicate column name") !== false) {
        echo "<div class='success'>ℹ️ عمود user_update_date موجود بالفعل</div>";
    } else {
        echo "<div class='error'>❌ خطأ: " . mysqli_error($con) . "</div>";
    }
}

// تحديث حالات الطلبات
echo "<h3>3. تحديث حالات الطلبات:</h3>";
$sql3 = "ALTER TABLE needs_requests MODIFY COLUMN status ENUM('قيد المراجعة', 'تم التوفير', 'لم يتم التوفير', 'ملغي') DEFAULT 'قيد المراجعة'";
if (mysqli_query($con, $sql3)) {
    echo "<div class='success'>✅ تم تحديث حالات الطلبات بنجاح</div>";
} else {
    echo "<div class='error'>❌ خطأ: " . mysqli_error($con) . "</div>";
}

// اختبار النظام
echo "<h3>4. اختبار النظام:</h3>";
$test_query = "SELECT COUNT(*) as total FROM needs_requests";
$test_result = mysqli_query($con, $test_query);
if ($test_result) {
    $test_row = mysqli_fetch_assoc($test_result);
    echo "<div class='success'>✅ النظام يعمل - إجمالي الطلبات: " . $test_row['total'] . "</div>";
} else {
    echo "<div class='error'>❌ خطأ في الاختبار: " . mysqli_error($con) . "</div>";
}

// عرض بنية الجدول
echo "<h3>5. بنية الجدول الحالية:</h3>";
$describe = "DESCRIBE needs_requests";
$result = mysqli_query($con, $describe);
if ($result) {
    echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
    echo "<tr style='background:#f8f9fa;'><th>العمود</th><th>النوع</th><th>Null</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td style='padding:8px;border:1px solid #ddd;'>" . $row['Field'] . "</td>";
        echo "<td style='padding:8px;border:1px solid #ddd;'>" . $row['Type'] . "</td>";
        echo "<td style='padding:8px;border:1px solid #ddd;'>" . $row['Null'] . "</td>";
        echo "<td style='padding:8px;border:1px solid #ddd;'>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>🎉 تم الانتهاء!</h2>";
echo "<p><a href='my_needs.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>العودة لصفحة الاحتياجات</a></p>";

echo "</div></body></html>";

mysqli_close($con);
?>
