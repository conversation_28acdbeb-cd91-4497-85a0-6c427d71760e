<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "../db_connection.php";

// استعلام الرواتب
$salaries_query = "
    SELECT 
        employ_tb.*,
        users_tb.user_name,
        DATEDIFF(CURDATE(), employ_tb.date_start) as days_employed,
        FLOOR(DATEDIFF(CURDATE(), employ_tb.date_start) / 365) as years_employed
    FROM employ_tb 
    INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user
    WHERE employ_tb.salary > 0
    ORDER BY employ_tb.salary DESC
";

$salaries_result = $conn->query($salaries_query);

// حساب الإحصائيات
$total_salaries = 0;
$employee_count = 0;
$job_statistics = [];
$salary_ranges = [
    '0-500000' => 0,
    '500001-1000000' => 0,
    '1000001-1500000' => 0,
    '1500001+' => 0
];

if ($salaries_result && $salaries_result->num_rows > 0) {
    while ($row = $salaries_result->fetch_assoc()) {
        $total_salaries += $row['salary'];
        $employee_count++;
        
        // إحصائيات المناصب
        $job = $row['job'];
        if (!isset($job_statistics[$job])) {
            $job_statistics[$job] = ['count' => 0, 'total_salary' => 0];
        }
        $job_statistics[$job]['count']++;
        $job_statistics[$job]['total_salary'] += $row['salary'];
        
        // توزيع الرواتب
        $salary = $row['salary'];
        if ($salary <= 500000) {
            $salary_ranges['0-500000']++;
        } elseif ($salary <= 1000000) {
            $salary_ranges['500001-1000000']++;
        } elseif ($salary <= 1500000) {
            $salary_ranges['1000001-1500000']++;
        } else {
            $salary_ranges['1500001+']++;
        }
    }
}

$average_salary = $employee_count > 0 ? $total_salaries / $employee_count : 0;

// إعادة تعيين مؤشر النتائج
$salaries_result->data_seek(0);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الرواتب الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .report-header { background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
        @media print { .no-print { display: none !important; } body { background: white !important; } }
        .employee-card { border-left: 4px solid #ffc107; transition: all 0.3s ease; }
        .employee-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- Header -->
        <div class="report-header text-center">
            <h1><i class="fas fa-users"></i> تقرير الرواتب الشامل</h1>
            <h3>روضة الأطفال</h3>
            <p class="mb-0">تاريخ التقرير: <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Controls -->
        <div class="row no-print mb-4">
            <div class="col-md-12 text-end">
                <button onclick="window.print()" class="btn btn-success">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button onclick="exportToExcel()" class="btn btn-primary">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <a href="../accounting_system.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="fas fa-users fa-3x text-warning mb-3"></i>
                    <h4 class="text-warning"><?= $employee_count ?></h4>
                    <p class="text-muted">عدد الموظفين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                    <h4 class="text-success"><?= number_format($total_salaries) ?></h4>
                    <p class="text-muted">إجمالي الرواتب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                    <h4 class="text-info"><?= number_format($average_salary) ?></h4>
                    <p class="text-muted">متوسط الراتب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <i class="fas fa-briefcase fa-3x text-primary mb-3"></i>
                    <h4 class="text-primary"><?= count($job_statistics) ?></h4>
                    <p class="text-muted">عدد المناصب</p>
                </div>
            </div>
        </div>

        <!-- Job Statistics -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-chart-pie"></i> إحصائيات المناصب</h5>
            <div class="row">
                <?php foreach ($job_statistics as $job => $stats): 
                    $avg_job_salary = $stats['count'] > 0 ? $stats['total_salary'] / $stats['count'] : 0;
                    $percentage = $employee_count > 0 ? ($stats['count'] / $employee_count) * 100 : 0;
                ?>
                <div class="col-md-6 mb-3">
                    <div class="employee-card p-3 border rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?= htmlspecialchars($job) ?></h6>
                                <small class="text-muted"><?= $stats['count'] ?> موظف (<?= number_format($percentage, 1) ?>%)</small>
                            </div>
                            <div class="text-end">
                                <h6 class="text-warning mb-0"><?= number_format($avg_job_salary) ?></h6>
                                <small class="text-muted">متوسط الراتب</small>
                            </div>
                        </div>
                        <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar bg-warning" style="width: <?= $percentage ?>%"></div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Salary Distribution -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-chart-bar"></i> توزيع الرواتب</h5>
            <div class="row">
                <?php 
                $range_labels = [
                    '0-500000' => '0 - 500,000',
                    '500001-1000000' => '500,001 - 1,000,000',
                    '1000001-1500000' => '1,000,001 - 1,500,000',
                    '1500001+' => '1,500,001+'
                ];
                foreach ($salary_ranges as $range => $count): 
                    $percentage = $employee_count > 0 ? ($count / $employee_count) * 100 : 0;
                ?>
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6><?= $range_labels[$range] ?> دينار</h6>
                            <div class="d-flex justify-content-between">
                                <span><?= $count ?> موظف</span>
                                <span><?= number_format($percentage, 1) ?>%</span>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-info" style="width: <?= $percentage ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Detailed Employee List -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-table"></i> قائمة الموظفين التفصيلية</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="employeesTable">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم الموظف</th>
                            <th>المنصب</th>
                            <th>الراتب</th>
                            <th>تاريخ البداية</th>
                            <th>سنوات الخدمة</th>
                            <th>الهاتف</th>
                            <th>المستخدم المسؤول</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($salaries_result && $salaries_result->num_rows > 0): ?>
                            <?php while ($row = $salaries_result->fetch_assoc()): ?>
                            <tr>
                                <td><strong><?= htmlspecialchars($row['f_name']) ?></strong></td>
                                <td><?= htmlspecialchars($row['job']) ?></td>
                                <td><span class="badge bg-warning text-dark fs-6"><?= number_format($row['salary']) ?></span></td>
                                <td><?= date('Y-m-d', strtotime($row['date_start'])) ?></td>
                                <td>
                                    <?= $row['years_employed'] ?> سنة
                                    <?php if ($row['days_employed'] % 365 > 0): ?>
                                        و <?= floor(($row['days_employed'] % 365) / 30) ?> شهر
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($row['phone']) ?></td>
                                <td><span class="badge bg-primary"><?= htmlspecialchars($row['user_name']) ?></span></td>
                            </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center text-muted">لا توجد بيانات موظفين</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-warning">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th><?= number_format($total_salaries) ?></th>
                            <th colspan="2"><?= $employee_count ?> موظف</th>
                            <th colspan="2">متوسط: <?= number_format($average_salary) ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <div class="col-md-6">
                <div class="stat-card">
                    <h5 class="mb-4"><i class="fas fa-chart-pie"></i> توزيع المناصب</h5>
                    <canvas id="jobsChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <h5 class="mb-4"><i class="fas fa-chart-bar"></i> توزيع الرواتب</h5>
                    <canvas id="salaryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Jobs Distribution Pie Chart
        const jobsCtx = document.getElementById('jobsChart').getContext('2d');
        new Chart(jobsCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php foreach ($job_statistics as $job => $stats) echo "'" . htmlspecialchars($job) . "',"; ?>],
                datasets: [{
                    data: [<?php foreach ($job_statistics as $job => $stats) echo $stats['count'] . ","; ?>],
                    backgroundColor: [
                        '#ffc107', '#fd7e14', '#dc3545', '#28a745', '#20c997', 
                        '#17a2b8', '#6f42c1', '#e83e8c', '#6c757d', '#343a40'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Salary Distribution Bar Chart
        const salaryCtx = document.getElementById('salaryChart').getContext('2d');
        new Chart(salaryCtx, {
            type: 'bar',
            data: {
                labels: ['0-500K', '500K-1M', '1M-1.5M', '1.5M+'],
                datasets: [{
                    label: 'عدد الموظفين',
                    data: [<?php foreach ($salary_ranges as $range => $count) echo $count . ","; ?>],
                    backgroundColor: '#ffc107',
                    borderColor: '#ff8c00',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Export to Excel function
        function exportToExcel() {
            const table = document.getElementById('employeesTable');
            const csv = tableToCSV(table);
            downloadCSV(csv, 'salary_report.csv');
        }

        function tableToCSV(table) {
            let csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length; j++) {
                    row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
                }

                csv.push(row.join(','));
            }

            return csv.join('\n');
        }

        function downloadCSV(csv, filename) {
            const csvFile = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const downloadLink = document.createElement('a');

            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';

            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>

<?php $conn->close(); ?>
