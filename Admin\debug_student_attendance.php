<?php
// ملف تشخيص مشاكل حضور الطلاب
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

include "addon/dbcon.php";

echo "<h2>تشخيص مشاكل حضور الطلاب</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f2f2f2; }
    .code { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #ccc; }
</style>";

// 1. اختبار الاتصال بقاعدة البيانات
echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
if ($con) {
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "<p>اسم قاعدة البيانات: kidzrcle_rwda</p>";
} else {
    echo "<p class='error'>❌ فشل في الاتصال بقاعدة البيانات: " . mysqli_connect_error() . "</p>";
    exit();
}

// 2. اختبار وجود الجداول
echo "<h3>2. اختبار وجود الجداول:</h3>";
$tables = ['stud_tb', 'stat', 'users_tb'];
foreach ($tables as $table) {
    $result = $con->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p class='success'>✅ جدول $table موجود</p>";
    } else {
        echo "<p class='error'>❌ جدول $table غير موجود</p>";
    }
}

// 3. اختبار بيانات الطلاب
echo "<h3>3. اختبار بيانات الطلاب:</h3>";
try {
    $students_query = "SELECT COUNT(*) as count FROM stud_tb";
    $students_result = $con->query($students_query);
    $students_count = $students_result->fetch_assoc()['count'];
    
    echo "<p>عدد الطلاب: <strong>$students_count</strong></p>";
    
    if ($students_count > 0) {
        echo "<p class='success'>✅ توجد بيانات طلاب</p>";
        
        // عرض عينة من الطلاب
        $sample_query = "SELECT id, name, catg, p_name, userID FROM stud_tb LIMIT 3";
        $sample_result = $con->query($sample_query);
        
        echo "<h4>عينة من الطلاب:</h4>";
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الصف</th><th>ولي الأمر</th><th>User ID</th></tr>";
        while ($student = $sample_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$student['id']}</td>";
            echo "<td>{$student['name']}</td>";
            echo "<td>{$student['catg']}</td>";
            echo "<td>{$student['p_name']}</td>";
            echo "<td>{$student['userID']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ لا توجد بيانات طلاب</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في جلب بيانات الطلاب: " . $e->getMessage() . "</p>";
}

// 4. اختبار بيانات الحضور
echo "<h3>4. اختبار بيانات الحضور:</h3>";
try {
    $attendance_query = "SELECT COUNT(*) as count FROM stat";
    $attendance_result = $con->query($attendance_query);
    $attendance_count = $attendance_result->fetch_assoc()['count'];
    
    echo "<p>عدد سجلات الحضور: <strong>$attendance_count</strong></p>";
    
    if ($attendance_count > 0) {
        echo "<p class='success'>✅ توجد بيانات حضور</p>";
        
        // عرض عينة من سجلات الحضور
        $sample_query = "SELECT s.name, st.stat_stud, st.data_stat, st.id_stud 
                        FROM stat st 
                        LEFT JOIN stud_tb s ON st.id_stud = s.id 
                        ORDER BY st.data_stat DESC 
                        LIMIT 5";
        $sample_result = $con->query($sample_query);
        
        echo "<h4>عينة من سجلات الحضور:</h4>";
        echo "<table>";
        echo "<tr><th>اسم الطالب</th><th>الحالة</th><th>التاريخ</th><th>ID الطالب</th></tr>";
        while ($record = $sample_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . ($record['name'] ?? 'غير موجود') . "</td>";
            echo "<td>{$record['stat_stud']}</td>";
            echo "<td>{$record['data_stat']}</td>";
            echo "<td>{$record['id_stud']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا توجد بيانات حضور</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في جلب بيانات الحضور: " . $e->getMessage() . "</p>";
}

// 5. اختبار الاستعلام المستخدم في API
echo "<h3>5. اختبار استعلام API:</h3>";
try {
    $test_date = date('Y-m-d');
    echo "<p>تاريخ الاختبار: $test_date</p>";
    
    // الاستعلام المستخدم في student_attendance_data.php
    $api_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name 
                  FROM stud_tb s
                  LEFT JOIN users_tb u ON s.userID = u.id_user
                  ORDER BY s.name
                  LIMIT 5";
    
    echo "<div class='code'>الاستعلام المستخدم:<br>$api_query</div>";
    
    $api_result = $con->query($api_query);
    
    if ($api_result) {
        echo "<p class='success'>✅ الاستعلام يعمل بنجاح</p>";
        echo "<p>عدد النتائج: " . $api_result->num_rows . "</p>";
        
        if ($api_result->num_rows > 0) {
            echo "<h4>نتائج الاستعلام:</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>الاسم</th><th>الصف</th><th>ولي الأمر</th><th>المستخدم</th></tr>";
            while ($row = $api_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['name']}</td>";
                echo "<td>{$row['catg']}</td>";
                echo "<td>{$row['p_name']}</td>";
                echo "<td>" . ($row['user_name'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ فشل في تنفيذ الاستعلام: " . $con->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في اختبار API: " . $e->getMessage() . "</p>";
}

// 6. اختبار ملف API مباشرة
echo "<h3>6. اختبار ملف API:</h3>";
$api_url = "student_attendance_data.php?date=" . date('Y-m-d');
echo "<p><a href='$api_url' target='_blank'>🔗 اختبار API مباشرة</a></p>";

// 7. اختبار JavaScript
echo "<h3>7. اختبار JavaScript:</h3>";
echo "<button onclick='testAPI()'>اختبار تحميل البيانات</button>";
echo "<div id='test-result'></div>";

echo "<script>
function testAPI() {
    const resultDiv = document.getElementById('test-result');
    resultDiv.innerHTML = '<p>جاري التحميل...</p>';
    
    fetch('student_attendance_data.php?date=" . date('Y-m-d') . "')
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            try {
                const data = JSON.parse(text);
                console.log('Parsed data:', data);
                resultDiv.innerHTML = '<p class=\"success\">✅ API يعمل بنجاح</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                console.error('JSON parse error:', e);
                resultDiv.innerHTML = '<p class=\"error\">❌ خطأ في تحليل JSON</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = '<p class=\"error\">❌ خطأ في الشبكة: ' + error.message + '</p>';
        });
}
</script>";

echo "<hr>";
echo "<h3>الخلاصة:</h3>";
echo "<p>إذا كانت جميع الاختبارات ناجحة، فالمشكلة قد تكون في:</p>";
echo "<ul>";
echo "<li>مسار الملفات</li>";
echo "<li>صلاحيات الملفات</li>";
echo "<li>إعدادات الخادم</li>";
echo "<li>JavaScript في المتصفح</li>";
echo "</ul>";

$con->close();
?>
