<?php
session_start();
if(!isset($_SESSION['user']) || ($_SESSION['user']->role !== "Admin" && $_SESSION['user']->role !== "Mod")){
    header("location:../login.php");
    exit();
}

include "addon/dbcon.php";

$leave_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if($leave_id <= 0) {
    die("معرف طلب الإجازة غير صحيح");
}

// جلب بيانات طلب الإجازة
$query = "SELECT lr.*, u.user_name, ur.user_name as response_by_name 
          FROM leave_requests lr 
          JOIN users_tb u ON lr.user_id = u.id_user 
          LEFT JOIN users_tb ur ON lr.response_by = ur.id_user 
          WHERE lr.id_leave = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $leave_id);
$stmt->execute();
$result = $stmt->get_result();

if($result->num_rows == 0) {
    die("طلب الإجازة غير موجود");
}

$leave = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>طباعة طلب إجازة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .print-header {
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .print-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
        }
        .print-info {
            margin: 10px 0;
            color: #666;
        }
        .leave-container {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 25px;
            background: #f9f9f9;
        }
        .employee-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
        }
        .leave-type-badge {
            background: #17a2b8;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        .info-section {
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            margin: 12px 0;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #2c3e50;
            width: 150px;
            flex-shrink: 0;
        }
        .info-value {
            color: #495057;
            flex: 1;
        }
        .details-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #667eea;
        }
        .details-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin: 10px 0;
        }
        .status-pending { background: #ffc107; }
        .status-approved { background: #28a745; }
        .status-rejected { background: #dc3545; }
        .response-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        .date-range {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none; }
        }
        .print-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="print-header">
        <h1>طلب إجازة</h1>
        <div class="print-info">تاريخ الطباعة: <?php echo date('Y/m/d H:i'); ?></div>
        <div class="print-info">طبع بواسطة: <?php echo $_SESSION['user']->user_name; ?></div>
    </div>

    <div class="leave-container">
        <div class="employee-title"><?php echo htmlspecialchars($leave['employee_name']); ?></div>
        <div style="text-align: center;">
            <span class="leave-type-badge"><?php echo $leave['leave_type']; ?></span>
        </div>
        
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">المستخدم:</div>
                <div class="info-value"><?php echo htmlspecialchars($leave['user_name']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">تاريخ الطلب:</div>
                <div class="info-value"><?php echo date('Y/m/d', strtotime($leave['request_date'])); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">الحالة:</div>
                <div class="info-value">
                    <span class="status-badge status-<?php
                        echo $leave['status'] == 'قيد المراجعة' ? 'pending' :
                            ($leave['status'] == 'موافق' ? 'approved' : 'rejected');
                    ?>">
                        <?php echo $leave['status']; ?>
                    </span>
                </div>
            </div>
            <?php if($leave['response_date']): ?>
            <div class="info-row">
                <div class="info-label">تاريخ الرد:</div>
                <div class="info-value"><?php echo date('Y/m/d H:i', strtotime($leave['response_date'])); ?></div>
            </div>
            <?php endif; ?>
            <?php if($leave['response_by_name']): ?>
            <div class="info-row">
                <div class="info-label">رد بواسطة:</div>
                <div class="info-value"><?php echo htmlspecialchars($leave['response_by_name']); ?></div>
            </div>
            <?php endif; ?>
        </div>

        <div class="date-range">
            <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">فترة الإجازة</div>
            <div class="info-row">
                <div class="info-label">تاريخ البداية:</div>
                <div class="info-value"><?php echo date('Y/m/d', strtotime($leave['start_date'])); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">تاريخ النهاية:</div>
                <div class="info-value"><?php echo date('Y/m/d', strtotime($leave['end_date'])); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">عدد الأيام:</div>
                <div class="info-value"><?php echo $leave['days_count']; ?> يوم</div>
            </div>
        </div>

        <div class="details-section">
            <div class="details-title">تفاصيل الإجازة:</div>
            <div><?php echo nl2br(htmlspecialchars($leave['leave_details'])); ?></div>
        </div>

        <?php if($leave['admin_response']): ?>
        <div class="response-section">
            <div class="details-title">رد الإدارة:</div>
            <div><?php echo nl2br(htmlspecialchars($leave['admin_response'])); ?></div>
        </div>
        <?php endif; ?>
    </div>

    <div class="no-print">
        <button onclick="window.print()" class="print-btn">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button onclick="window.close()" class="print-btn" style="background: #6c757d;">
            إغلاق
        </button>
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
