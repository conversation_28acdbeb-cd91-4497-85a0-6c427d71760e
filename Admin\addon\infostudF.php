<?php
session_start();
include "dbcon.php";

if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo "<td colspan=20 style='text-align: center; padding: 30px; color: #dc3545;'>غير مصرح لك بالوصول</td>";
    exit();
}

// التحقق من وجود ID في POST أو GET
$search = 0;
if (isset($_POST['id']) && $_POST['id'] != '0' && !empty($_POST['id'])) {
    $search = intval($_POST['id']);
} elseif (isset($_GET['id']) && $_GET['id'] != '0' && !empty($_GET['id'])) {
    $search = intval($_GET['id']);
}

if ($search == 0) {
    echo "<td colspan=20 style='font-size: 25px; text-align: center; padding: 50px;'>يرجى اختيار مستخدم من القائمة أعلاه لعرض بيانات الطلاب</td>";
    exit();
}

// استعلام بسيط بدون COALESCE أولاً
$sql = "SELECT stud_tb.*, stud_pay.*, users_tb.user_name
        FROM stud_tb,stud_pay,users_tb
        WHERE stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND users_tb.id_user=$search
        ORDER BY stud_tb.datein DESC";

$result = mysqli_query($con, $sql);

// إضافة تشخيص
if (!$result) {
    echo "<td colspan=20 style='color: red;'>خطأ في الاستعلام: " . mysqli_error($con) . "</td>";
    exit();
}

if (mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        $id = $row['id'];
        $id_note = $row['id_note'];
        $name = $row['name'];
        $age = $row['age'];
        $sex = $row['sex'];
        $catg = $row['catg'];
        $datein = $row['datein'];
        $p_name = $row['p_name'];
        $p_phone = $row['p_phone'];
        $loc = $row['loc'];
        $date_exp = $row['date_exp'];
        $cash_stud = number_format($row['cash_stud']);
        $user_name = $row['user_name'];
        $id_pay = $row['id_pay'];
        $health_status = isset($row['health_status']) ? $row['health_status'] : 'لا توجد ملاحظات';
        $registration_status = isset($row['registration_status']) ? $row['registration_status'] : 'تسجيل جديد';

        $date_in = strtotime(date('y-m-d'));
        $date_out = strtotime($date_exp);
        $stat = $date_out - $date_in;
        $cek = floor($stat / (60 * 60 * 24));

        if ($cek <= 0) {
            $mes = '<h3 class=exp>منتهي</h3>';
        } elseif ($cek <= 10 && $cek > 0) {
            $mes = '<h3 class=soon>قريبا</h3>';
        } else {
            $mes = '<h3 class=still>فعال</h3>';
        }

        // Check if the registration status is new or old
        $currentDate = new DateTime();
        $targetDate = new DateTime($datein);
        $regStatus = 'old';
        if ($currentDate->format('Y-m') == $targetDate->format('Y-m')) {
            $regStatus = 'جديد';
            $classStatus = "still";
        } else {
            $regStatus = 'قديم';
            $classStatus = "soon";
        }

        // Displaying data
        echo '<tr id="tr_'.$id.'">
            <td>
                <button type="button" class="btn btn-secondary mb-1" id="edit_bnt" name="update">
                    <a style="text-decoration: none; color: whitesmoke;" href="editstud.php?id='.$id.'">تعديل</a>
                </button>
                <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove"
                        onclick="deletdata('.$id.')">حذف
                </button>
            </td>
            <td>'.$user_name.'</td>
            <td>'.$cek.'</td>
            <td>'.$mes.'</td>
            <td>IQD '.$cash_stud.'</td>
            <td>'.$date_exp.'</td>
            <td>'.$datein.'</td>
            <td>'.$p_name.'</td>
            <td>'.$p_phone.'</td>
            <td>'.$catg.'</td>
            <td>'.$loc.'</td>
            <td>'.$sex.'</td>
            <td>'.$age.'</td>
            <td><p class="'.$classStatus.'">'.$regStatus.'</p></td>
            <td>'.$name.'</td>
            <td>'.$id_pay.'</td>
            <td>'.((strlen($health_status) > 50) ? substr($health_status, 0, 50) . '...' : htmlspecialchars($health_status)).'</td>
            <td><span class="badge '.($registration_status == 'تسجيل جديد' ? 'bg-primary' : 'bg-success').'">'.$registration_status.'</span></td>
            <td></td>
        </tr>';
    }

    // إضافة إحصائيات بسيطة
    $datenow = date('Y-m-d');
    $Date2 = date('Y-m-d', strtotime($datenow. ' + 1 days'));
    $Date4 = date('Y-m-d', strtotime($datenow. ' + 365 days'));
    $sql2 = "SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date4') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.id_user=$search ";
    $result2 = mysqli_query($con, $sql2);
    $sum = mysqli_num_rows($result2);

    echo "<script>showToast2(".$sum.")</script>";

} else {
    echo "<td colspan=20 style='font-size: 25px;'>لاتوجد بيانات طلاب لهذا المستخدم</td>";
    echo "<script>reomtost()</script>";
}

?>
