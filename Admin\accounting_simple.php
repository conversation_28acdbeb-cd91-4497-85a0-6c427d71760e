<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
$con = new mysqli("localhost", "kidzrcle_rwda", "kidzrcle_rwda", "kidzrcle_rwda");
$con->set_charset("utf8");

if ($con->connect_error) {
    die("فشل الاتصال: " . $con->connect_error);
}

// حساب الإحصائيات
$total_revenue = 0;
$total_expenses = 0;
$total_salaries = 0;

// الإيرادات
$revenue_result = $con->query("SELECT SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0");
if ($revenue_result && $row = $revenue_result->fetch_assoc()) {
    $total_revenue = $row['total'] ? $row['total'] : 0;
}

// المصروفات
$expenses_result = $con->query("SELECT SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0");
if ($expenses_result && $row = $expenses_result->fetch_assoc()) {
    $total_expenses = $row['total'] ? $row['total'] : 0;
}

// الرواتب
$salaries_result = $con->query("SELECT SUM(salary) as total FROM employ_tb WHERE salary > 0");
if ($salaries_result && $row = $salaries_result->fetch_assoc()) {
    $total_salaries = $row['total'] ? $row['total'] : 0;
}

$net_profit = $total_revenue - $total_expenses - $total_salaries;

// جلب المستخدمين
$users = [];
$users_result = $con->query("SELECT DISTINCT user_name FROM users_tb WHERE role = 'User' ORDER BY user_name");
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row['user_name'];
    }
}

// جلب البيانات
$all_data = [];

// الإيرادات
$revenue_query = "SELECT 'إيراد' as type, CONCAT('دفعة طالب: ', COALESCE(st.name, 'غير محدد')) as description, 
                  sp.cash_stud as amount, sp.datein as date, COALESCE(u.user_name, 'غير محدد') as user_name
                  FROM stud_pay sp 
                  LEFT JOIN stud_tb st ON sp.id_stud = st.id 
                  LEFT JOIN users_tb u ON st.userID = u.id_user 
                  WHERE sp.cash_stud > 0 
                  ORDER BY sp.datein DESC LIMIT 50";
$revenue_data = $con->query($revenue_query);
if ($revenue_data) {
    while ($row = $revenue_data->fetch_assoc()) {
        $all_data[] = $row;
    }
}

// المصروفات
$expenses_query = "SELECT 'مصروف' as type, COALESCE(d.depit_note, 'مصروف غير محدد') as description,
                   d.depit_cash as amount, d.depit_date as date, COALESCE(u.user_name, 'غير محدد') as user_name
                   FROM depit_tb d 
                   LEFT JOIN users_tb u ON d.userID = u.id_user 
                   WHERE d.depit_cash > 0 
                   ORDER BY d.depit_date DESC LIMIT 50";
$expenses_data = $con->query($expenses_query);
if ($expenses_data) {
    while ($row = $expenses_data->fetch_assoc()) {
        $all_data[] = $row;
    }
}

// الرواتب
$salaries_query = "SELECT 'راتب' as type, CONCAT('راتب: ', COALESCE(e.f_name, 'غير محدد'), ' - ', COALESCE(e.job, 'غير محدد')) as description,
                   e.salary as amount, e.date_start as date, COALESCE(u.user_name, 'غير محدد') as user_name
                   FROM employ_tb e 
                   LEFT JOIN users_tb u ON e.userID = u.id_user 
                   WHERE e.salary > 0 
                   ORDER BY e.date_start DESC LIMIT 50";
$salaries_data = $con->query($salaries_query);
if ($salaries_data) {
    while ($row = $salaries_data->fetch_assoc()) {
        $all_data[] = $row;
    }
}

// ترتيب البيانات
if (!empty($all_data)) {
    usort($all_data, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
}

// معالجة الفلاتر
$user_filter = isset($_GET['user_filter']) ? $_GET['user_filter'] : '';
$type_filter = isset($_GET['type_filter']) ? $_GET['type_filter'] : '';
$search_filter = isset($_GET['search_filter']) ? $_GET['search_filter'] : '';

$filtered_data = [];
foreach ($all_data as $row) {
    $include = true;
    
    if ($user_filter && stripos($row['user_name'], $user_filter) === false) {
        $include = false;
    }
    
    if ($type_filter && stripos($row['type'], $type_filter) === false) {
        $include = false;
    }
    
    if ($search_filter && stripos($row['description'], $search_filter) === false) {
        $include = false;
    }
    
    if ($include) {
        $filtered_data[] = $row;
    }
}

$display_data = !empty($filtered_data) ? $filtered_data : $all_data;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/admin_navbar.css">
    <link rel="icon" href="css/icon.ico">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            min-height: 100vh;
            padding: 20px 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid;
        }
        .stat-card.revenue { border-left-color: #27ae60; }
        .stat-card.expenses { border-left-color: #e74c3c; }
        .stat-card.salaries { border-left-color: #f39c12; }
        .stat-card.profit { border-left-color: #3498db; }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stat-title {
            color: #7f8c8d;
            font-weight: 600;
        }
        .filters {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .data-table {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .table th {
            background: #34495e;
            color: white;
            border: none;
            padding: 15px;
            text-align: center;
        }
        .table td {
            padding: 12px;
            text-align: center;
            vertical-align: middle;
        }
        .badge-revenue { background: #27ae60; color: white; padding: 5px 10px; border-radius: 10px; }
        .badge-expense { background: #e74c3c; color: white; padding: 5px 10px; border-radius: 10px; }
        .badge-salary { background: #f39c12; color: white; padding: 5px 10px; border-radius: 10px; }
        .form-control {
            border-radius: 10px;
            border: 2px solid #ecf0f1;
            padding: 10px 15px;
        }
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .btn-primary {
            background: #3498db;
            border: none;
        }
        .btn-success {
            background: #27ae60;
            border: none;
        }
    </style>
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-calculator"></i> نظام المحاسبة</h1>
            <p>إدارة شاملة للحسابات والمالية</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card revenue">
                <i class="fas fa-arrow-up" style="font-size: 2.5rem; color: #27ae60; margin-bottom: 15px;"></i>
                <div class="stat-value"><?php echo number_format($total_revenue); ?></div>
                <div class="stat-title">إجمالي الإيرادات</div>
                <small style="color: #95a5a6;">دينار عراقي</small>
            </div>
            <div class="stat-card expenses">
                <i class="fas fa-arrow-down" style="font-size: 2.5rem; color: #e74c3c; margin-bottom: 15px;"></i>
                <div class="stat-value"><?php echo number_format($total_expenses); ?></div>
                <div class="stat-title">إجمالي المصروفات</div>
                <small style="color: #95a5a6;">دينار عراقي</small>
            </div>
            <div class="stat-card salaries">
                <i class="fas fa-users" style="font-size: 2.5rem; color: #f39c12; margin-bottom: 15px;"></i>
                <div class="stat-value"><?php echo number_format($total_salaries); ?></div>
                <div class="stat-title">إجمالي الرواتب</div>
                <small style="color: #95a5a6;">دينار عراقي</small>
            </div>
            <div class="stat-card profit">
                <i class="fas fa-chart-line" style="font-size: 2.5rem; color: #3498db; margin-bottom: 15px;"></i>
                <div class="stat-value" style="color: <?php echo $net_profit >= 0 ? '#27ae60' : '#e74c3c'; ?>">
                    <?php echo number_format($net_profit); ?>
                </div>
                <div class="stat-title">صافي الربح</div>
                <small style="color: #95a5a6;">دينار عراقي</small>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <h4><i class="fas fa-filter"></i> فلاتر البحث</h4>
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label>المستخدم:</label>
                        <select name="user_filter" class="form-control">
                            <option value="">جميع المستخدمين</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo htmlspecialchars($user); ?>" <?php echo ($user_filter == $user) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label>النوع:</label>
                        <select name="type_filter" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="إيراد" <?php echo ($type_filter == 'إيراد') ? 'selected' : ''; ?>>الإيرادات</option>
                            <option value="مصروف" <?php echo ($type_filter == 'مصروف') ? 'selected' : ''; ?>>المصروفات</option>
                            <option value="راتب" <?php echo ($type_filter == 'راتب') ? 'selected' : ''; ?>>الرواتب</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>البحث:</label>
                        <input type="text" name="search_filter" class="form-control" 
                               placeholder="ابحث في الوصف..." value="<?php echo htmlspecialchars($search_filter); ?>">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label>&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="?" class="btn btn-success w-100">
                                <i class="fas fa-refresh"></i> مسح
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Data Table -->
        <div class="data-table">
            <h4><i class="fas fa-table"></i> جميع المعاملات المالية (<?php echo count($display_data); ?> معاملة)</h4>
            
            <?php if (!empty($display_data)): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($display_data as $row): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d', strtotime($row['date'])); ?></td>
                                    <td>
                                        <?php if ($row['type'] == 'إيراد'): ?>
                                            <span class="badge-revenue">💰 إيراد</span>
                                        <?php elseif ($row['type'] == 'مصروف'): ?>
                                            <span class="badge-expense">💸 مصروف</span>
                                        <?php else: ?>
                                            <span class="badge-salary">👥 راتب</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['description']); ?></td>
                                    <td style="font-weight: bold; color: <?php echo $row['type'] == 'إيراد' ? '#27ae60' : '#e74c3c'; ?>">
                                        <?php echo number_format($row['amount']); ?> د.ع
                                    </td>
                                    <td><?php echo htmlspecialchars($row['user_name']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                    <h5>لا توجد معاملات مالية</h5>
                    <p>لم يتم العثور على معاملات تطابق معايير البحث</p>
                    <a href="?" class="btn btn-primary">عرض جميع المعاملات</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="js/jquery.min.js"></script>
</body>
</html>

<?php $con->close(); ?>
