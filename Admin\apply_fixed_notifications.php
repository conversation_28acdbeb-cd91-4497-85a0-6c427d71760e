<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

echo "<h2>تطبيق الإشعارات المحسنة على جميع صفحات الأدمن</h2>";

// قائمة الصفحات التي تحتاج إلى الإشعارات
$admin_pages = [
    'statistics.php',
    'allstud.php',
    'new_stud.php',
    'addstud.php',
    'manage_needs.php',
    'manage_leaves.php',
    'allDepit.php',
    'info_depit.php',
    'expired_stud.php',
    'expried_soon.php',
    'hadanaStud.php',
    'rodaStud.php',
    'tamhediStud.php',
    'tadeery.php'
];

$updated_count = 0;
$errors = [];

foreach ($admin_pages as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        
        // التحقق من وجود الإشعارات المحسنة
        if (strpos($content, 'notifications_fixed.php') === false) {
            // البحث عن نهاية الصفحة
            if (preg_match('/<\/body>\s*<\/html>/i', $content)) {
                // إضافة الإشعارات قبل نهاية الصفحة
                $new_content = preg_replace(
                    '/(<\/body>\s*<\/html>)/i',
                    "\n    <!-- إضافة الإشعارات المحسنة -->\n    <?php include 'notifications_fixed.php'; ?>\n$1",
                    $content
                );
                
                if (file_put_contents($page, $new_content)) {
                    echo "<p style='color: green;'>✅ تم تحديث $page</p>";
                    $updated_count++;
                } else {
                    $errors[] = "فشل في كتابة $page";
                }
            } else {
                $errors[] = "لم يتم العثور على نهاية صحيحة في $page";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ $page محدث مسبقاً</p>";
        }
    } else {
        $errors[] = "الملف $page غير موجود";
    }
}

echo "<h3>ملخص التحديث:</h3>";
echo "<p>📊 تم تحديث <strong>$updated_count</strong> صفحة</p>";

if (!empty($errors)) {
    echo "<h4>الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>❌ $error</p>";
    }
}

// إنشاء ملف CSS إضافي للتأكد من عدم التداخل
$css_content = "
/* CSS إضافي للإشعارات المحسنة */
.notifications-fixed-container {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    z-index: 999999 !important;
}

/* التأكد من عدم تداخل الإشعارات مع العناصر الأخرى */
nav, .navbar, header {
    z-index: 1000 !important;
}

body {
    position: relative !important;
}

/* تحسين عرض الإشعارات على الشاشات الصغيرة */
@media (max-width: 480px) {
    .notifications-fixed-container {
        top: 10px !important;
        left: 10px !important;
    }
    
    .notifications-dropdown-fixed {
        width: calc(100vw - 40px) !important;
        max-width: 300px !important;
    }
}

/* إخفاء الإشعارات القديمة إذا كانت موجودة */
.notifications-container:not(.notifications-fixed-container) {
    display: none !important;
}
";

if (file_put_contents('css/notifications_fixed.css', $css_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء ملف CSS للإشعارات المحسنة</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف CSS</p>";
}

// تحديث ملف الإشعارات المحسنة لتضمين CSS
$notifications_content = file_get_contents('notifications_fixed.php');
if (strpos($notifications_content, 'notifications_fixed.css') === false) {
    $css_link = '<link rel="stylesheet" href="css/notifications_fixed.css">';
    $updated_notifications = str_replace(
        '<!-- مكون الإشعارات المحسن -->',
        $css_link . "\n<!-- مكون الإشعارات المحسن -->",
        $notifications_content
    );
    
    if (file_put_contents('notifications_fixed.php', $updated_notifications)) {
        echo "<p style='color: green;'>✅ تم تحديث ملف الإشعارات لتضمين CSS</p>";
    }
}

echo "<h3>اختبار النظام:</h3>";
echo "<p>للتأكد من عمل النظام بشكل صحيح:</p>";
echo "<ol>";
echo "<li>قم بزيارة أي صفحة من صفحات الأدمن</li>";
echo "<li>ابحث عن زر الإشعارات الأزرق في الزاوية العلوية اليسرى</li>";
echo "<li>انقر على الزر لفتح قائمة الإشعارات</li>";
echo "<li>تأكد من أن القائمة تظهر بالكامل دون اختفاء أي جزء</li>";
echo "</ol>";

echo "<h3>الصفحات المحدثة:</h3>";
echo "<ul>";
foreach ($admin_pages as $page) {
    if (file_exists($page)) {
        echo "<li><a href='$page' target='_blank'>$page</a></li>";
    }
}
echo "</ul>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='home.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للرئيسية</a>";
echo "<a href='test_notifications.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a>";
echo "</p>";
?>
