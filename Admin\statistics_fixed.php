<?php
session_start();
try {
    if (isset($_SESSION['user'])) {
        if ($_SESSION['user']->role === "Admin") {
            // Admin logic here
        } else {
            header("location:../login.php", true);
            die("");
        }
    } else {
        header("location:../login.php", true);
        die("");
    }
} catch (Exception $e) {
    echo "Error in session handling: " . $e->getMessage();
}

try {
    $user = 'kidzrcle_rwda';
    $pass = 'kidzrcle_rwda';
    $database = new PDO("mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8;", $user, $pass);

    $con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
    $con->set_charset("utf8");
    if ($con->connect_error) {
        throw new Exception("Connection failed: " . $con->connect_error);
    }

    $datenow = date('Y-m-d');
    $selected_user = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

    // جلب جميع المستخدمين
    $users_query = "SELECT u.id_user, u.user_name, COUNT(s.id) as student_count
                    FROM users_tb u
                    LEFT JOIN stud_tb s ON u.id_user = s.userID
                    WHERE u.role = 'User'
                    GROUP BY u.id_user, u.user_name
                    ORDER BY u.user_name";
    $users_result = mysqli_query($con, $users_query);

    if ($selected_user > 0) {
        // إحصائيات مستخدم محدد
        $user_condition = "AND s.userID = $selected_user";
        $pay_condition = "AND s.userID = $selected_user";
    } else {
        // إحصائيات عامة
        $user_condition = "";
        $pay_condition = "";
    }

    // الاستعلامات الأساسية
    $total_students_query = "SELECT COUNT(*) as count FROM stud_tb s WHERE 1=1 $user_condition";
    $total_students = mysqli_fetch_assoc(mysqli_query($con, $total_students_query))['count'];

    $active_students_query = "SELECT COUNT(*) as count FROM stud_tb s 
                             JOIN stud_pay sp ON s.id = sp.id_stud 
                             WHERE sp.date_exp >= '$datenow' $user_condition";
    $active_students = mysqli_fetch_assoc(mysqli_query($con, $active_students_query))['count'];

    $expired_students_query = "SELECT COUNT(*) as count FROM stud_tb s 
                              JOIN stud_pay sp ON s.id = sp.id_stud 
                              WHERE sp.date_exp < '$datenow' $user_condition";
    $expired_students = mysqli_fetch_assoc(mysqli_query($con, $expired_students_query))['count'];

    $expiring_soon_query = "SELECT COUNT(*) as count FROM stud_tb s 
                           JOIN stud_pay sp ON s.id = sp.id_stud 
                           WHERE sp.date_exp BETWEEN '$datenow' AND DATE_ADD('$datenow', INTERVAL 3 DAY) $user_condition";
    $expiring_soon = mysqli_fetch_assoc(mysqli_query($con, $expiring_soon_query))['count'];

    // إحصائيات الفئات
    $categories = ['روضة', 'حضانة', 'تمهيدي', 'تحضيري'];
    $category_stats = [];
    foreach ($categories as $category) {
        $cat_query = "SELECT COUNT(*) as count FROM stud_tb s WHERE s.catg = '$category' $user_condition";
        $category_stats[$category] = mysqli_fetch_assoc(mysqli_query($con, $cat_query))['count'];
    }

    // إحصائيات المصاريف
    $total_expenses_query = "SELECT COUNT(*) as count, SUM(amount) as total FROM depit_tb d 
                            JOIN users_tb u ON d.user_id = u.id_user 
                            WHERE 1=1 " . ($selected_user > 0 ? "AND d.user_id = $selected_user" : "");
    $expenses_result = mysqli_fetch_assoc(mysqli_query($con, $total_expenses_query));
    $total_expenses_count = $expenses_result['count'] ?? 0;
    $total_expenses_amount = $expenses_result['total'] ?? 0;

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات - أكاديمية الأطفال</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/admin_navbar.css">
    <link rel="icon" href="css/icon.ico">
    
    <?php include "addon/topbar.php"; ?>
    
    <style>
        .statistics-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .user-selector {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .user-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .category-stats {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .category-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e0e0e0;
        }

        .print-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .selected-user-info {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .statistics-container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .stat-card {
                padding: 20px;
            }
            
            .stat-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="statistics-container">
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> إحصائيات أكاديمية الأطفال</h1>
            <p>تقرير شامل عن حالة الطلاب والمصاريف</p>
        </div>

        <div class="user-selector">
            <h3><i class="fas fa-user-friends"></i> اختيار المستخدم</h3>
            <form method="GET" action="">
                <select name="user_id" onchange="this.form.submit()">
                    <option value="0">جميع المستخدمين</option>
                    <?php while ($user_row = mysqli_fetch_assoc($users_result)): ?>
                        <option value="<?php echo $user_row['id_user']; ?>" 
                                <?php echo ($selected_user == $user_row['id_user']) ? 'selected' : ''; ?>>
                            <?php echo $user_row['user_name']; ?> (<?php echo $user_row['student_count']; ?> طالب)
                        </option>
                    <?php endwhile; ?>
                </select>
            </form>
        </div>

        <?php if ($selected_user > 0): ?>
            <?php
            $selected_user_info = mysqli_fetch_assoc(mysqli_query($con, "SELECT user_name FROM users_tb WHERE id_user = $selected_user"));
            ?>
            <div class="selected-user-info">
                <h2><i class="fas fa-user"></i> إحصائيات المستخدم: <?php echo $selected_user_info['user_name']; ?></h2>
                <button class="print-btn" onclick="printUserStatistics()">
                    <i class="fas fa-print"></i> طباعة الإحصائيات
                </button>
            </div>
        <?php endif; ?>

        <div class="stats-grid">
            <div class="stat-card" onclick="showStudentDetails('total')">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_students); ?></div>
                <div class="stat-label">إجمالي الطلاب</div>
            </div>

            <div class="stat-card" onclick="showStudentDetails('active')">
                <div class="stat-icon">
                    <i class="fas fa-user-check" style="color: #28a745;"></i>
                </div>
                <div class="stat-number"><?php echo number_format($active_students); ?></div>
                <div class="stat-label">الطلاب النشطون</div>
            </div>

        <div class="category-stats">
            <h3><i class="fas fa-layer-group"></i> إحصائيات الفئات</h3>
            <div class="category-grid">
                <?php foreach ($category_stats as $category => $count): ?>
                    <div class="category-item" onclick="showCategoryDetails('<?php echo $category; ?>')">
                        <h4><?php echo htmlspecialchars($category); ?></h4>
                        <div class="stat-number" style="font-size: 1.5rem; color: #667eea;">
                            <?php echo number_format($count); ?>
                        </div>
                        <div class="stat-label">طالب</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="print-btn" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <a href="home.php" class="print-btn" style="background: #6c757d;">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <script>
        // وظيفة عرض تفاصيل الطلاب
        function showDetails(type) {
            const search = <?php echo $search; ?>;
            let url = '';

            switch(type) {
                case 'total':
                    url = search > 0 ? `allstud.php?search=${search}` : 'allstud.php';
                    break;
                case 'active':
                    url = search > 0 ? `new_stud.php?search=${search}` : 'new_stud.php';
                    break;
                case 'expired':
                    url = search > 0 ? `expired_stud.php?search=${search}` : 'expired_stud.php';
                    break;
                case 'soon':
                    url = search > 0 ? `expried_soon.php?search=${search}` : 'expried_soon.php';
                    break;
                case 'new':
                    url = search > 0 ? `new_stud.php?search=${search}` : 'new_stud.php';
                    break;
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        // وظيفة عرض تفاصيل المصاريف
        function showExpenseDetails() {
            const search = <?php echo $search; ?>;
            window.open(search > 0 ? `info_depit.php?search=${search}` : 'info_depit.php', '_blank');
        }

        // وظيفة عرض تفاصيل الفئات
        function showCategoryDetails(category) {
            const search = <?php echo $search; ?>;
            let url = '';

            switch(category) {
                case 'روضة':
                    url = search > 0 ? `rodaStud.php?search=${search}` : 'rodaStud.php';
                    break;
                case 'حضانة':
                    url = search > 0 ? `hadanaStud.php?search=${search}` : 'hadanaStud.php';
                    break;
                case 'تمهيدي':
                    url = search > 0 ? `tamhediStud.php?search=${search}` : 'tamhediStud.php';
                    break;
                case 'تحضيري':
                    url = search > 0 ? `tadeery.php?search=${search}` : 'tadeery.php';
                    break;
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        // تأثيرات تفاعلية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.stat-card').forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;

                // تأثير العد التصاعدي للأرقام
                const numberElement = card.querySelector('.stat-number');
                if (numberElement) {
                    const targetNumber = parseInt(numberElement.textContent.replace(/,/g, ''));

                    if (!isNaN(targetNumber) && targetNumber > 0) {
                        let currentNumber = 0;
                        const increment = targetNumber / 50;
                        const timer = setInterval(() => {
                            currentNumber += increment;
                            if (currentNumber >= targetNumber) {
                                currentNumber = targetNumber;
                                clearInterval(timer);
                            }
                            numberElement.textContent = Math.floor(currentNumber).toLocaleString();
                        }, 30);
                    }
                }
            });

            console.log('📊 صفحة الإحصائيات جاهزة!');
        });
    </script>
</body>
</html>

            <div class="stat-card" onclick="showStudentDetails('expired')">
                <div class="stat-icon">
                    <i class="fas fa-user-times" style="color: #dc3545;"></i>
                </div>
                <div class="stat-number"><?php echo number_format($expired_students); ?></div>
                <div class="stat-label">منتهي الاشتراك</div>
            </div>

            <div class="stat-card" onclick="showStudentDetails('expiring')">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                </div>
                <div class="stat-number"><?php echo number_format($expiring_soon); ?></div>
                <div class="stat-label">قريب الانتهاء</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-receipt" style="color: #6f42c1;"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_expenses_count); ?></div>
                <div class="stat-label">إجمالي المصاريف</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave" style="color: #fd7e14;"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_expenses_amount); ?></div>
                <div class="stat-label">المبلغ الإجمالي</div>
            </div>
        </div>

        <div class="category-stats">
            <h3><i class="fas fa-layer-group"></i> إحصائيات الفئات</h3>
            <div class="category-grid">
                <?php foreach ($category_stats as $category => $count): ?>
                    <div class="category-item">
                        <h4><?php echo $category; ?></h4>
                        <div class="stat-number" style="font-size: 1.5rem; color: #667eea;">
                            <?php echo number_format($count); ?>
                        </div>
                        <div class="stat-label">طالب</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="print-btn" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <a href="home.php" class="print-btn" style="background: #6c757d; text-decoration: none;">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <script>
        // وظيفة عرض تفاصيل الطلاب
        function showStudentDetails(type) {
            const userId = <?php echo $selected_user; ?>;
            let url = '';
            
            switch(type) {
                case 'total':
                    url = userId > 0 ? `allstud.php?user_id=${userId}` : 'allstud.php';
                    break;
                case 'active':
                    url = userId > 0 ? `new_stud.php?user_id=${userId}` : 'new_stud.php';
                    break;
                case 'expired':
                    url = userId > 0 ? `expired_stud.php?user_id=${userId}` : 'expired_stud.php';
                    break;
                case 'expiring':
                    url = userId > 0 ? `expried_soon.php?user_id=${userId}` : 'expried_soon.php';
                    break;
            }
            
            if (url) {
                window.open(url, '_blank');
            }
        }

        // وظيفة طباعة إحصائيات المستخدم
        function printUserStatistics() {
            window.print();
        }

        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.stat-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            
            // تأثير العد التصاعدي للأرقام
            const numberElement = card.querySelector('.stat-number');
            const targetNumber = parseInt(numberElement.textContent.replace(/,/g, ''));
            
            if (!isNaN(targetNumber) && targetNumber > 0) {
                let currentNumber = 0;
                const increment = targetNumber / 50;
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= targetNumber) {
                        currentNumber = targetNumber;
                        clearInterval(timer);
                    }
                    numberElement.textContent = Math.floor(currentNumber).toLocaleString();
                }, 30);
            }
        });

        console.log('📊 صفحة الإحصائيات جاهزة!');
    </script>
</body>
</html>
