# نظام إدارة الاحتياجات وطلبات الإجازة

## نظرة عامة
تم إضافة نظام شامل لإدارة الاحتياجات وطلبات الإجازة إلى نظام إدارة الروضة. يتيح هذا النظام للمستخدمين تقديم طلبات الاحتياجات والإجازات، وللإدارة والمدققين مراجعة هذه الطلبات والرد عليها.

## المكونات المضافة

### 1. قاعدة البيانات
- **جدول needs_requests**: لتخزين طلبات الاحتياجات
- **جدول leave_requests**: لتخزين طلبات الإجازة
- **ملف SQL**: `create_needs_leave_tables.sql` لإنشاء الجداول

### 2. واجهات المستخدمين
#### صفحات المستخدم (Users/)
- `request_need.php` - تقديم طلب احتياج جديد
- `my_needs.php` - عرض طلبات الاحتياجات المقدمة
- `request_leave.php` - تقديم طلب إجازة جديد
- `my_leaves.php` - عرض طلبات الإجازة المقدمة
- `home.php` - تم تحديثها لإضافة أزرار النظام الجديد

#### صفحات الإدارة (Admin/)
- `manage_needs.php` - إدارة ومراجعة طلبات الاحتياجات
- `manage_leaves.php` - إدارة ومراجعة طلبات الإجازة
- `home.php` - تم تحديثها لإضافة أزرار الإدارة

#### صفحات المدقق (Moda/)
- `manage_needs.php` - إدارة ومراجعة طلبات الاحتياجات
- `manage_leaves.php` - إدارة ومراجعة طلبات الإجازة
- `home.php` - تم تحديثها لإضافة أزرار الإدارة

## خطوات التشغيل

### 1. إنشاء الجداول
```sql
-- تشغيل الملف SQL في قاعدة البيانات
SOURCE create_needs_leave_tables.sql;
```

أو نسخ محتوى الملف وتشغيله في phpMyAdmin أو أي أداة إدارة قاعدة بيانات.

### 2. التحقق من الصلاحيات
تأكد من أن المستخدمين لديهم الأدوار المناسبة:
- **User**: للوصول إلى صفحات تقديم الطلبات
- **Admin**: للوصول إلى صفحات الإدارة
- **Mod**: للوصول إلى صفحات المراجعة

## الميزات الرئيسية

### نظام الاحتياجات
- **تقديم الطلبات**: المستخدمون يمكنهم تقديم طلبات احتياج أو صيانة
- **تتبع الحالة**: قيد المراجعة، تم التوفير، لم يتوفر
- **الفلترة**: حسب التاريخ، المستخدم، الحالة
- **الردود**: الإدارة يمكنها كتابة ردود مفصلة

### نظام الإجازات
- **أنواع الإجازة**: مرضية، عرضية، طارئة، زمنية، ظروف أخرى
- **حساب الأيام**: تلقائي بناءً على تاريخ البداية والنهاية
- **الموافقة/الرفض**: الإدارة يمكنها الموافقة أو رفض الطلبات
- **التتبع**: المستخدمون يمكنهم تتبع حالة طلباتهم

## التصميم والواجهة
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم العربية**: واجهة باللغة العربية مع دعم RTL
- **ألوان متدرجة**: تصميم عصري بألوان جذابة
- **رموز تعبيرية**: استخدام Font Awesome للرموز

## الأمان
- **التحقق من الجلسة**: جميع الصفحات محمية بنظام الجلسات
- **التحقق من الأدوار**: كل صفحة تتحقق من صلاحيات المستخدم
- **Prepared Statements**: حماية من SQL Injection
- **تنظيف البيانات**: استخدام htmlspecialchars لمنع XSS

## الاستخدام

### للمستخدمين
1. تسجيل الدخول بحساب User
2. الذهاب إلى الصفحة الرئيسية
3. اختيار "طلب احتياج" أو "طلب إجازة"
4. ملء النموذج وإرساله
5. متابعة الحالة من "احتياجاتي" أو "إجازاتي"

### للإدارة والمدققين
1. تسجيل الدخول بحساب Admin أو Mod
2. الذهاب إلى "الاحتياجات" أو "طلبات الإجازة"
3. استخدام الفلاتر لتصفية الطلبات
4. مراجعة الطلبات والرد عليها
5. تحديث الحالة وكتابة الملاحظات

## الدعم الفني
في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق التطوير.

## ملاحظات مهمة
- تأكد من تشغيل ملف SQL قبل استخدام النظام
- جميع الطلبات تبدأ بحالة "قيد المراجعة"
- يمكن للإدارة والمدققين فقط تغيير حالة الطلبات
- البيانات محفوظة بشكل آمن مع إمكانية التتبع الكامل
