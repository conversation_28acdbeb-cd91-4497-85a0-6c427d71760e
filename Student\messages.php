<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

$student = $_SESSION['student'];
$student_id = $student->id;

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_message'])) {
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);
    
    if (!empty($subject) && !empty($message)) {
        $insert_query = "INSERT INTO app_messages (student_id, sender_type, sender_id, subject, message, message_type) 
                        VALUES ($student_id, 'student', $student_id, '$subject', '$message', 'general')";
        if (mysqli_query($con, $insert_query)) {
            $success_message = "تم إرسال رسالتك بنجاح! سيتم الرد عليك قريباً.";
        }
    }
}

// تحديث حالة قراءة الرسائل
$update_messages = "UPDATE app_messages SET is_read = 1 
                   WHERE student_id = $student_id AND sender_type != 'student'";
mysqli_query($con, $update_messages);

// تحديث حالة قراءة الإشعارات المتعلقة بالرسائل
$update_notifications = "UPDATE app_notifications SET is_read = 1 
                        WHERE student_id = $student_id AND type = 'message'";
mysqli_query($con, $update_notifications);

// إنشاء جدول الرسائل إذا لم يكن موجوداً
$create_table = "CREATE TABLE IF NOT EXISTS app_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    sender_type VARCHAR(50) DEFAULT 'admin',
    sender_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'general',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
@mysqli_query($con, $create_table);

// جلب الرسائل
$messages_query = "SELECT m.*, 
                   CASE 
                       WHEN m.sender_type = 'admin' THEN 'الإدارة'
                       WHEN m.sender_type = 'teacher' THEN 'المعلم'
                       WHEN m.sender_type = 'student' THEN 'أنت'
                   END as sender_name
                   FROM app_messages m 
                   WHERE m.student_id = $student_id 
                   ORDER BY m.created_at DESC";
$messages_result = mysqli_query($con, $messages_query);

// إضافة رسائل افتراضية إذا لم توجد
if (mysqli_num_rows($messages_result) == 0) {
    $default_messages = [
        [
            'subject' => 'مرحباً بك في أكاديمية كيدز',
            'message' => 'نرحب بك في أكاديمية كيدز! نتمنى لك عاماً دراسياً مليئاً بالتعلم والمرح. إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.',
            'sender_type' => 'admin',
            'message_type' => 'general'
        ],
        [
            'subject' => 'تذكير: موعد النشاط القادم',
            'message' => 'نذكرك بموعد النشاط الفني القادم يوم الأحد الساعة 10:00 صباحاً. يرجى إحضار الأدوات المطلوبة.',
            'sender_type' => 'teacher',
            'message_type' => 'important'
        ]
    ];
    
    foreach ($default_messages as $msg) {
        $insert_query = "INSERT INTO app_messages (student_id, sender_type, sender_id, subject, message, message_type) 
                        VALUES ($student_id, '{$msg['sender_type']}', 1, '{$msg['subject']}', '{$msg['message']}', '{$msg['message_type']}')";
        mysqli_query($con, $insert_query);
    }
    
    // إعادة جلب الرسائل
    $messages_result = mysqli_query($con, $messages_query);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#9b59b6">
    <title>💬 الرسائل - أكاديمية كيدز</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/app-style.css">
    <style>
        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .back-btn, .compose-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .app-content {
            margin-top: 70px;
            padding: 1rem;
            padding-bottom: 80px;
        }

        .message-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 3px solid rgba(255, 255, 255, 0.8);
            cursor: pointer;
        }

        .message-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .sender-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .sender-admin .sender-avatar { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .sender-teacher .sender-avatar { background: linear-gradient(45deg, #3498db, #2980b9); }
        .sender-student .sender-avatar { background: linear-gradient(45deg, #27ae60, #2ecc71); }

        .message-meta {
            flex: 1;
        }

        .message-subject {
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: bold;
            margin: 0 0 0.3rem 0;
        }

        .message-info {
            color: #7f8c8d;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .message-preview {
            color: #34495e;
            line-height: 1.5;
            margin-top: 0.8rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .message-type-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .type-general { background: #3498db; color: white; }
        .type-important { background: #f39c12; color: white; }
        .type-urgent { background: #e74c3c; color: white; }
        .type-personal { background: #9b59b6; color: white; }

        .compose-form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 3px solid rgba(255, 255, 255, 0.8);
            display: none;
        }

        .compose-form.show {
            display: block;
            animation: slideInDown 0.5s ease;
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 0.8rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #9b59b6;
            box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
        }

        .btn-send {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 0.8rem 1.5rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-send:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
            color: white;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 3px solid rgba(255, 107, 107, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #7f8c8d;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 15px;
        }

        .nav-item.active, .nav-item:hover {
            color: #9b59b6;
            background: rgba(155, 89, 182, 0.1);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .nav-item span {
            font-size: 0.7rem;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .message-card {
                padding: 1rem;
            }
            
            .message-header {
                gap: 0.8rem;
            }
            
            .sender-avatar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
            
            .message-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التطبيق العلوي -->
    <div class="app-header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
        </a>
        <h1>💬 الرسائل</h1>
        <button class="compose-btn" onclick="toggleComposeForm()">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="app-content">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success animate__animated animate__bounceIn">
                <i class="fas fa-check-circle"></i>
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <!-- نموذج إرسال رسالة جديدة -->
        <div class="compose-form" id="composeForm">
            <h4 style="color: #2c3e50; margin-bottom: 1rem;">
                <i class="fas fa-edit"></i> رسالة جديدة
            </h4>
            <form method="POST">
                <div class="mb-3">
                    <label for="subject" class="form-label">الموضوع:</label>
                    <input type="text" id="subject" name="subject" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label for="message" class="form-label">الرسالة:</label>
                    <textarea id="message" name="message" class="form-control" rows="4" required></textarea>
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" name="send_message" class="btn btn-send">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="toggleComposeForm()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة الرسائل -->
        <?php if (mysqli_num_rows($messages_result) > 0): ?>
            <?php while ($message = mysqli_fetch_assoc($messages_result)): ?>
                <div class="message-card sender-<?php echo $message['sender_type']; ?> animate__animated animate__fadeInUp" 
                     onclick="openMessage(<?php echo $message['id']; ?>)">
                    <div class="message-header">
                        <div class="sender-avatar">
                            <?php 
                            echo $message['sender_type'] == 'admin' ? 'إ' : 
                                ($message['sender_type'] == 'teacher' ? 'م' : 'أ');
                            ?>
                        </div>
                        <div class="message-meta">
                            <h3 class="message-subject"><?php echo htmlspecialchars($message['subject']); ?></h3>
                            <div class="message-info">
                                <span><i class="fas fa-user"></i> <?php echo $message['sender_name']; ?></span>
                                <span><i class="fas fa-clock"></i> <?php echo date('Y/m/d H:i', strtotime($message['created_at'])); ?></span>
                                <span class="message-type-badge type-<?php echo $message['message_type']; ?>">
                                    <?php 
                                    echo $message['message_type'] == 'general' ? 'عامة' : 
                                        ($message['message_type'] == 'important' ? 'مهمة' : 
                                        ($message['message_type'] == 'urgent' ? 'عاجلة' : 'شخصية'));
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="message-preview">
                        <?php echo htmlspecialchars($message['message']); ?>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-envelope-open"></i>
                <h3>لا توجد رسائل</h3>
                <p>ستظهر رسائلك هنا عند وصولها</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- شريط التنقل السفلي -->
    <div class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="activities.php" class="nav-item">
            <i class="fas fa-palette"></i>
            <span>الأنشطة</span>
        </a>
        <a href="schedule.php" class="nav-item">
            <i class="fas fa-calendar"></i>
            <span>الجدول</span>
        </a>
        <a href="messages.php" class="nav-item active">
            <i class="fas fa-comments"></i>
            <span>الرسائل</span>
        </a>
        <a href="news.php" class="nav-item">
            <i class="fas fa-newspaper"></i>
            <span>الأخبار</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الدخول
            const messageCards = document.querySelectorAll('.message-card');
            messageCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate__animated', 'animate__fadeInUp');
                }, index * 100);
            });

            // تأثيرات اللمس للهواتف
            if (isMobileDevice()) {
                addTouchEffects();
            }
        });

        function toggleComposeForm() {
            const form = document.getElementById('composeForm');
            form.classList.toggle('show');
            
            if (form.classList.contains('show')) {
                form.scrollIntoView({ behavior: 'smooth' });
                setTimeout(() => {
                    document.getElementById('subject').focus();
                }, 500);
            }
        }

        function openMessage(messageId) {
            // تأثير بصري
            event.currentTarget.style.transform = 'scale(0.98)';
            setTimeout(() => {
                event.currentTarget.style.transform = 'scale(1)';
                // هنا يمكن إضافة منطق فتح الرسالة في صفحة منفصلة
                showMessageDetails(messageId);
            }, 150);
        }

        function showMessageDetails(messageId) {
            // عرض تفاصيل الرسالة في نافذة منبثقة
            alert('سيتم فتح تفاصيل الرسالة قريباً!');
        }

        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        function addTouchEffects() {
            const touchElements = document.querySelectorAll('.message-card, .nav-item, .back-btn, .compose-btn');
            
            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s';
                });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        }
    </script>
</body>
</html>
