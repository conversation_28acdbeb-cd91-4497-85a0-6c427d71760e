<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول معلومات التواصل
$create_contact_info = "CREATE TABLE IF NOT EXISTS contact_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    info_key VARCHAR(100) NOT NULL UNIQUE,
    info_value TEXT,
    info_type VARCHAR(50) DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_contact_info);

$message = '';
$messageType = 'success';

// معالجة حفظ معلومات التواصل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_contact'])) {
    $contact_fields = [
        'school_name', 'school_address', 'phone_1', 'phone_2', 'email', 
        'website', 'facebook', 'twitter', 'instagram', 'whatsapp',
        'working_hours', 'description'
    ];
    
    foreach ($contact_fields as $field) {
        if (isset($_POST[$field])) {
            $value = mysqli_real_escape_string($con, $_POST[$field]);
            $sql = "INSERT INTO contact_info (info_key, info_value) VALUES ('$field', '$value') 
                    ON DUPLICATE KEY UPDATE info_value = '$value', updated_at = NOW()";
            @mysqli_query($con, $sql);
        }
    }
    $message = 'تم حفظ معلومات التواصل بنجاح!';
}

// جلب معلومات التواصل الحالية
$contact_info = [];
$contact_result = @mysqli_query($con, "SELECT * FROM contact_info");
if ($contact_result) {
    while ($row = mysqli_fetch_assoc($contact_result)) {
        $contact_info[$row['info_key']] = $row['info_value'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 معلومات التواصل - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container {
            max-width: 900px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-save:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px 0 0 15px;
            padding: 1rem;
        }

        .input-group .form-control {
            border-radius: 0 15px 15px 0;
            border-right: none;
        }

        .social-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .facebook { background: #3b5998; }
        .twitter { background: #1da1f2; }
        .instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
        .whatsapp { background: #25d366; }
        .website { background: #667eea; }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .social-links {
                gap: 0.5rem;
            }
            
            .social-link {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container">
        <!-- Header -->
        <div class="header-card">
            <h1>📞 معلومات التواصل</h1>
            <p style="color: #7f8c8d; margin: 0;">تحديث معلومات التواصل والعنوان وأرقام الهواتف</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom alert-success-custom">
                <i class="fas fa-check-circle"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Contact Form -->
        <div class="contact-card">
            <form method="POST">
                <h3 class="section-title">
                    <i class="fas fa-school"></i>
                    معلومات المؤسسة
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-graduation-cap"></i>
                            اسم المؤسسة:
                        </label>
                        <input type="text" name="school_name" class="form-control" 
                               value="<?php echo htmlspecialchars($contact_info['school_name'] ?? 'أكاديمية كيدز'); ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني:
                        </label>
                        <input type="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($contact_info['email'] ?? ''); ?>" placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-map-marker-alt"></i>
                        العنوان:
                    </label>
                    <textarea name="school_address" class="form-control" rows="3" placeholder="العنوان الكامل للمؤسسة..."><?php echo htmlspecialchars($contact_info['school_address'] ?? ''); ?></textarea>
                </div>

                <h3 class="section-title">
                    <i class="fas fa-phone"></i>
                    أرقام الهواتف
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-phone"></i>
                            الهاتف الأول:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" name="phone_1" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['phone_1'] ?? ''); ?>" placeholder="+966 50 123 4567">
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-phone"></i>
                            الهاتف الثاني:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" name="phone_2" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['phone_2'] ?? ''); ?>" placeholder="+966 50 123 4568">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">
                        <i class="fab fa-whatsapp"></i>
                        رقم الواتساب:
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fab fa-whatsapp"></i>
                        </span>
                        <input type="tel" name="whatsapp" class="form-control" 
                               value="<?php echo htmlspecialchars($contact_info['whatsapp'] ?? ''); ?>" placeholder="+966 50 123 4567">
                    </div>
                </div>

                <h3 class="section-title">
                    <i class="fas fa-globe"></i>
                    وسائل التواصل الاجتماعي
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-globe"></i>
                            الموقع الإلكتروني:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-globe"></i>
                            </span>
                            <input type="url" name="website" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['website'] ?? ''); ?>" placeholder="https://kidz.com">
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fab fa-facebook"></i>
                            فيسبوك:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fab fa-facebook"></i>
                            </span>
                            <input type="url" name="facebook" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['facebook'] ?? ''); ?>" placeholder="https://facebook.com/kidz">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fab fa-twitter"></i>
                            تويتر:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fab fa-twitter"></i>
                            </span>
                            <input type="url" name="twitter" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['twitter'] ?? ''); ?>" placeholder="https://twitter.com/kidz">
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fab fa-instagram"></i>
                            إنستغرام:
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fab fa-instagram"></i>
                            </span>
                            <input type="url" name="instagram" class="form-control" 
                                   value="<?php echo htmlspecialchars($contact_info['instagram'] ?? ''); ?>" placeholder="https://instagram.com/kidz">
                        </div>
                    </div>
                </div>

                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات إضافية
                </h3>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-clock"></i>
                        ساعات العمل:
                    </label>
                    <input type="text" name="working_hours" class="form-control" 
                           value="<?php echo htmlspecialchars($contact_info['working_hours'] ?? ''); ?>" placeholder="الأحد - الخميس: 7:00 ص - 2:00 م">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-info-circle"></i>
                        وصف المؤسسة:
                    </label>
                    <textarea name="description" class="form-control" rows="4" placeholder="وصف مختصر عن المؤسسة وأهدافها..."><?php echo htmlspecialchars($contact_info['description'] ?? ''); ?></textarea>
                </div>
                
                <button type="submit" name="save_contact" class="btn-save">
                    <i class="fas fa-save"></i> حفظ معلومات التواصل
                </button>
            </form>

            <!-- Social Media Preview -->
            <?php if (!empty($contact_info['facebook']) || !empty($contact_info['twitter']) || !empty($contact_info['instagram']) || !empty($contact_info['whatsapp']) || !empty($contact_info['website'])): ?>
                <div class="social-preview">
                    <h5 style="color: #2c3e50; margin-bottom: 1rem;">
                        <i class="fas fa-eye"></i> معاينة وسائل التواصل
                    </h5>
                    <div class="social-links">
                        <?php if (!empty($contact_info['website'])): ?>
                            <a href="<?php echo htmlspecialchars($contact_info['website']); ?>" target="_blank" class="social-link website">
                                <i class="fas fa-globe"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($contact_info['facebook'])): ?>
                            <a href="<?php echo htmlspecialchars($contact_info['facebook']); ?>" target="_blank" class="social-link facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($contact_info['twitter'])): ?>
                            <a href="<?php echo htmlspecialchars($contact_info['twitter']); ?>" target="_blank" class="social-link twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($contact_info['instagram'])): ?>
                            <a href="<?php echo htmlspecialchars($contact_info['instagram']); ?>" target="_blank" class="social-link instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($contact_info['whatsapp'])): ?>
                            <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $contact_info['whatsapp']); ?>" target="_blank" class="social-link whatsapp">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // Form submission loading state
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.btn-save');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
