<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../../login.php");
    exit();
}

include "dbcon.php";

// التحقق من الاتصال بقاعدة البيانات
if (!$con) {
    die("خطأ في الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

// الحصول على المعاملات
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$category = isset($_GET['category']) ? $_GET['category'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search_term = isset($_GET['search_term']) ? $_GET['search_term'] : '';

// تحديد اسم الملف
$filename = 'تقرير_المحاسبة_' . date('Y-m-d_H-i-s') . '.csv';

// تحديد headers للتصدير
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// فتح output stream
$output = fopen('php://output', 'w');

// إضافة BOM للدعم العربي في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة headers
fputcsv($output, ['التاريخ', 'النوع', 'الوصف', 'المبلغ', 'المستخدم', 'الفئة'], ',', '"');

// بناء الاستعلام حسب النوع
$data = [];

if ($category == 'revenue' || $category == '') {
    // الإيرادات من مدفوعات الطلاب
    $revenue_query = "SELECT 
        sp.id_pay as id,
        'إيرادات' as type,
        CONCAT('دفعة طالب: ', st.name) as description,
        sp.cash_stud as amount,
        sp.datein as date,
        u.user_name as user_name,
        'إيراد' as category
    FROM stud_pay sp
    JOIN stud_tb st ON sp.id_stud = st.id
    JOIN users_tb u ON st.userID = u.id_user
    WHERE sp.cash_stud > 0";
    
    // إضافة فلاتر
    if ($user_id > 0) {
        $revenue_query .= " AND st.userID = $user_id";
    }
    
    if ($date_from) {
        $revenue_query .= " AND sp.datein >= '$date_from'";
    }
    
    if ($date_to) {
        $revenue_query .= " AND sp.datein <= '$date_to'";
    }
    
    if ($search_term) {
        $revenue_query .= " AND (st.name LIKE '%$search_term%' OR sp.cash_stud LIKE '%$search_term%')";
    }
    
    $revenue_result = mysqli_query($con, $revenue_query);
    if ($revenue_result) {
        while ($row = mysqli_fetch_assoc($revenue_result)) {
            $data[] = $row;
        }
    }
}

if ($category == 'expenses' || $category == '') {
    // المصروفات
    $expenses_query = "SELECT 
        d.id as id,
        'مصروفات' as type,
        d.depit_note as description,
        d.depit_cash as amount,
        d.depit_date as date,
        u.user_name as user_name,
        'مصروف' as category
    FROM depit_tb d
    JOIN users_tb u ON d.userID = u.id_user
    WHERE d.depit_cash > 0";
    
    // إضافة فلاتر
    if ($user_id > 0) {
        $expenses_query .= " AND d.userID = $user_id";
    }
    
    if ($date_from) {
        $expenses_query .= " AND d.depit_date >= '$date_from'";
    }
    
    if ($date_to) {
        $expenses_query .= " AND d.depit_date <= '$date_to'";
    }
    
    if ($search_term) {
        $expenses_query .= " AND (d.depit_note LIKE '%$search_term%' OR d.depit_cash LIKE '%$search_term%')";
    }
    
    $expenses_result = mysqli_query($con, $expenses_query);
    if ($expenses_result) {
        while ($row = mysqli_fetch_assoc($expenses_result)) {
            $data[] = $row;
        }
    }
}

if ($category == 'salaries' || $category == '') {
    // الرواتب
    $salaries_query = "SELECT 
        e.id as id,
        'رواتب' as type,
        CONCAT('راتب: ', e.f_name, ' - ', e.job) as description,
        e.salary as amount,
        e.date_start as date,
        u.user_name as user_name,
        'راتب' as category
    FROM employ_tb e
    JOIN users_tb u ON e.userID = u.id_user
    WHERE e.salary > 0";
    
    // إضافة فلاتر
    if ($user_id > 0) {
        $salaries_query .= " AND e.userID = $user_id";
    }
    
    if ($date_from) {
        $salaries_query .= " AND e.date_start >= '$date_from'";
    }
    
    if ($date_to) {
        $salaries_query .= " AND e.date_start <= '$date_to'";
    }
    
    if ($search_term) {
        $salaries_query .= " AND (e.f_name LIKE '%$search_term%' OR e.job LIKE '%$search_term%' OR e.salary LIKE '%$search_term%')";
    }
    
    $salaries_result = mysqli_query($con, $salaries_query);
    if ($salaries_result) {
        while ($row = mysqli_fetch_assoc($salaries_result)) {
            $data[] = $row;
        }
    }
}

// ترتيب البيانات حسب التاريخ
usort($data, function($a, $b) {
    return strtotime($b['date']) - strtotime($a['date']);
});

// كتابة البيانات
foreach ($data as $row) {
    fputcsv($output, [
        date('Y-m-d', strtotime($row['date'])),
        $row['type'],
        $row['description'],
        number_format($row['amount']),
        $row['user_name'],
        $row['category']
    ], ',', '"');
}

// إضافة ملخص في النهاية
$total_revenue = 0;
$total_expenses = 0;
$total_salaries = 0;

foreach ($data as $row) {
    if ($row['type'] == 'إيرادات') {
        $total_revenue += $row['amount'];
    } elseif ($row['type'] == 'مصروفات') {
        $total_expenses += $row['amount'];
    } else {
        $total_salaries += $row['amount'];
    }
}

$net_profit = $total_revenue - $total_expenses - $total_salaries;

// إضافة سطر فارغ
fputcsv($output, ['', '', '', '', '', ''], ',', '"');

// إضافة الملخص
fputcsv($output, ['الملخص', '', '', '', '', ''], ',', '"');
fputcsv($output, ['إجمالي الإيرادات', '', '', number_format($total_revenue), '', ''], ',', '"');
fputcsv($output, ['إجمالي المصروفات', '', '', number_format($total_expenses), '', ''], ',', '"');
fputcsv($output, ['إجمالي الرواتب', '', '', number_format($total_salaries), '', ''], ',', '"');
fputcsv($output, ['صافي الربح', '', '', number_format($net_profit), '', ''], ',', '"');

fclose($output);
mysqli_close($con);
?>
