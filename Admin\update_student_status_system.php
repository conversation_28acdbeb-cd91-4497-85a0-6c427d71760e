<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $con->connect_error);
}

echo "<h2>تحديث نظام حالة الطلاب</h2>";

// التحقق من وجود عمود student_status
$check_column = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'student_status'");
if (mysqli_num_rows($check_column) == 0) {
    echo "<p>إضافة عمود حالة الطالب...</p>";
    $add_column = "ALTER TABLE stud_tb ADD COLUMN student_status ENUM('جديد', 'مشترك') DEFAULT 'جديد'";
    if (mysqli_query($con, $add_column)) {
        echo "<p style='color: green;'>✅ تم إضافة عمود حالة الطالب بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في إضافة عمود حالة الطالب: " . mysqli_error($con) . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ عمود حالة الطالب موجود مسبقاً</p>";
}

// تحديث حالة الطلاب الحاليين
echo "<p>تحديث حالة الطلاب الحاليين...</p>";

// الطلاب الذين تم تجديد اشتراكهم (لديهم أكثر من دفعة واحدة)
$update_renewed = "UPDATE stud_tb s 
                   SET student_status = 'مشترك' 
                   WHERE s.id IN (
                       SELECT DISTINCT sp.id_stud 
                       FROM stud_pay sp 
                       GROUP BY sp.id_stud 
                       HAVING COUNT(*) > 1
                   )";

if (mysqli_query($con, $update_renewed)) {
    $affected_rows = mysqli_affected_rows($con);
    echo "<p style='color: green;'>✅ تم تحديث حالة $affected_rows طالب إلى 'مشترك'</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في تحديث حالة الطلاب المجددين: " . mysqli_error($con) . "</p>";
}

// الطلاب الجدد (تسجيل خلال آخر 30 يوم ولديهم دفعة واحدة فقط)
$datenow = date('Y-m-d');
$date_30_days_ago = date('Y-m-d', strtotime($datenow. ' - 30 days'));

$update_new = "UPDATE stud_tb s 
               SET student_status = 'جديد' 
               WHERE s.datein >= '$date_30_days_ago' 
               AND s.id IN (
                   SELECT DISTINCT sp.id_stud 
                   FROM stud_pay sp 
                   GROUP BY sp.id_stud 
                   HAVING COUNT(*) = 1
               )";

if (mysqli_query($con, $update_new)) {
    $affected_rows = mysqli_affected_rows($con);
    echo "<p style='color: green;'>✅ تم تحديث حالة $affected_rows طالب إلى 'جديد'</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في تحديث حالة الطلاب الجدد: " . mysqli_error($con) . "</p>";
}

echo "<h3>إحصائيات الطلاب بعد التحديث:</h3>";

// عرض الإحصائيات
$stats_query = "SELECT 
                    student_status,
                    COUNT(*) as count
                FROM stud_tb 
                GROUP BY student_status";

$stats_result = mysqli_query($con, $stats_query);
while ($row = mysqli_fetch_assoc($stats_result)) {
    echo "<p>📊 {$row['student_status']}: {$row['count']} طالب</p>";
}

echo "<p style='color: green; font-weight: bold;'>✅ تم تحديث نظام حالة الطلاب بنجاح!</p>";
echo "<p><a href='statistics.php'>العودة إلى صفحة الإحصائيات</a></p>";

$con->close();
?>
