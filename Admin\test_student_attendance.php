<?php
// ملف اختبار لصفحة حضور الطلاب
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

include "addon/dbcon.php";

echo "<h2>اختبار صفحة حضور الطلاب</h2>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
if ($con) {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>";
    exit();
}

// اختبار وجود الجداول المطلوبة
echo "<h3>2. اختبار وجود الجداول:</h3>";

$required_tables = ['stud_tb', 'stat', 'users_tb'];
foreach ($required_tables as $table) {
    $result = $con->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول $table موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
    }
}

// اختبار عدد الطلاب
echo "<h3>3. اختبار بيانات الطلاب:</h3>";
$students_count = $con->query("SELECT COUNT(*) as count FROM stud_tb")->fetch_assoc()['count'];
echo "<p>عدد الطلاب في قاعدة البيانات: <strong>$students_count</strong></p>";

if ($students_count > 0) {
    echo "<p style='color: green;'>✅ توجد بيانات طلاب</p>";
    
    // عرض عينة من الطلاب
    $sample_students = $con->query("SELECT id, name, catg, p_name FROM stud_tb LIMIT 5");
    echo "<h4>عينة من الطلاب:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الرقم</th><th>الاسم</th><th>الصف</th><th>ولي الأمر</th></tr>";
    while ($student = $sample_students->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$student['id']}</td>";
        echo "<td>{$student['name']}</td>";
        echo "<td>{$student['catg']}</td>";
        echo "<td>{$student['p_name']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد بيانات طلاب</p>";
}

// اختبار بيانات الحضور
echo "<h3>4. اختبار بيانات الحضور:</h3>";
$attendance_count = $con->query("SELECT COUNT(*) as count FROM stat")->fetch_assoc()['count'];
echo "<p>عدد سجلات الحضور: <strong>$attendance_count</strong></p>";

if ($attendance_count > 0) {
    echo "<p style='color: green;'>✅ توجد بيانات حضور</p>";
    
    // عرض عينة من سجلات الحضور
    $sample_attendance = $con->query("
        SELECT s.name, st.stat_stud, st.data_stat 
        FROM stat st 
        JOIN stud_tb s ON st.id_stud = s.id 
        ORDER BY st.data_stat DESC 
        LIMIT 5
    ");
    echo "<h4>عينة من سجلات الحضور:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم الطالب</th><th>الحالة</th><th>التاريخ</th></tr>";
    while ($record = $sample_attendance->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$record['name']}</td>";
        echo "<td>{$record['stat_stud']}</td>";
        echo "<td>{$record['data_stat']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد بيانات حضور</p>";
}

// اختبار المستخدمين
echo "<h3>5. اختبار بيانات المستخدمين:</h3>";
$users_count = $con->query("SELECT COUNT(*) as count FROM users_tb WHERE role = 'User'")->fetch_assoc()['count'];
echo "<p>عدد المستخدمين: <strong>$users_count</strong></p>";

if ($users_count > 0) {
    echo "<p style='color: green;'>✅ توجد بيانات مستخدمين</p>";
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد بيانات مستخدمين</p>";
}

// اختبار ملفات API
echo "<h3>6. اختبار ملفات API:</h3>";

$api_files = [
    'student_attendance_data.php' => 'ملف جلب بيانات الحضور',
    'save_student_attendance.php' => 'ملف حفظ بيانات الحضور'
];

foreach ($api_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file) موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) غير موجود</p>";
    }
}

// روابط للاختبار
echo "<h3>7. روابط الاختبار:</h3>";
echo "<p><a href='attandec.php' target='_blank' style='color: blue; text-decoration: underline;'>🔗 فتح صفحة حضور الطلاب الجديدة</a></p>";
echo "<p><a href='employee_attendance.php' target='_blank' style='color: blue; text-decoration: underline;'>🔗 فتح صفحة حضور الموظفين للمقارنة</a></p>";

// اختبار API
echo "<h3>8. اختبار API:</h3>";
$test_date = date('Y-m-d');
echo "<p><a href='student_attendance_data.php?date=$test_date' target='_blank' style='color: blue; text-decoration: underline;'>🔗 اختبار API جلب البيانات</a></p>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، فإن صفحة حضور الطلاب جاهزة للاستخدام!</p>";
echo "<p><strong>للوصول للصفحة:</strong> <a href='attandec.php'>انقر هنا</a></p>";

$con->close();
?>
