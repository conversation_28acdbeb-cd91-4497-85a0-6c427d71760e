<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تجديد الاشتراك</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
<script>
      let x;
    let toast = document.getElementById("toast2"),
         text = document.querySelector(".p1"),
         text2 = document.querySelector(".p2"),
         icon = document.querySelector("#icon")
    function RnewFunction(){
    clearTimeout(x);
    text2.innerText="تم تجديد الاشتراك "
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
        toast.style.transform = "translateX(-500px)"
        
    }, 4000);
    setInterval(()=>{
      window.location.href="home.php"
    },4500)
}

</script>
    <?php 
    $datenow=date('Y-m-d');
    $dateadd=date('Y-m-d', strtotime($datenow. ' + 30 days'));
    $id=$_GET['renewId'];
    $sql="SELECT * FROM stud_tb,stud_pay WHERE id=$id AND stud_pay.id_stud=$id";
    $resul=mysqli_query($con,$sql);
    $row=mysqli_fetch_assoc($resul);
          $id=$row['id'];
          $id_note=$row['id_note'];
          $name=$row['name'];
          $dateexp=$row['date_exp'];
    if(isset($_POST['addS'])){
      $id_pay=$_POST['id_pay'];
      $datein=$_POST['datein'];
      $date_exp=date('Y-m-d',strtotime($datein. ' + 30 days'));
      $cash_stud=$_POST['cash_stud'];
      $addData="UPDATE stud_tb,stud_pay SET id=$id,datein='$datein',date_exp='$date_exp',cash_stud='$cash_stud',id_pay='$id_pay' WHERE id=$id AND id_stud=$id";
      $resul=mysqli_query($con,$addData);
      if($resul){
        $msg1=" ! تمت ";
        $msg2="تم تجديد اشتراك  الطالب بنجاح";
        $iconC="fa fa-circle-check";
       echo "<script>RnewFunction(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
        
      }
    }
    ?>
    <form method="POST">
 <div class='contin'>

 <div class='input-box'>
        <label for="id_note"> معرف الطالب <label>
        <input type="number" placeholder=  "معرف الطالب" name="id_note" required value="<?php echo $row['id_note'];?>" disabled>
        
        <label for="name">اسم الطالب <label>
        <input type="text" name="name" required value="<?php echo $row['name'];?>">
        <label for="id_note"> رقم الوصل <label>
        <input type="number" placeholder="  رقم وصل الاشتراك" name="id_pay" required >
        <label for="datein">تاريخ التسجيل (التجديد) <label>
        <input type="date"  name="datein" required > 
        <label for="cash_stud"> قيمة الاشتراك <label>
        <input type="number" placeholder=" المبلغ  IQD " name="cash_stud" required>
 
       
        <div><button class="btn" name="addS" id="23">تجديد </button></div>
  </div>
  
  
 </div>
   </body>
   
</html>