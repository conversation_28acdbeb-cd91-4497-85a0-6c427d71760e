<?php
session_start();
try {
    if (isset($_SESSION['user'])) {
        if ($_SESSION['user']->role === "Admin") {
            // Admin logic here
        } else {
            header("location:../login.php", true);
            die("");
        }
    } else {
        header("location:../login.php", true);
        die("");
    }
} catch (Exception $e) {
    echo "Error in session handling: " . $e->getMessage();
}

try {
    $user = 'kidzrcle_rwda';
    $pass = 'kidzrcle_rwda';
    $database = new PDO("mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8;", $user, $pass);

    $con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
    $con->set_charset("utf8");
    if ($con->connect_error) {
        throw new Exception("Connection failed: " . $con->connect_error);
    }

    $datenow = date('Y-m-d');
    $selected_user = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

    // جلب جميع المستخدمين
    $users_query = "SELECT u.id_user, u.user_name, COUNT(s.id) as student_count
                    FROM users_tb u
                    LEFT JOIN stud_tb s ON u.id_user = s.userID
                    WHERE u.role = 'User'
                    GROUP BY u.id_user, u.user_name
                    ORDER BY u.user_name";
    $users_result = mysqli_query($con, $users_query);

    if ($selected_user > 0) {
        // إحصائيات مستخدم محدد
        $user_condition = "AND s.userID = $selected_user";
        $pay_condition = "AND s.userID = $selected_user";
    } else {
        // إحصائيات عامة
        $user_condition = "";
        $pay_condition = "";
    }

    // الإحصائيات الأساسية
    $total_students = mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s WHERE 1=1 $user_condition");
    $total_students_count = mysqli_fetch_assoc($total_students)['count'];

    $active_students = mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s, stud_pay sp WHERE s.id = sp.id_stud AND DATE(sp.date_exp) > '$datenow' $user_condition");
    $active_students_count = mysqli_fetch_assoc($active_students)['count'];

    $expired_students = mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s, stud_pay sp WHERE s.id = sp.id_stud AND DATE(sp.date_exp) <= '$datenow' $user_condition");
    $expired_students_count = mysqli_fetch_assoc($expired_students)['count'];

    // إحصائيات الأصناف
    $nursery_count = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s WHERE s.catg='حضانة' $user_condition"))['count'];
    $kindergarten_count = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s WHERE s.catg='روضة' $user_condition"))['count'];
    $preparatory_count = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s WHERE s.catg='تمهيدي' $user_condition"))['count'];
    $preschool_count = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s WHERE s.catg='تحضيري' $user_condition"))['count'];

    // إحصائيات المدفوعات
    if ($selected_user > 0) {
        $total_payments = mysqli_query($con, "SELECT SUM(sp.cash_stud) as total FROM stud_pay sp, stud_tb s WHERE sp.id_stud = s.id AND s.userID = $selected_user");
        $total_payments_amount = mysqli_fetch_assoc($total_payments)['total'] ?? 0;


    } else {
        $total_payments = mysqli_query($con, "SELECT SUM(cash_stud) as total FROM stud_pay");
        $total_payments_amount = mysqli_fetch_assoc($total_payments)['total'] ?? 0;


    }



    // إحصائيات المستخدم المحدد
    if ($selected_user > 0) {
        // إجمالي المصروفات للمستخدم
        $user_expenses = mysqli_query($con, "SELECT SUM(depit_cash) as total FROM depit_tb WHERE userID = $selected_user");
        $user_expenses_amount = mysqli_fetch_assoc($user_expenses)['total'] ?? 0;

        // عدد المصروفات للمستخدم
        $user_expenses_count = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM depit_tb WHERE userID = $selected_user"))['count'];



        // إحصائيات الطلاب للمستخدم
        $user_students = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb WHERE userID = $selected_user"))['count'];

        // الطلاب قريبي الانتهاء (خلال 3 أيام)
        $date_3_days = date('Y-m-d', strtotime($datenow. ' + 3 days'));
        $expiring_students = mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb s, stud_pay sp WHERE s.id = sp.id_stud AND s.userID = $selected_user AND DATE(sp.date_exp) BETWEEN '$datenow' AND '$date_3_days'");
        $expiring_students_count = mysqli_fetch_assoc($expiring_students)['count'];

        // الطلاب الجدد (المسجلين لأول مرة خلال آخر 30 يوم)
        $date_30_days_ago = date('Y-m-d', strtotime($datenow. ' - 30 days'));
        $new_students = mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb WHERE userID = $selected_user AND datein >= '$date_30_days_ago'");
        $new_students_count = mysqli_fetch_assoc($new_students)['count'];

        // إحصائيات الحضور للطلاب التابعين للمستخدم (التحقق من وجود جدول الحضور)
        $attend_table_check = mysqli_query($con, "SHOW TABLES LIKE 'attend_tb'");
        if (mysqli_num_rows($attend_table_check) > 0) {
            $user_attendance = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(*) as count FROM attend_tb WHERE student_id IN (SELECT id FROM stud_tb WHERE userID = $selected_user)"))['count'];
        } else {
            $user_attendance = 0;
        }
    }

} catch (Exception $e) {
    echo "Error in query execution: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 الإحصائيات - أكاديمية الأطفال</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .statistics-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .page-title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 30px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .stat-card[style*="cursor: pointer"] {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card[style*="cursor: pointer"]:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.4);
        }

        .stat-card[style*="cursor: pointer"]:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .stat-card[style*="cursor: pointer"]:hover:before {
            left: 100%;
        }

        .stat-card[title]:after {
            content: '👆';
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.2rem;
            opacity: 0.7;
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: white;
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
        }
        
        .stat-card:nth-child(1) { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-card:nth-child(2) { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .stat-card:nth-child(3) { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-card:nth-child(4) { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-card:nth-child(5) { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stat-card:nth-child(6) { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .stat-card:nth-child(7) { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .stat-card:nth-child(8) { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .stat-card:nth-child(9) { background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%); }
        .stat-card:nth-child(10) { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .stat-card:nth-child(11) { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-card:nth-child(12) { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }

        /* قائمة اختيار المستخدم */
        .user-selector {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .selector-form {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .selector-label {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .user-select {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 12px 20px;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            min-width: 300px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-select:hover {
            background: white;
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .user-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        /* معلومات المستخدم المحدد */
        .selected-user-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .selected-user-info h2 {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .user-highlight {
            color: #38ef7d;
            text-shadow: 0 0 20px rgba(56, 239, 125, 0.5);
        }

        /* الجدول التفصيلي */
        .detailed-table {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .table-container {
            overflow-x: auto;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .students-table th {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        }

        .students-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .students-table tr.active {
            background: rgba(56, 239, 125, 0.1);
        }

        .students-table tr.warning {
            background: rgba(255, 193, 7, 0.1);
        }

        .students-table tr.expired {
            background: rgba(245, 87, 108, 0.1);
        }

        .students-table tbody tr {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .students-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .student-name {
            font-weight: 600;
            color: white !important;
        }

        .category {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 5px 10px;
            font-weight: 600;
        }

        .amount {
            font-weight: 600;
            color: #38ef7d;
        }

        .status-active {
            background: rgba(56, 239, 125, 0.2);
            color: #38ef7d;
            padding: 5px 10px;
            border-radius: 10px;
            font-weight: 600;
        }

        .status-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            padding: 5px 10px;
            border-radius: 10px;
            font-weight: 600;
        }

        .status-expired {
            background: rgba(245, 87, 108, 0.2);
            color: #f5576c;
            padding: 5px 10px;
            border-radius: 10px;
            font-weight: 600;
        }

        .status-unknown {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            padding: 5px 10px;
            border-radius: 10px;
            font-weight: 600;
        }

        /* أنماط زر الطباعة */
        .print-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            margin: 20px 0;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            background: linear-gradient(135deg, #218838, #1e7e34);
        }

        .print-button i {
            font-size: 1.1rem;
        }

        /* أنماط الطباعة */
        @media print {
            body * {
                visibility: hidden;
            }

            .printable-area,
            .printable-area * {
                visibility: visible;
            }

            .printable-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }

            .print-button,
            .user-selector,
            .no-print {
                display: none !important;
            }

            .page-title {
                color: #000 !important;
                font-size: 24px !important;
                margin-bottom: 20px;
            }

            .selected-user-info h2 {
                color: #000 !important;
                font-size: 20px !important;
                margin-bottom: 15px;
            }

            .stats-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 15px !important;
                margin-bottom: 30px;
            }

            .stat-card {
                border: 2px solid #ddd !important;
                background: #f8f9fa !important;
                padding: 15px !important;
                border-radius: 8px !important;
                text-align: center;
            }

            .stat-number {
                color: #000 !important;
                font-size: 24px !important;
                font-weight: bold;
            }

            .stat-label {
                color: #333 !important;
                font-size: 14px !important;
            }

            .detailed-table {
                page-break-inside: avoid;
                margin-top: 30px;
            }

            .table-title {
                color: #000 !important;
                font-size: 18px !important;
                margin-bottom: 15px;
            }

            .students-table,
            .students-table th,
            .students-table td {
                border: 1px solid #000 !important;
                color: #000 !important;
            }

            .students-table th {
                background: #f0f0f0 !important;
                font-weight: bold;
            }

            .amount {
                font-weight: bold;
            }
        }

        /* أنماط النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ffcccc;
        }

        .modal-body {
            padding: 30px;
            background: white;
            color: #333;
        }

        .student-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .info-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .info-value {
            color: #34495e;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-renew {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .students-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .student-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 5px solid #007bff;
            border: 1px solid #e0e0e0;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .student-card.expiring {
            border-left-color: #ffc107;
            background: #fff9e6;
        }

        .student-card.new {
            border-left-color: #28a745;
            background: #f0f9f0;
        }

        .student-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #000 !important;
            margin-bottom: 10px;
            text-shadow: none;
            background: rgba(52, 73, 94, 0.05);
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #bdc3c7;
        }

        .student-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
            color: #34495e;
            font-weight: 500;
        }

        .student-details div {
            background: rgba(236, 240, 241, 0.7);
            padding: 5px 8px;
            border-radius: 5px;
            border: 1px solid #d5dbdb;
        }

        .student-details strong {
            color: #2c3e50;
        }

        .student-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 10px;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-new {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-unknown {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .student-status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .status-new {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .status-expiring {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin: 30px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* أنماط الفلترة */
        .filter-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid #ddd;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .filter-input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            color: #333;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #007bff;
            background: #f8f9fa;
        }

        .filter-input::placeholder {
            color: #666;
        }

        .filter-select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            color: #333;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .filter-select option {
            background: white;
            color: #333;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-filter {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-reset {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* أنماط نافذة الإحصائيات */
        .statistics-modal .modal-content {
            max-width: 1200px;
        }

        .statistics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .statistics-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            text-shadow: none;
        }

        .print-statistic-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .print-statistic-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .statistics-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            text-shadow: none;
        }

        .summary-label {
            color: #34495e;
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* أنماط الجداول في النوافذ المنبثقة */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
            background: white !important;
        }

        .students-table th {
            background: #f8f9fa !important;
            color: #2c3e50 !important;
            font-weight: 600 !important;
            padding: 12px !important;
            text-align: center !important;
            border-bottom: 2px solid #dee2e6 !important;
        }

        .students-table td {
            padding: 10px !important;
            text-align: center !important;
            border-bottom: 1px solid #dee2e6 !important;
            color: #34495e !important;
            background: white !important;
        }

        .students-table tr:hover {
            background: #f8f9fa !important;
        }

        .students-table .amount {
            color: #27ae60 !important;
            font-weight: bold !important;
        }
        
        @media (max-width: 768px) {
            .statistics-container {
                margin: 10px;
                padding: 10px;
            }

            .page-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .selector-form {
                flex-direction: column;
                gap: 10px;
            }

            .user-select {
                min-width: 100%;
            }

            .table-container {
                font-size: 0.8rem;
            }

            .students-table th,
            .students-table td {
                padding: 8px 5px;
            }

            .detailed-table {
                padding: 15px;
            }
        }
    </style>

    <?php include "addon/topbar.php"; ?>
</head>
<body>
    <div class="statistics-container">
        <h1 class="page-title">
            <i class="fas fa-chart-bar"></i>
            الإحصائيات الشاملة
        </h1>

        <!-- قائمة اختيار المستخدم -->
        <div class="user-selector no-print">
            <form method="GET" action="" class="selector-form">
                <label for="user_id" class="selector-label">
                    <i class="fas fa-user"></i> اختر المستخدم:
                </label>
                <select name="user_id" id="user_id" class="user-select" onchange="this.form.submit()">
                    <option value="0" <?php echo $selected_user == 0 ? 'selected' : ''; ?>>
                        📊 الإحصائيات العامة (جميع المستخدمين)
                    </option>
                    <?php while($user_row = mysqli_fetch_assoc($users_result)): ?>
                        <option value="<?php echo $user_row['id_user']; ?>"
                                <?php echo $selected_user == $user_row['id_user'] ? 'selected' : ''; ?>>
                            👤 <?php echo $user_row['user_name']; ?>
                            (<?php echo $user_row['student_count']; ?> طلاب)
                        </option>
                    <?php endwhile; ?>
                </select>
            </form>
        </div>

        <!-- عرض المستخدم المحدد -->
        <?php if ($selected_user > 0): ?>
            <?php
            mysqli_data_seek($users_result, 0);
            $current_user_name = "";
            while($user_row = mysqli_fetch_assoc($users_result)) {
                if ($user_row['id_user'] == $selected_user) {
                    $current_user_name = $user_row['user_name'];
                    break;
                }
            }
            ?>
            <div class="selected-user-info">
                <h2>
                    <i class="fas fa-user-circle"></i>
                    إحصائيات المستخدم: <span class="user-highlight"><?php echo $current_user_name; ?></span>
                </h2>
                <!-- زر الطباعة للمستخدم المحدد -->
                <button class="print-button" onclick="printUserStatistics()">
                    <i class="fas fa-print"></i>
                    طباعة إحصائيات المستخدم
                </button>
            </div>
        <?php else: ?>
            <div class="selected-user-info">
                <h2>
                    <i class="fas fa-globe"></i>
                    الإحصائيات العامة لجميع المستخدمين
                </h2>
            </div>
        <?php endif; ?>

        <div class="stats-grid printable-area">
            <div class="stat-card" onclick="showStatisticDetails('total_students', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل جميع الطلاب">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $total_students_count; ?></div>
                <div class="stat-label">إجمالي الطلاب</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('active_students', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل الطلاب الفعالين">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo $active_students_count; ?></div>
                <div class="stat-label">الطلاب الفعالين</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('expired_students', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل منتهي الاشتراك">
                <div class="stat-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number"><?php echo $expired_students_count; ?></div>
                <div class="stat-label">منتهي الاشتراك</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('nursery', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل صنف الحضانة">
                <div class="stat-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <div class="stat-number"><?php echo $nursery_count; ?></div>
                <div class="stat-label">صنف الحضانة</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('kindergarten', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل صنف الروضة">
                <div class="stat-icon">
                    <i class="fas fa-child"></i>
                </div>
                <div class="stat-number"><?php echo $kindergarten_count; ?></div>
                <div class="stat-label">صنف الروضة</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('preparatory', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل صنف التمهيدي">
                <div class="stat-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stat-number"><?php echo $preparatory_count; ?></div>
                <div class="stat-label">صنف التمهيدي</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('preschool', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل صنف التحضيري">
                <div class="stat-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-number"><?php echo $preschool_count; ?></div>
                <div class="stat-label">صنف التحضيري</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('total_payments', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل إجمالي المدفوعات">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_payments_amount); ?></div>
                <div class="stat-label">إجمالي المدفوعات</div>
            </div>



            <?php if ($selected_user == 0): ?>





            <?php endif; ?>

            <!-- إحصائيات المصروفات للمستخدم المحدد -->
            <?php if ($selected_user > 0): ?>


            <div class="stat-card" onclick="showStatisticDetails('expenses_count', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل عدد المصروفات">
                <div class="stat-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="stat-number"><?php echo $user_expenses_count ?? 0; ?></div>
                <div class="stat-label">عدد المصروفات</div>
            </div>



            <div class="stat-card" onclick="showStatisticDetails('expiring_students', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل الطلاب قريبي الانتهاء">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?php echo $expiring_students_count ?? 0; ?></div>
                <div class="stat-label">طلاب قريبي الانتهاء</div>
            </div>

            <div class="stat-card" onclick="showStatisticDetails('new_students', <?php echo $selected_user; ?>)" style="cursor: pointer;" title="انقر لعرض تفاصيل الطلاب الجدد">
                <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stat-number"><?php echo $new_students_count ?? 0; ?></div>
                <div class="stat-label">الطلاب الجدد</div>
            </div>
            <?php endif; ?>
        </div>

        <!-- جدول تفصيلي للطلاب -->
        <?php if ($selected_user > 0): ?>
        <div class="detailed-table">
            <h3 class="table-title">
                <i class="fas fa-table"></i>
                تفاصيل طلاب المستخدم: <?php echo $current_user_name; ?>
            </h3>

            <?php
            $students_query = "SELECT s.*, sp.cash_stud, sp.date_exp,
                              DATEDIFF(sp.date_exp, '$datenow') as days_remaining
                              FROM stud_tb s
                              LEFT JOIN stud_pay sp ON s.id = sp.id_stud
                              WHERE s.userID = $selected_user
                              ORDER BY s.name";
            $students_result = mysqli_query($con, $students_query);
            ?>

            <div class="table-container">
                <table class="students-table">
                    <thead>
                        <tr>
                            <th>اسم الطالب</th>
                            <th>العمر</th>
                            <th>الجنس</th>
                            <th>الصنف</th>
                            <th>تاريخ التسجيل</th>
                            <th>المبلغ المدفوع</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($student = mysqli_fetch_assoc($students_result)): ?>
                        <tr class="<?php echo $student['days_remaining'] <= 0 ? 'expired' : ($student['days_remaining'] <= 10 ? 'warning' : 'active'); ?>"
                            onclick="showStudentDetails(<?php echo $student['id']; ?>)"
                            style="cursor: pointer;"
                            title="انقر لعرض تفاصيل الطالب">
                            <td class="student-name"><?php echo $student['name']; ?></td>
                            <td><?php echo $student['age']; ?></td>
                            <td><?php echo $student['sex']; ?></td>
                            <td class="category"><?php echo $student['catg']; ?></td>
                            <td><?php echo $student['datein']; ?></td>
                            <td class="amount"><?php echo number_format($student['cash_stud'] ?? 0); ?> د.ع</td>
                            <td><?php echo $student['date_exp'] ?? 'غير محدد'; ?></td>
                            <td class="status">
                                <?php
                                if ($student['days_remaining'] === null) {
                                    echo '<span class="status-unknown">غير محدد</span>';
                                } elseif ($student['days_remaining'] <= 0) {
                                    echo '<span class="status-expired">منتهي</span>';
                                } elseif ($student['days_remaining'] <= 10) {
                                    echo '<span class="status-warning">ينتهي قريباً (' . $student['days_remaining'] . ' أيام)</span>';
                                } else {
                                    echo '<span class="status-active">فعال (' . $student['days_remaining'] . ' يوم)</span>';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- جدول تفصيلي للمصروفات للمستخدم المحدد -->
        <div class="detailed-table">
            <h3 class="table-title">
                <i class="fas fa-money-bill-wave"></i>
                تفاصيل مصروفات المستخدم: <?php echo $current_user_name; ?>
            </h3>

            <?php
            $expenses_query = "SELECT depit_date, depit_cash, depit_note
                              FROM depit_tb
                              WHERE userID = $selected_user
                              ORDER BY depit_date DESC
                              LIMIT 20";
            $expenses_result = mysqli_query($con, $expenses_query);
            ?>

            <div class="table-container">
                <table class="students-table">
                    <thead>
                        <tr>
                            <th>تاريخ المصروف</th>
                            <th>المبلغ</th>
                            <th>وصف المصروف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($expenses_result) > 0): ?>
                            <?php while($expense = mysqli_fetch_assoc($expenses_result)): ?>
                            <tr>
                                <td><?php echo $expense['depit_date']; ?></td>
                                <td class="amount"><?php echo number_format($expense['depit_cash']); ?> د.ع</td>
                                <td><?php echo $expense['depit_note']; ?></td>
                            </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="3" style="text-align: center; color: #666;">
                                    لا توجد مصروفات مسجلة لهذا المستخدم
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- النوافذ المنبثقة -->
        <!-- نافذة تفاصيل الطالب -->
        <div id="studentModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="studentModalTitle">تفاصيل الطالب</h2>
                    <span class="close" onclick="closeModal('studentModal')">&times;</span>
                </div>
                <div class="modal-body" id="studentModalBody">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <!-- نافذة الطلاب قريبي الانتهاء -->
        <div id="expiringStudentsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>الطلاب قريبي الانتهاء</h2>
                    <span class="close" onclick="closeModal('expiringStudentsModal')">&times;</span>
                </div>
                <div class="modal-body" id="expiringStudentsBody">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <!-- نافذة الطلاب الجدد -->
        <div id="newStudentsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>الطلاب الجدد (آخر 30 يوم)</h2>
                    <span class="close" onclick="closeModal('newStudentsModal')">&times;</span>
                </div>
                <div class="modal-body" id="newStudentsBody">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <!-- نافذة الإحصائيات التفصيلية -->
        <div id="statisticsModal" class="modal statistics-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="statisticsModalTitle">تفاصيل الإحصائية</h2>
                    <span class="close" onclick="closeModal('statisticsModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="statistics-header">
                        <div class="statistics-title" id="statisticsTitle"></div>
                        <button class="print-statistic-btn" onclick="printStatisticDetails()">
                            <i class="fas fa-print"></i>
                            طباعة التفاصيل
                        </button>
                    </div>

                    <div class="statistics-summary" id="statisticsSummary">
                        <!-- ملخص الإحصائية -->
                    </div>

                    <!-- فلترة البيانات -->
                    <div class="filter-container" id="filterContainer">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">البحث بالاسم</label>
                                <input type="text" class="filter-input" id="nameFilter" placeholder="ابحث بالاسم...">
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">الصنف</label>
                                <select class="filter-select" id="categoryFilter">
                                    <option value="">جميع الأصناف</option>
                                    <option value="حضانة">حضانة</option>
                                    <option value="روضة">روضة</option>
                                    <option value="تمهيدي">تمهيدي</option>
                                    <option value="تحضيري">تحضيري</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">الجنس</label>
                                <select class="filter-select" id="genderFilter">
                                    <option value="">الكل</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="انثى">أنثى</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">العمر</label>
                                <select class="filter-select" id="ageFilter">
                                    <option value="">جميع الأعمار</option>
                                    <option value="1">سنة واحدة</option>
                                    <option value="2">سنتان</option>
                                    <option value="3">3 سنوات</option>
                                    <option value="4">4 سنوات</option>
                                    <option value="5">5 سنوات</option>
                                    <option value="6+">6 سنوات فأكثر</option>
                                </select>
                            </div>
                            <div class="filter-buttons">
                                <button class="filter-btn btn-filter" onclick="applyFilters()">
                                    <i class="fas fa-filter"></i> تطبيق
                                </button>
                                <button class="filter-btn btn-reset" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="statisticsContent">
                        <!-- محتوى الإحصائية -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.stat-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;

            // تأثير العد التصاعدي للأرقام
            const numberElement = card.querySelector('.stat-number');
            const targetNumber = parseInt(numberElement.textContent.replace(/,/g, ''));

            if (!isNaN(targetNumber)) {
                let currentNumber = 0;
                const increment = targetNumber / 50;
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= targetNumber) {
                        currentNumber = targetNumber;
                        clearInterval(timer);
                    }
                    numberElement.textContent = Math.floor(currentNumber).toLocaleString();
                }, 30);
            }
        });

        // متغيرات عامة للفلترة
        let currentStatisticData = [];
        let currentStatisticType = '';
        let currentUserId = 0;

        // وظائف النوافذ المنبثقة
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // وظيفة عرض تفاصيل الإحصائية
        function showStatisticDetails(type, userId) {
            currentStatisticType = type;
            currentUserId = userId;

            fetch(`get_statistic_details.php?type=${type}&user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentStatisticData = data.data;
                        displayStatisticModal(data);
                    } else {
                        alert('خطأ في جلب البيانات: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }

        // عرض نافذة الإحصائية
        function displayStatisticModal(data) {
            const modal = document.getElementById('statisticsModal');
            const title = document.getElementById('statisticsModalTitle');
            const statisticsTitle = document.getElementById('statisticsTitle');
            const summary = document.getElementById('statisticsSummary');
            const content = document.getElementById('statisticsContent');

            title.textContent = data.title;
            statisticsTitle.textContent = data.title;

            // عرض الملخص
            summary.innerHTML = `
                <div class="summary-item">
                    <div class="summary-number">${data.summary.total}</div>
                    <div class="summary-label">إجمالي العدد</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${data.summary.amount || 0}</div>
                    <div class="summary-label">إجمالي المبلغ</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${data.summary.average || 0}</div>
                    <div class="summary-label">المتوسط</div>
                </div>
            `;

            // إخفاء/إظهار الفلترة حسب نوع البيانات
            const filterContainer = document.getElementById('filterContainer');
            if (data.type === 'students') {
                filterContainer.style.display = 'block';
            } else {
                filterContainer.style.display = 'none';
            }

            displayStatisticContent(data.data, data.type);
            modal.style.display = 'block';
        }

        // عرض محتوى الإحصائية
        function displayStatisticContent(data, type) {
            const content = document.getElementById('statisticsContent');

            if (type === 'students') {
                content.innerHTML = `
                    <div class="table-container">
                        <table class="students-table" style="background: white; color: #333;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="color: #2c3e50; font-weight: 600;">رقم الوصل</th>
                                    <th style="color: #2c3e50; font-weight: 600;">اسم الطالب</th>
                                    <th style="color: #2c3e50; font-weight: 600;">العمر</th>
                                    <th style="color: #2c3e50; font-weight: 600;">الجنس</th>
                                    <th style="color: #2c3e50; font-weight: 600;">الصنف</th>
                                    <th style="color: #2c3e50; font-weight: 600;">تاريخ التسجيل</th>
                                    <th style="color: #2c3e50; font-weight: 600;">المبلغ المدفوع</th>
                                    <th style="color: #2c3e50; font-weight: 600;">تاريخ الانتهاء</th>
                                    <th style="color: #2c3e50; font-weight: 600;">الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => {
                                    // تحديد حالة الاشتراك
                                    let subscriptionStatus = '';
                                    let statusClass = '';
                                    if (item.days_remaining !== undefined) {
                                        if (item.days_remaining <= 0) {
                                            subscriptionStatus = 'منتهي';
                                            statusClass = 'status-expired';
                                        } else if (item.days_remaining <= 3) {
                                            subscriptionStatus = 'قريب الانتهاء';
                                            statusClass = 'status-warning';
                                        } else {
                                            subscriptionStatus = 'فعال';
                                            statusClass = 'status-active';
                                        }
                                    } else {
                                        subscriptionStatus = 'غير محدد';
                                        statusClass = 'status-unknown';
                                    }

                                    return `
                                        <tr onclick="showStudentDetails(${item.id})" style="cursor: pointer; color: #34495e;">
                                            <td style="color: #007bff; font-weight: 600;">${item.id_pay || 'غير محدد'}</td>
                                            <td style="color: #000 !important; font-weight: 600;">${item.name}</td>
                                            <td style="color: #34495e;">${item.age}</td>
                                            <td style="color: #34495e;">${item.sex}</td>
                                            <td style="color: #34495e;">${item.catg}</td>
                                            <td style="color: #34495e;">${item.datein || 'غير محدد'}</td>
                                            <td class="amount" style="color: #27ae60; font-weight: bold;">${item.cash_stud ? new Intl.NumberFormat().format(item.cash_stud) + ' د.ع' : 'غير محدد'}</td>
                                            <td style="color: #34495e;">${item.date_exp || 'غير محدد'}</td>
                                            <td><span class="student-status ${statusClass}">${subscriptionStatus}</span></td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            } else if (type === 'expenses') {
                content.innerHTML = `
                    <div class="table-container">
                        <table class="students-table" style="background: white; color: #333;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="color: #2c3e50; font-weight: 600;">التاريخ</th>
                                    <th style="color: #2c3e50; font-weight: 600;">المبلغ</th>
                                    <th style="color: #2c3e50; font-weight: 600;">الوصف</th>
                                    <th style="color: #2c3e50; font-weight: 600;">المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => `
                                    <tr style="color: #34495e;">
                                        <td style="color: #34495e;">${item.depit_date}</td>
                                        <td class="amount" style="color: #27ae60; font-weight: bold;">${new Intl.NumberFormat().format(item.depit_cash)} د.ع</td>
                                        <td style="color: #34495e;">${item.depit_note}</td>
                                        <td style="color: #34495e;">${item.user_name || 'غير محدد'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            } else if (type === 'payments') {
                content.innerHTML = `
                    <div class="table-container">
                        <table class="students-table" style="background: white; color: #333;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="color: #2c3e50; font-weight: 600;">اسم الطالب</th>
                                    <th style="color: #2c3e50; font-weight: 600;">المبلغ</th>
                                    <th style="color: #2c3e50; font-weight: 600;">تاريخ الانتهاء</th>
                                    <th style="color: #2c3e50; font-weight: 600;">المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => `
                                    <tr onclick="showStudentDetails(${item.id_stud})" style="cursor: pointer; color: #34495e;">
                                        <td style="color: #000 !important; font-weight: 600;">${item.student_name}</td>
                                        <td class="amount" style="color: #27ae60; font-weight: bold;">${new Intl.NumberFormat().format(item.cash_stud)} د.ع</td>
                                        <td style="color: #34495e;">${item.date_exp}</td>
                                        <td style="color: #34495e;">${item.user_name}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div class="table-container">
                        <table class="students-table" style="background: white; color: #333;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    ${Object.keys(data[0] || {}).map(key => `<th style="color: #2c3e50; font-weight: 600;">${key}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.map(item => `
                                    <tr style="color: #34495e;">
                                        ${Object.values(item).map(value => `<td style="color: #34495e;">${value}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }
        }

        function showStudentDetails(studentId) {
            fetch(`get_student_details.php?id=${studentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const student = data.student;
                        const modalBody = document.getElementById('studentModalBody');
                        modalBody.innerHTML = `
                            <div class="student-info">
                                <div class="info-card">
                                    <div class="info-label">اسم الطالب</div>
                                    <div class="info-value">${student.name}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">العمر</div>
                                    <div class="info-value">${student.age} سنة</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">الجنس</div>
                                    <div class="info-value">${student.sex}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">الصنف</div>
                                    <div class="info-value">${student.catg}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">تاريخ التسجيل</div>
                                    <div class="info-value">${student.datein}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">اسم ولي الأمر</div>
                                    <div class="info-value">${student.p_name || 'غير محدد'}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">رقم الهاتف</div>
                                    <div class="info-value">${student.p_phone}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">الموقع</div>
                                    <div class="info-value">${student.loc}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">المبلغ المدفوع</div>
                                    <div class="info-value">${student.cash_stud ? new Intl.NumberFormat().format(student.cash_stud) + ' د.ع' : 'غير محدد'}</div>
                                </div>
                                <div class="info-card">
                                    <div class="info-label">تاريخ انتهاء الاشتراك</div>
                                    <div class="info-value">${student.date_exp || 'غير محدد'}</div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <a href="editstud.php?id=${student.id}" class="action-btn btn-edit">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="renew.php?renewId=${student.id}" class="action-btn btn-renew">
                                    <i class="fas fa-sync-alt"></i> تجديد
                                </a>
                                <button onclick="deleteStudent(${student.id})" class="action-btn btn-delete">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        `;
                        document.getElementById('studentModalTitle').textContent = `تفاصيل الطالب: ${student.name}`;
                        document.getElementById('studentModal').style.display = 'block';
                    } else {
                        alert('خطأ في جلب بيانات الطالب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }

        function showExpiringStudents(userId) {
            fetch(`get_expiring_students.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const modalBody = document.getElementById('expiringStudentsBody');
                        if (data.students.length === 0) {
                            modalBody.innerHTML = '<p style="text-align: center; color: #666;">لا يوجد طلاب قريبي الانتهاء</p>';
                        } else {
                            modalBody.innerHTML = `
                                <div class="students-grid">
                                    ${data.students.map(student => `
                                        <div class="student-card expiring" onclick="showStudentDetails(${student.id})">
                                            <div class="student-name">${student.name}</div>
                                            <div class="student-details">
                                                <div><strong>العمر:</strong> ${student.age}</div>
                                                <div><strong>الصنف:</strong> ${student.catg}</div>
                                                <div><strong>الهاتف:</strong> ${student.p_phone}</div>
                                                <div><strong>الانتهاء:</strong> ${student.date_exp}</div>
                                            </div>
                                            <div class="student-status status-expiring">
                                                ينتهي خلال ${student.days_remaining} أيام
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            `;
                        }
                        document.getElementById('expiringStudentsModal').style.display = 'block';
                    } else {
                        alert('خطأ في جلب بيانات الطلاب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }

        function showNewStudents(userId) {
            fetch(`get_new_students.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const modalBody = document.getElementById('newStudentsBody');
                        if (data.students.length === 0) {
                            modalBody.innerHTML = '<p style="text-align: center; color: #666;">لا يوجد طلاب جدد خلال آخر 30 يوم</p>';
                        } else {
                            modalBody.innerHTML = `
                                <div class="alert alert-info" style="margin-bottom: 20px; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; color: #0c5460;">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>ملاحظة:</strong> هؤلاء الطلاب تم تسجيلهم لأول مرة خلال آخر 30 يوم. بعد 30 يوم سينتقلون تلقائياً لأقسام أخرى حسب حالة اشتراكهم.
                                </div>
                                <div class="students-grid">
                                    ${data.students.map(student => `
                                        <div class="student-card new" onclick="showStudentDetails(${student.id})">
                                            <div class="student-name">${student.name}</div>
                                            <div class="student-details">
                                                <div><strong>العمر:</strong> ${student.age}</div>
                                                <div><strong>الصنف:</strong> ${student.catg}</div>
                                                <div><strong>الهاتف:</strong> ${student.p_phone}</div>
                                                <div><strong>التسجيل:</strong> ${student.datein}</div>
                                                <div><strong>حالة الاشتراك:</strong>
                                                    <span style="color: ${student.subscription_status === 'مفعل' ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                                        ${student.subscription_status}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="student-status status-new">
                                                طالب جديد (${student.days_since_registration} أيام) - ${student.subscription_status}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            `;
                        }
                        document.getElementById('newStudentsModal').style.display = 'block';
                    } else {
                        alert('خطأ في جلب بيانات الطلاب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }

        function deleteStudent(studentId) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
                fetch('delete_student.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: studentId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف الطالب بنجاح');
                        closeModal('studentModal');
                        location.reload();
                    } else {
                        alert('خطأ في حذف الطالب: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
            }
        }

        // وظائف الفلترة
        function applyFilters() {
            const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const genderFilter = document.getElementById('genderFilter').value;
            const ageFilter = document.getElementById('ageFilter').value;

            const filteredData = currentStatisticData.filter(item => {
                const nameMatch = !nameFilter || item.name.toLowerCase().includes(nameFilter);
                const categoryMatch = !categoryFilter || item.catg === categoryFilter;
                const genderMatch = !genderFilter || item.sex === genderFilter;

                let ageMatch = true;
                if (ageFilter) {
                    if (ageFilter === '6+') {
                        ageMatch = parseInt(item.age) >= 6;
                    } else {
                        ageMatch = item.age === ageFilter;
                    }
                }

                return nameMatch && categoryMatch && genderMatch && ageMatch;
            });

            displayStatisticContent(filteredData, 'students');

            // تحديث الملخص
            const summary = document.getElementById('statisticsSummary');
            const totalAmount = filteredData.reduce((sum, item) => sum + (parseFloat(item.cash_stud) || 0), 0);
            const average = filteredData.length > 0 ? totalAmount / filteredData.length : 0;

            summary.innerHTML = `
                <div class="summary-item">
                    <div class="summary-number">${filteredData.length}</div>
                    <div class="summary-label">عدد النتائج</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${new Intl.NumberFormat().format(totalAmount)}</div>
                    <div class="summary-label">إجمالي المبلغ</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${new Intl.NumberFormat().format(average)}</div>
                    <div class="summary-label">متوسط المبلغ</div>
                </div>
            `;
        }

        function resetFilters() {
            document.getElementById('nameFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('genderFilter').value = '';
            document.getElementById('ageFilter').value = '';

            displayStatisticContent(currentStatisticData, 'students');

            // إعادة تعيين الملخص
            const totalAmount = currentStatisticData.reduce((sum, item) => sum + (parseFloat(item.cash_stud) || 0), 0);
            const average = currentStatisticData.length > 0 ? totalAmount / currentStatisticData.length : 0;

            const summary = document.getElementById('statisticsSummary');
            summary.innerHTML = `
                <div class="summary-item">
                    <div class="summary-number">${currentStatisticData.length}</div>
                    <div class="summary-label">إجمالي العدد</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${new Intl.NumberFormat().format(totalAmount)}</div>
                    <div class="summary-label">إجمالي المبلغ</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">${new Intl.NumberFormat().format(average)}</div>
                    <div class="summary-label">المتوسط</div>
                </div>
            `;
        }

        // وظيفة طباعة تفاصيل الإحصائية
        function printStatisticDetails() {
            const printWindow = window.open('', '_blank');
            const title = document.getElementById('statisticsTitle').textContent;
            const summary = document.getElementById('statisticsSummary').innerHTML;
            const content = document.getElementById('statisticsContent').innerHTML;

            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>${title} - أكاديمية الأطفال</title>
                    <style>
                        body {
                            font-family: 'Cairo', Arial, sans-serif;
                            margin: 20px;
                            color: #000;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 3px solid #007bff;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #007bff;
                            margin: 0;
                            font-size: 28px;
                        }
                        .header h2 {
                            color: #333;
                            margin: 10px 0 0 0;
                            font-size: 20px;
                        }
                        .summary {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            gap: 20px;
                            margin-bottom: 30px;
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                        }
                        .summary-item {
                            text-align: center;
                        }
                        .summary-number {
                            font-size: 2rem;
                            font-weight: bold;
                            color: #007bff;
                        }
                        .summary-label {
                            color: #666;
                            font-size: 0.9rem;
                        }
                        .table-container {
                            margin-bottom: 20px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                            font-size: 12px;
                        }
                        th, td {
                            border: 1px solid #000;
                            padding: 8px;
                            text-align: center;
                            vertical-align: middle;
                        }
                        th {
                            background: #f0f0f0;
                            font-weight: bold;
                            color: #000;
                        }
                        td {
                            color: #000;
                        }
                        .amount {
                            color: #000;
                            font-weight: bold;
                        }
                        .student-status {
                            padding: 2px 6px;
                            border-radius: 10px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        .status-active {
                            background: #d4edda;
                            color: #155724;
                        }
                        .status-warning {
                            background: #fff3cd;
                            color: #856404;
                        }
                        .status-expired {
                            background: #f8d7da;
                            color: #721c24;
                        }
                        .status-unknown {
                            background: #e2e3e5;
                            color: #383d41;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }
                        th, td {
                            border: 1px solid #000;
                            padding: 8px;
                            text-align: center;
                        }
                        th {
                            background: #f0f0f0;
                            font-weight: bold;
                        }
                        .print-date {
                            text-align: left;
                            margin-top: 30px;
                            font-size: 12px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📊 أكاديمية الأطفال</h1>
                        <h2>${title}</h2>
                    </div>

                    <div class="summary">
                        ${summary}
                    </div>

                    <div class="content">
                        ${content}
                    </div>

                    <div class="print-date">
                        تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();

            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modals = ['studentModal', 'expiringStudentsModal', 'newStudentsModal', 'statisticsModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // وظيفة طباعة إحصائيات المستخدم
        function printUserStatistics() {
            window.print();
        }

        console.log('📊 صفحة الإحصائيات جاهزة!');
    </script>

    <!-- إضافة الإشعارات المحسنة -->
    <?php include 'notifications_fixed.php'; ?>
</body>
</html>
