<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اجمالي عدد المصروفات </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <div style="margin: 20px; text-align: center;">
        <h2>جميع المصروفات</h2>
        <button class="btn btn-primary text-light" onclick="printAllDepitTable()">طباعة</button>
    </div>
    <table class="table" id="Table" style="text-align:center;">
  <thead>
    <tr>
      <th scope="col">مستخدم الحضانة</th>
      <th scope="col"> تاريخ الصرف</th>
      <th scope="col">قيمة المصروف</th>
      <th scope="col">وصف  المصروف </th>  
    </tr>
  </thead>
  <tbody id="myTable">
    <?php
      
      $sql="SELECT * FROM depit_tb,users_tb WHERE depit_tb.userID=users_tb.id_user ";
      $result=mysqli_query($con,$sql);
      if($result){
       while($row=mysqli_fetch_assoc($result)) {
          $user_name=$row['user_name'];
          $depit_note=$row['depit_note'];
          $depit_date=$row['depit_date'];
          $depit_cash=number_format($row['depit_cash']);
          echo '<tr>
          <td> '.$user_name.' </td>
          <td>'.$depit_date.'</td>
          <td> IQD '.$depit_cash.'  </td>
          <td>'.$depit_note.'</td>

          
          </tr>
        ';
         }
      }
  

    ?>
   
  </tbody>
</table>
   </body>
<script>
  $(document).ready(function () {
    $("#Table").DataTable();
  });
</script>

<script>
function printAllDepitTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول
    var table = document.getElementById('Table').cloneNode(true);
    var tableContent = table.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير جميع المصروفات</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير جميع المصروفات</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tableContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    
    <!-- إضافة الإشعارات المحسنة -->
    <?php include 'notifications_fixed.php'; ?>
</body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>
</html>