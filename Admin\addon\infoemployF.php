<?php

session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
$id=$_GET['id'];
$sql="SELECT * FROM employ_tb,users_tb WHERE employ_tb.userID=users_tb.id_user  and employ_tb.userID=$id  ";
$result=mysqli_query($con,$sql);
if(mysqli_num_rows($result)>0){
 while($row=mysqli_fetch_assoc($result)) {
  
    $id=$row['id_employ'];
    $f_name=$row['f_name'];
    $b_date=$row['b_date'];
    $job=$row['job'];
    $date_start=$row['date_start'];
    $location=$row['location'];
    $salary=number_format($row['salary']);
    $user_name=$row['user_name'];
    ?><tr id="tr_<?php echo $id?>">
    <td><button type="button" class="btn btn-secondary mb-1" name="update"> <a href=edit_employ.php?UpDate=<?php echo $id ?> class="text-light">تعديل </a></button>
    <br> 
    <button type="button" class="btn btn-secondary mb-1" onclick="deletdata(<?php echo $id ?>)" >حذف </button></td>
    <td> <?php echo $user_name?></td>
    <td> IQD <?php echo $salary?></td>
    <td><?php echo $location?></td>
    <td><?php echo $date_start?></td>
    <td><?php echo $job?></td>
    <td> <?php echo $b_date?></td>
    <td><?php echo $f_name?></td>
    </tr>
    
   <?php
   }
}else{
    ?> 
    <td colspan="8" style="font-size: 25px;">لايوجد موظفين لهذا المستخدم</td>
    <?php

}


?>