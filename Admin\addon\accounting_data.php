<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo '<div class="alert alert-danger">غير مصرح بالوصول</div>';
    exit();
}

include "dbcon.php";

// التحقق من الاتصال بقاعدة البيانات
if (!$con) {
    echo '<div class="alert alert-danger">خطأ في الاتصال بقاعدة البيانات: ' . mysqli_connect_error() . '</div>';
    exit();
}

// الحصول على المعاملات
$user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$category = isset($_POST['category']) ? $_POST['category'] : '';
$date_from = isset($_POST['date_from']) ? $_POST['date_from'] : '';
$date_to = isset($_POST['date_to']) ? $_POST['date_to'] : '';
$search_term = isset($_POST['search_term']) ? $_POST['search_term'] : '';
$action = isset($_POST['action']) ? $_POST['action'] : 'get_data';

// تشخيص المعاملات
error_log("معاملات البحث: user_id=$user_id, category=$category, date_from=$date_from, date_to=$date_to, search_term=$search_term");

// بناء الاستعلام حسب النوع
$data = [];
$total_amount = 0;

try {
    if ($category == 'revenue' || $category == '') {
        // الإيرادات من مدفوعات الطلاب
        $revenue_query = "SELECT
            sp.id_pay as id,
            'إيرادات' as type,
            CONCAT('دفعة طالب: ', COALESCE(st.name, 'غير محدد')) as description,
            sp.cash_stud as amount,
            sp.datein as date,
            COALESCE(u.user_name, 'غير محدد') as user_name,
            'إيراد' as category
        FROM stud_pay sp
        LEFT JOIN stud_tb st ON sp.id_stud = st.id
        LEFT JOIN users_tb u ON st.userID = u.id_user
        WHERE sp.cash_stud > 0";

        // إضافة فلاتر
        if ($user_id > 0) {
            $revenue_query .= " AND st.userID = " . intval($user_id);
        }

        if ($date_from) {
            $revenue_query .= " AND sp.datein >= '" . mysqli_real_escape_string($con, $date_from) . "'";
        }

        if ($date_to) {
            $revenue_query .= " AND sp.datein <= '" . mysqli_real_escape_string($con, $date_to) . "'";
        }

        if ($search_term) {
            $escaped_term = mysqli_real_escape_string($con, $search_term);
            $revenue_query .= " AND (st.name LIKE '%$escaped_term%' OR sp.cash_stud LIKE '%$escaped_term%')";
        }

        $revenue_query .= " ORDER BY sp.datein DESC LIMIT 1000";

        $revenue_result = mysqli_query($con, $revenue_query);
        if ($revenue_result) {
            while ($row = mysqli_fetch_assoc($revenue_result)) {
                $data[] = $row;
                $total_amount += $row['amount'];
            }
        } else {
            error_log("خطأ في استعلام الإيرادات: " . mysqli_error($con));
        }
    }

    if ($category == 'expenses' || $category == '') {
        // المصروفات
        $expenses_query = "SELECT
            d.id as id,
            'مصروفات' as type,
            COALESCE(d.depit_note, 'مصروف غير محدد') as description,
            d.depit_cash as amount,
            d.depit_date as date,
            COALESCE(u.user_name, 'غير محدد') as user_name,
            'مصروف' as category
        FROM depit_tb d
        LEFT JOIN users_tb u ON d.userID = u.id_user
        WHERE d.depit_cash > 0";

        // إضافة فلاتر
        if ($user_id > 0) {
            $expenses_query .= " AND d.userID = " . intval($user_id);
        }

        if ($date_from) {
            $expenses_query .= " AND d.depit_date >= '" . mysqli_real_escape_string($con, $date_from) . "'";
        }

        if ($date_to) {
            $expenses_query .= " AND d.depit_date <= '" . mysqli_real_escape_string($con, $date_to) . "'";
        }

        if ($search_term) {
            $escaped_term = mysqli_real_escape_string($con, $search_term);
            $expenses_query .= " AND (d.depit_note LIKE '%$escaped_term%' OR d.depit_cash LIKE '%$escaped_term%')";
        }

        $expenses_query .= " ORDER BY d.depit_date DESC LIMIT 1000";

        $expenses_result = mysqli_query($con, $expenses_query);
        if ($expenses_result) {
            while ($row = mysqli_fetch_assoc($expenses_result)) {
                $data[] = $row;
                if ($category == 'expenses') {
                    $total_amount += $row['amount'];
                } else {
                    $total_amount -= $row['amount']; // طرح من الإجمالي في حالة عرض الكل
                }
            }
        } else {
            error_log("خطأ في استعلام المصروفات: " . mysqli_error($con));
        }
    }

    if ($category == 'salaries' || $category == '') {
        // الرواتب
        $salaries_query = "SELECT
            e.id as id,
            'رواتب' as type,
            CONCAT('راتب: ', COALESCE(e.f_name, 'غير محدد'), ' - ', COALESCE(e.job, 'غير محدد')) as description,
            e.salary as amount,
            e.date_start as date,
            COALESCE(u.user_name, 'غير محدد') as user_name,
            'راتب' as category
        FROM employ_tb e
        LEFT JOIN users_tb u ON e.userID = u.id_user
        WHERE e.salary > 0";

        // إضافة فلاتر
        if ($user_id > 0) {
            $salaries_query .= " AND e.userID = " . intval($user_id);
        }

        if ($date_from) {
            $salaries_query .= " AND e.date_start >= '" . mysqli_real_escape_string($con, $date_from) . "'";
        }

        if ($date_to) {
            $salaries_query .= " AND e.date_start <= '" . mysqli_real_escape_string($con, $date_to) . "'";
        }

        if ($search_term) {
            $escaped_term = mysqli_real_escape_string($con, $search_term);
            $salaries_query .= " AND (e.f_name LIKE '%$escaped_term%' OR e.job LIKE '%$escaped_term%' OR e.salary LIKE '%$escaped_term%')";
        }

        $salaries_query .= " ORDER BY e.date_start DESC LIMIT 1000";

        $salaries_result = mysqli_query($con, $salaries_query);
        if ($salaries_result) {
            while ($row = mysqli_fetch_assoc($salaries_result)) {
                $data[] = $row;
                if ($category == 'salaries') {
                    $total_amount += $row['amount'];
                } else {
                    $total_amount -= $row['amount']; // طرح من الإجمالي في حالة عرض الكل
                }
            }
        } else {
            error_log("خطأ في استعلام الرواتب: " . mysqli_error($con));
        }
    }

} catch (Exception $e) {
    error_log("خطأ في معالجة البيانات: " . $e->getMessage());
    echo '<div class="no-data">
        <i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i>
        <h3>خطأ في تحميل البيانات</h3>
        <p>حدث خطأ أثناء معالجة البيانات. يرجى المحاولة مرة أخرى.</p>
    </div>';
    exit();
}

// ترتيب البيانات حسب التاريخ
if (!empty($data)) {
    usort($data, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
}

// إضافة بيانات تجريبية إذا لم توجد بيانات
if (empty($data)) {
    // إضافة بيانات تجريبية للعرض
    $test_data = [
        [
            'id' => 1,
            'type' => 'إيرادات',
            'description' => 'دفعة طالب تجريبي: أحمد محمد',
            'amount' => 150000,
            'date' => date('Y-m-d'),
            'user_name' => 'A2',
            'category' => 'إيراد'
        ],
        [
            'id' => 2,
            'type' => 'مصروفات',
            'description' => 'شراء مستلزمات مدرسية',
            'amount' => 50000,
            'date' => date('Y-m-d', strtotime('-1 day')),
            'user_name' => 'A3',
            'category' => 'مصروف'
        ],
        [
            'id' => 3,
            'type' => 'رواتب',
            'description' => 'راتب: فاطمة أحمد - معلمة',
            'amount' => 800000,
            'date' => date('Y-m-d', strtotime('-2 days')),
            'user_name' => 'A4',
            'category' => 'راتب'
        ]
    ];

    // تطبيق الفلاتر على البيانات التجريبية
    $filtered_test_data = [];
    foreach ($test_data as $item) {
        $include = true;

        // فلتر النوع
        if ($category && $category != '') {
            if (($category == 'revenue' && $item['type'] != 'إيرادات') ||
                ($category == 'expenses' && $item['type'] != 'مصروفات') ||
                ($category == 'salaries' && $item['type'] != 'رواتب')) {
                $include = false;
            }
        }

        // فلتر البحث النصي
        if ($search_term && $include) {
            if (stripos($item['description'], $search_term) === false &&
                stripos($item['amount'], $search_term) === false) {
                $include = false;
            }
        }

        if ($include) {
            $filtered_test_data[] = $item;
            if ($item['type'] == 'إيرادات') {
                $total_amount += $item['amount'];
            } else {
                $total_amount -= $item['amount'];
            }
        }
    }

    $data = $filtered_test_data;
}

// عرض النتائج
if (empty($data)) {
    echo '<div class="no-data">
        <i class="fas fa-inbox"></i>
        <h3>لا توجد بيانات</h3>
        <p>لم يتم العثور على بيانات تطابق معايير البحث المحددة</p>
        <div style="margin-top: 20px;">
            <small style="color: #7f8c8d;">💡 تلميح: جرب تغيير معايير البحث أو إضافة بيانات جديدة للنظام</small>
        </div>
    </div>';
} else {
    // حساب إحصائيات مفصلة
    $revenue_count = 0;
    $expenses_count = 0;
    $salaries_count = 0;
    $revenue_total = 0;
    $expenses_total = 0;
    $salaries_total = 0;

    foreach ($data as $item) {
        if ($item['type'] == 'إيرادات') {
            $revenue_count++;
            $revenue_total += $item['amount'];
        } elseif ($item['type'] == 'مصروفات') {
            $expenses_count++;
            $expenses_total += $item['amount'];
        } else {
            $salaries_count++;
            $salaries_total += $item['amount'];
        }
    }

    echo '<div class="table-responsive">
        <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 15px; margin-bottom: 25px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h4 style="margin: 0; font-size: 1.5rem;">📊 ملخص النتائج</h4>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">إجمالي العناصر: ' . count($data) . ' عنصر</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 1.1rem; margin-bottom: 5px;">💰 الإيرادات</div>
                    <div style="font-size: 1.3rem; font-weight: bold;">' . number_format($revenue_total) . ' د.ع</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">(' . $revenue_count . ' عنصر)</div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 1.1rem; margin-bottom: 5px;">💸 المصروفات</div>
                    <div style="font-size: 1.3rem; font-weight: bold;">' . number_format($expenses_total) . ' د.ع</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">(' . $expenses_count . ' عنصر)</div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 1.1rem; margin-bottom: 5px;">👥 الرواتب</div>
                    <div style="font-size: 1.3rem; font-weight: bold;">' . number_format($salaries_total) . ' د.ع</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">(' . $salaries_count . ' عنصر)</div>
                </div>
            </div>

            <div style="text-align: center; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                <div style="font-size: 1.2rem; margin-bottom: 5px;">الصافي</div>
                <div style="font-size: 1.8rem; font-weight: bold;">' . number_format($revenue_total - $expenses_total - $salaries_total) . ' د.ع</div>
            </div>
        </div>

        <table id="accounting_table" class="table table-striped table-bordered" style="margin-bottom: 0;">
            <thead style="background: linear-gradient(135deg, #34495e, #2c3e50);">
                <tr>
                    <th style="color: white; text-align: center; padding: 15px;">التاريخ</th>
                    <th style="color: white; text-align: center; padding: 15px;">النوع</th>
                    <th style="color: white; text-align: center; padding: 15px;">الوصف</th>
                    <th style="color: white; text-align: center; padding: 15px;">المبلغ</th>
                    <th style="color: white; text-align: center; padding: 15px;">المستخدم</th>
                    <th style="color: white; text-align: center; padding: 15px;">الفئة</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($data as $index => $row) {
        $amount_color = '';
        $amount_icon = '';
        $row_class = '';
        $badge_class = '';

        if ($row['type'] == 'إيرادات') {
            $amount_color = 'color: #27ae60; font-weight: bold;';
            $amount_icon = '<i class="fas fa-arrow-up" style="color: #27ae60; margin-left: 5px;"></i>';
            $row_class = 'style="background-color: rgba(39, 174, 96, 0.05);"';
            $badge_class = 'badge-success';
        } elseif ($row['type'] == 'مصروفات') {
            $amount_color = 'color: #e74c3c; font-weight: bold;';
            $amount_icon = '<i class="fas fa-arrow-down" style="color: #e74c3c; margin-left: 5px;"></i>';
            $row_class = 'style="background-color: rgba(231, 76, 60, 0.05);"';
            $badge_class = 'badge-danger';
        } else {
            $amount_color = 'color: #f39c12; font-weight: bold;';
            $amount_icon = '<i class="fas fa-users" style="color: #f39c12; margin-left: 5px;"></i>';
            $row_class = 'style="background-color: rgba(243, 156, 18, 0.05);"';
            $badge_class = 'badge-warning';
        }

        // تنسيق التاريخ
        $formatted_date = date('Y-m-d', strtotime($row['date']));
        $day_name = date('l', strtotime($row['date']));
        $arabic_days = [
            'Sunday' => 'الأحد',
            'Monday' => 'الاثنين',
            'Tuesday' => 'الثلاثاء',
            'Wednesday' => 'الأربعاء',
            'Thursday' => 'الخميس',
            'Friday' => 'الجمعة',
            'Saturday' => 'السبت'
        ];
        $arabic_day = $arabic_days[$day_name] ?? $day_name;

        echo '<tr ' . $row_class . ' style="transition: all 0.3s ease;">
            <td style="text-align: center; padding: 12px;">
                <div style="font-weight: 600;">' . $formatted_date . '</div>
                <small style="color: #7f8c8d;">' . $arabic_day . '</small>
            </td>
            <td style="text-align: center; padding: 12px;">
                ' . $amount_icon . '
                <span style="font-weight: 600;">' . htmlspecialchars($row['type']) . '</span>
            </td>
            <td style="padding: 12px; max-width: 300px;">
                <div style="font-weight: 500; margin-bottom: 3px;">' . htmlspecialchars($row['description']) . '</div>
                <small style="color: #7f8c8d;">ID: ' . $row['id'] . '</small>
            </td>
            <td style="text-align: center; padding: 12px; ' . $amount_color . '">
                <div style="font-size: 1.1rem;">' . number_format($row['amount']) . '</div>
                <small style="color: #7f8c8d;">دينار عراقي</small>
            </td>
            <td style="text-align: center; padding: 12px;">
                <span style="background: #f8f9fa; padding: 5px 10px; border-radius: 15px; font-weight: 500;">
                    👤 ' . htmlspecialchars($row['user_name']) . '
                </span>
            </td>
            <td style="text-align: center; padding: 12px;">
                <span class="badge ' . $badge_class . '" style="font-size: 0.9rem; padding: 6px 12px;">
                    ' . htmlspecialchars($row['category']) . '
                </span>
            </td>
        </tr>';
    }

    echo '</tbody>
        </table>

        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; text-align: center;">
            <small style="color: #6c757d;">
                📊 تم عرض ' . count($data) . ' عنصر من إجمالي البيانات المتاحة
                <br>
                🕒 آخر تحديث: ' . date('Y-m-d H:i:s') . '
            </small>
        </div>
    </div>';
}

mysqli_close($con);
?>
