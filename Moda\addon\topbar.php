<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){


 }else{
        header("location:http:../login.php",true);
        die("");

    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      if(isset($_GET['exitbtn'])){
        header("location:../logout.php",true);
        exit();
      }


?>

<!-- تضمين CSS للأزرار الأفقية -->
<link rel="stylesheet" href="../Admin/css/admin_navbar.css">
<link rel="stylesheet" href="../Admin/css/navbar_notifications.css">

<style>
/* إخفاء التصميم القديم وإظهار التصميم الجديد */
nav ul {
    display: none !important;
}

.quick-actions {
    display: flex !important;
}

/* تأكيد أن الأزرار تظهر بالشكل الصحيح */
.quick-btn {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إخفاء عناصر القائمة القديمة */
.menu-btn, #click, #capss {
    display: none !important;
}

/* تأكيد تطبيق التصميم الجديد */
nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 999 !important;
}

body {
    margin-top: 90px !important;
    padding-top: 20px !important;
}

/* إخفاء الشريط الجانبي القديم تماماً */
.sidebar, .sidebar-wrapper, .sidebar-content {
    display: none !important;
}

/* تأكيد عرض الأزرار الأفقية */
.quick-actions .quick-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    padding: 6px 10px !important;
    margin: 0 3px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.quick-actions .quick-btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    color: white !important;
    text-decoration: none !important;
}

.quick-actions .quick-btn i {
    color: white !important;
    margin-left: 2px !important;
}

.quick-actions .quick-btn span {
    color: white !important;
    font-size: 11px !important;
}

/* شارة الإشعارات */
.notification-badge-nav {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.notifications-btn {
    position: relative;
}

/* تحسين مظهر منطقة المستخدم */
.user-form {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.welcome-label {
    color: white;
    font-weight: 500;
    margin: 0;
    font-size: 0.9rem;
}

.user-form .btn {
    padding: 8px 15px;
    font-size: 0.85rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.user-form .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
}

/* تحسين الاستجابة للهواتف */
@media (max-width: 768px) {
    .quick-actions {
        flex-wrap: wrap;
        max-width: 60%;
        gap: 3px;
    }

    .quick-btn span {
        display: none;
    }

    .quick-btn {
        min-width: 40px;
        padding: 8px;
        justify-content: center;
    }

    .user-form {
        flex-direction: column;
        gap: 5px;
    }

    .welcome-label {
        font-size: 0.8rem;
        text-align: center;
    }
}
</style>

<script>
// التأكد من إخفاء العناصر القديمة وإظهار الجديدة
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء القائمة القديمة
    const oldNavUl = document.querySelector('nav ul:not(.quick-actions)');
    if (oldNavUl && !oldNavUl.classList.contains('quick-actions')) {
        oldNavUl.style.display = 'none';
    }

    // إظهار الأزرار الجديدة
    const quickActions = document.querySelector('.quick-actions');
    if (quickActions) {
        quickActions.style.display = 'flex';
        quickActions.style.visibility = 'visible';
        quickActions.style.opacity = '1';
    }

    // إخفاء عناصر القائمة القديمة
    const menuBtn = document.querySelector('.menu-btn');
    const clickInput = document.querySelector('#click');
    const capss = document.querySelector('#capss');

    if (menuBtn) menuBtn.style.display = 'none';
    if (clickInput) clickInput.style.display = 'none';
    if (capss) capss.style.display = 'none';

    // تطبيق التصميم الجديد على nav
    const nav = document.querySelector('nav');
    if (nav) {
        nav.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        nav.style.position = 'fixed';
        nav.style.top = '0';
        nav.style.left = '0';
        nav.style.zIndex = '999';
        nav.style.display = 'flex';
        nav.style.alignItems = 'center';
        nav.style.justifyContent = 'space-between';
    }
});
</script>

<nav>
<div class='logo2'><img src="../Admin/css/logooo.png" alt=""></div>

         <!-- الأزرار الأفقية للمدقق -->
         <div class="quick-actions">
             <!-- زر الإشعارات -->
             <a href="#" onclick="toggleNotifications(event)" class="quick-btn notifications-btn" title="الإشعارات">
                 <i class="fas fa-bell"></i>
                 <span>الإشعارات</span>
                 <span class="notification-badge-nav" id="notificationBadgeNav" style="display: none;">0</span>
             </a>

             <!-- الأزرار الرئيسية -->
             <a href="home.php" class="quick-btn home-btn" title="الرئيسية">
                 <i class="fa-solid fa-home"></i>
                 <span>الرئيسية</span>
             </a>
             <a href="ATTANC.php" class="quick-btn" title="حضور الطلاب">
                 <i class="fa-solid fa-school"></i>
                 <span>حضور الطلاب</span>
             </a>
             <a href="employee_AttANC.php" class="quick-btn" title="حضور الموظفين">
                 <i class="fa-solid fa-user-clock"></i>
                 <span>حضور الموظفين</span>
             </a>
             <a href="manage_needs.php" class="quick-btn" title="الاحتياجات">
                 <i class="fa-solid fa-clipboard-list"></i>
                 <span>الاحتياجات</span>
             </a>
             <a href="manage_leaves.php" class="quick-btn" title="طلبات الإجازة">
                 <i class="fa-solid fa-calendar-check"></i>
                 <span>طلبات الإجازة</span>
             </a>
             <a href="About_us.php" class="quick-btn" title="ماذا عنا">
                 <i class="fa-solid fa-info-circle"></i>
                 <span>ماذا عنا</span>
             </a>
         </div>

         <!-- منطقة المستخدم -->
         <div class="logo">
             <form action="" class="user-form">
                 <label class="welcome-label"><?php echo $_SESSION['user']->user_name; ?> مرحبا بك</label>
                 <button class='btn btn-danger' name="exitbtn" type='submit' id='exit_btn'>
                     <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                 </button>
             </form>
         </div>

      </nav>

<!-- إضافة مكون الإشعارات للمدقق -->
<script src="js/notifications.js"></script>

<?php
$notifications_file = '../Admin/notifications_navbar.php';
if (file_exists($notifications_file)) {
    include $notifications_file;
}
?>
