<?php
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار ملف infostudF.php</h2>";

include "addon/dbcon.php";

// الحصول على أول مستخدم متاح
$sql = "SELECT id_user, user_name FROM users_tb LIMIT 1";
$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $user = mysqli_fetch_assoc($result);
    $user_id = $user['id_user'];
    $user_name = $user['user_name'];
    
    echo "<h3>اختبار المستخدم: $user_name (ID: $user_id)</h3>";
    
    // محاكاة POST request
    $_POST['id'] = $user_id;
    
    echo "<h4>محتوى ملف infostudF.php:</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
    
    // تضمين الملف وعرض النتيجة
    ob_start();
    include "addon/infostudF.php";
    $output = ob_get_clean();
    
    echo $output;
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ لا توجد مستخدمين في قاعدة البيانات</p>";
}

// اختبار مع مستخدم غير موجود
echo "<h3>اختبار مع مستخدم غير موجود:</h3>";
$_POST['id'] = 99999;
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
ob_start();
include "addon/infostudF.php";
$output = ob_get_clean();
echo $output;
echo "</div>";

// اختبار بدون اختيار مستخدم
echo "<h3>اختبار بدون اختيار مستخدم:</h3>";
unset($_POST['id']);
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
ob_start();
include "addon/infostudF.php";
$output = ob_get_clean();
echo $output;
echo "</div>";
?>
