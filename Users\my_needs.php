<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";
include "notifications_helper.php";

$message = '';
$messageType = '';

// التحقق من رسالة النجاح من URL
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "تم إرسال طلب الاحتياج بنجاح";
    $messageType = 'success';
}

// معالجة إرسال طلب احتياج جديد
if(isset($_POST['submit_need'])){
    try {
        $user_id = $_SESSION['user']->id_user;
        $need_name = trim($_POST['need_name']);
        $need_type = $_POST['need_type'];
        $need_details = trim($_POST['need_details']);
        $request_date = date('Y-m-d');

        if(empty($need_name) || empty($need_details)) {
            $message = "يرجى ملء جميع الحقول المطلوبة";
            $messageType = 'error';
        } else {
            $stmt = $con->prepare("INSERT INTO needs_requests (user_id, need_name, request_date, need_type, need_details) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("issss", $user_id, $need_name, $request_date, $need_type, $need_details);

            if($stmt->execute()) {
                $message = "تم إرسال طلب الاحتياج بنجاح";
                $messageType = 'success';

                // الحصول على معرف الطلب الجديد
                $need_id = $con->insert_id;

                // إرسال إشعار للإدارة والمدقق
                try {
                    addNeedRequestNotification($con, $_SESSION['user']->user_name, $need_type, $need_id);
                } catch (Exception $e) {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
                }

                // إعادة توجيه لتجنب إعادة الإرسال عند التحديث
                header("Location: my_needs.php?success=1");
                exit();

            } else {
                $message = "حدث خطأ أثناء إرسال الطلب: " . $con->error;
                $messageType = 'error';
            }
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

// التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
function checkAndAddColumns($con) {
    // فحص الأعمدة الموجودة
    $check_columns = "SHOW COLUMNS FROM needs_requests";
    $result = mysqli_query($con, $check_columns);

    $existing_columns = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $existing_columns[] = $row['Field'];
        }
    }

    // إضافة عمود user_comment إذا لم يكن موجوداً
    if (!in_array('user_comment', $existing_columns)) {
        $add_user_comment = "ALTER TABLE needs_requests ADD COLUMN user_comment TEXT NULL";
        mysqli_query($con, $add_user_comment);
    }

    // إضافة عمود user_update_date إذا لم يكن موجوداً
    if (!in_array('user_update_date', $existing_columns)) {
        $add_user_update_date = "ALTER TABLE needs_requests ADD COLUMN user_update_date DATETIME NULL";
        mysqli_query($con, $add_user_update_date);
    }

    // تحديث حالات الطلبات لتشمل "ملغي"
    $update_status = "ALTER TABLE needs_requests MODIFY COLUMN status ENUM('قيد المراجعة', 'تم التوفير', 'لم يتم التوفير', 'ملغي') DEFAULT 'قيد المراجعة'";
    mysqli_query($con, $update_status);
}

// تشغيل فحص الأعمدة
checkAndAddColumns($con);

// معالجة تحديث حالة الطلب من قبل المستخدم
if(isset($_POST['update_status'])){
    try {
        $need_id = intval($_POST['need_id']);
        $new_status = $_POST['new_status'];
        $user_comment = trim($_POST['user_comment']);
        $user_id = $_SESSION['user']->id_user;

        // التحقق من أن الطلب يخص المستخدم الحالي
        $check_query = "SELECT id_need FROM needs_requests WHERE id_need = ? AND user_id = ?";
        $check_stmt = $con->prepare($check_query);
        $check_stmt->bind_param("ii", $need_id, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if($check_result->num_rows > 0) {
            // محاولة التحديث مع الأعمدة الجديدة أولاً
            $update_query = "UPDATE needs_requests SET
                            status = ?,
                            user_comment = ?,
                            user_update_date = NOW()
                            WHERE id_need = ? AND user_id = ?";
            $update_stmt = $con->prepare($update_query);

            if ($update_stmt) {
                $update_stmt->bind_param("ssii", $new_status, $user_comment, $need_id, $user_id);

                if($update_stmt->execute()) {
                    $message = "تم تحديث حالة الطلب بنجاح";
                    $messageType = 'success';

                    // إرسال إشعار للإدارة والمدقق بالتحديث (إذا كانت وظيفة الإشعارات متوفرة)
                    if (function_exists('addNotification')) {
                        addNotification($con, 'Admin', 'تحديث طلب احتياج',
                            "قام المستخدم بتحديث حالة طلب الاحتياج إلى: $new_status",
                            'needs', $need_id);
                        addNotification($con, 'Auditor', 'تحديث طلب احتياج',
                            "قام المستخدم بتحديث حالة طلب الاحتياج إلى: $new_status",
                            'needs', $need_id);
                    }
                } else {
                    $message = "حدث خطأ أثناء تحديث الطلب: " . $con->error;
                    $messageType = 'error';
                }
            } else {
                // إذا فشل التحديث مع الأعمدة الجديدة، جرب التحديث بدونها
                $simple_update_query = "UPDATE needs_requests SET status = ? WHERE id_need = ? AND user_id = ?";
                $simple_update_stmt = $con->prepare($simple_update_query);
                $simple_update_stmt->bind_param("sii", $new_status, $need_id, $user_id);

                if($simple_update_stmt->execute()) {
                    $message = "تم تحديث حالة الطلب بنجاح (بدون تعليق)";
                    $messageType = 'success';
                } else {
                    $message = "حدث خطأ أثناء تحديث الطلب: " . $con->error;
                    $messageType = 'error';
                }
            }
        } else {
            $message = "غير مسموح لك بتحديث هذا الطلب";
            $messageType = 'error';
        }
    } catch (Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

$user_id = $_SESSION['user']->id_user;
$query = "SELECT nr.*, u.user_name as response_by_name
          FROM needs_requests nr
          LEFT JOIN users_tb u ON nr.response_by = u.id_user
          WHERE nr.user_id = ?
          ORDER BY nr.created_at DESC";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>احتياجاتي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .needs-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .need-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .need-card:hover {
            transform: translateY(-5px);
        }
        
        .need-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .need-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .need-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
        }
        
        .info-item i {
            color: #667eea;
            width: 20px;
        }
        
        .need-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .admin-response {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .response-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .user-comment {
            background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .comment-header {
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .update-form {
            background: linear-gradient(135deg, #fff3e0, #fce4ec);
            border: 1px solid #ff9800;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            display: none;
        }

        .update-form h4 {
            color: #e65100;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .update-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-update {
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        .btn-cancel {
            background: linear-gradient(45deg, #757575, #616161);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(117, 117, 117, 0.3);
        }

        .toggle-update-btn {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .toggle-update-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
        }
        
        .new-request-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            border: none;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .new-request-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            color: white;
        }

        .request-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .request-form.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .submit-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }

        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="needs-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                الاحتياجات - عرض وطلب
            </h1>
            <button class="new-request-btn" onclick="toggleRequestForm()">
                <i class="fas fa-plus"></i>
                طلب احتياج جديد
            </button>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if(!empty($message)): ?>
            <div class="alert alert-<?= $messageType ?>">
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                <?= $message ?>
            </div>
        <?php endif; ?>

        <!-- نموذج طلب احتياج جديد -->
        <div class="request-form" id="requestForm">
            <h3 style="color: #667eea; margin-bottom: 25px;">
                <i class="fas fa-clipboard-list"></i>
                طلب احتياج جديد
            </h3>

            <form method="POST">
                <div class="form-group">
                    <label class="form-label">اسم الاحتياج *</label>
                    <input type="text" name="need_name" class="form-control" placeholder="أدخل اسم الاحتياج" required>
                </div>

                <div class="form-group">
                    <label class="form-label">نوع الاحتياج *</label>
                    <select name="need_type" class="form-select" required>
                        <option value="">اختر نوع الاحتياج</option>
                        <option value="احتياج">احتياج</option>
                        <option value="صيانة">صيانة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">تفاصيل الاحتياج *</label>
                    <textarea name="need_details" class="form-control" rows="5" placeholder="اكتب تفاصيل الاحتياج بوضوح..." required></textarea>
                </div>

                <button type="submit" name="submit_need" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الطلب
                </button>
            </form>
        </div>
        
        <?php if($result->num_rows > 0): ?>
            <?php while($need = $result->fetch_assoc()): ?>
                <div class="need-card">
                    <div class="need-header">
                        <h3 class="need-title"><?php echo htmlspecialchars($need['need_name']); ?></h3>
                        <span class="status-badge status-<?php 
                            echo $need['status'] == 'قيد المراجعة' ? 'pending' : 
                                ($need['status'] == 'تم التوفير' ? 'approved' : 'rejected'); 
                        ?>">
                            <?php echo $need['status']; ?>
                        </span>
                    </div>
                    
                    <div class="need-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($need['request_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-tag"></i>
                            <span>النوع: <?php echo $need['need_type']; ?></span>
                        </div>
                        <?php if($need['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($need['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="need-details">
                        <strong>تفاصيل الاحتياج:</strong><br>
                        <?php echo nl2br(htmlspecialchars($need['need_details'])); ?>
                    </div>
                    
                    <?php if($need['admin_response']): ?>
                        <div class="admin-response">
                            <div class="response-header">
                                <i class="fas fa-reply"></i>
                                رد الإدارة:
                                <?php if($need['response_by_name']): ?>
                                    (بواسطة: <?php echo htmlspecialchars($need['response_by_name']); ?>)
                                <?php endif; ?>
                            </div>
                            <?php echo nl2br(htmlspecialchars($need['admin_response'])); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($need['user_comment']) && $need['user_comment']): ?>
                        <div class="user-comment">
                            <div class="comment-header">
                                <i class="fas fa-comment"></i>
                                تعليقك على الطلب:
                                <?php if(isset($need['user_update_date']) && $need['user_update_date']): ?>
                                    <small>(تم التحديث: <?php echo date('Y/m/d H:i', strtotime($need['user_update_date'])); ?>)</small>
                                <?php endif; ?>
                            </div>
                            <?php echo nl2br(htmlspecialchars($need['user_comment'])); ?>
                        </div>
                    <?php endif; ?>

                    <!-- زر تحديث الحالة -->
                    <button class="toggle-update-btn" onclick="toggleUpdateForm(<?php echo $need['id_need']; ?>)">
                        <i class="fas fa-edit"></i>
                        تحديث حالة الطلب
                    </button>

                    <!-- نموذج تحديث الحالة -->
                    <div class="update-form" id="updateForm<?php echo $need['id_need']; ?>">
                        <h4>
                            <i class="fas fa-edit"></i>
                            تحديث حالة الطلب
                        </h4>
                        <form method="POST">
                            <input type="hidden" name="need_id" value="<?php echo $need['id_need']; ?>">

                            <div class="form-group">
                                <label for="new_status<?php echo $need['id_need']; ?>">الحالة الجديدة:</label>
                                <select name="new_status" id="new_status<?php echo $need['id_need']; ?>" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="قيد المراجعة" <?php echo $need['status'] == 'قيد المراجعة' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                    <option value="تم التوفير" <?php echo $need['status'] == 'تم التوفير' ? 'selected' : ''; ?>>تم التوفير</option>
                                    <option value="لم يتم التوفير" <?php echo $need['status'] == 'لم يتم التوفير' ? 'selected' : ''; ?>>لم يتم التوفير</option>
                                    <option value="ملغي" <?php echo $need['status'] == 'ملغي' ? 'selected' : ''; ?>>ملغي</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="user_comment<?php echo $need['id_need']; ?>">تعليق أو ملاحظة:</label>
                                <textarea name="user_comment" id="user_comment<?php echo $need['id_need']; ?>"
                                          placeholder="اكتب تعليقك أو ملاحظتك حول الطلب..."><?php echo htmlspecialchars(isset($need['user_comment']) ? $need['user_comment'] : ''); ?></textarea>
                            </div>

                            <div class="update-buttons">
                                <button type="submit" name="update_status" class="btn-update">
                                    <i class="fas fa-save"></i>
                                    حفظ التحديث
                                </button>
                                <button type="button" class="btn-cancel" onclick="toggleUpdateForm(<?php echo $need['id_need']; ?>)">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="need-card">
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>لا توجد طلبات احتياج</h3>
                    <p>لم تقم بإرسال أي طلبات احتياج بعد</p>
                    <button class="new-request-btn" onclick="toggleRequestForm()">
                        <i class="fas fa-plus"></i>
                        إرسال طلب احتياج
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function toggleRequestForm() {
            const form = document.getElementById('requestForm');
            const button = document.querySelector('.new-request-btn');

            if (form.classList.contains('show')) {
                form.classList.remove('show');
                button.innerHTML = '<i class="fas fa-plus"></i> طلب احتياج جديد';
            } else {
                form.classList.add('show');
                button.innerHTML = '<i class="fas fa-minus"></i> إخفاء النموذج';
                form.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // وظيفة إظهار/إخفاء نموذج تحديث الحالة
        function toggleUpdateForm(needId) {
            const form = document.getElementById('updateForm' + needId);

            if (form.style.display === 'none' || form.style.display === '') {
                form.style.display = 'block';
                form.scrollIntoView({ behavior: 'smooth' });
            } else {
                form.style.display = 'none';
            }
        }

        // إظهار النموذج تلقائياً إذا كان هناك رسالة خطأ
        <?php if(!empty($message) && $messageType === 'error'): ?>
            document.addEventListener('DOMContentLoaded', function() {
                toggleRequestForm();
            });
        <?php endif; ?>
    </script>
</body>
</html>
