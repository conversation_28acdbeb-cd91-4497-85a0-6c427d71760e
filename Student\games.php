<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

// إنشاء جدول الألعاب إذا لم يكن موجوداً
$create_table = "CREATE TABLE IF NOT EXISTS app_games (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    game_type ENUM('educational', 'puzzle', 'memory', 'math', 'language') NOT NULL,
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
    age_group VARCHAR(20),
    game_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    play_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
mysqli_query($con, $create_table);

// جلب الألعاب
$games_query = "SELECT * FROM app_games WHERE is_active = 1 ORDER BY play_count DESC, created_at DESC";
$games_result = mysqli_query($con, $games_query);

// إضافة ألعاب افتراضية إذا لم توجد
if (mysqli_num_rows($games_result) == 0) {
    $default_games = [
        [
            'title' => '🎨 لعبة التلوين',
            'description' => 'لون الصور الجميلة واستمتع بالألوان الزاهية',
            'game_type' => 'educational',
            'difficulty_level' => 'easy',
            'age_group' => '3-6 سنوات'
        ],
        [
            'title' => '🐾 أصوات الحيوانات',
            'description' => 'تعلم أصوات الحيوانات المختلفة بطريقة ممتعة',
            'game_type' => 'educational',
            'difficulty_level' => 'easy',
            'age_group' => '2-5 سنوات'
        ],
        [
            'title' => '🧩 بازل الأشكال',
            'description' => 'ضع الأشكال في مكانها الصحيح',
            'game_type' => 'puzzle',
            'difficulty_level' => 'easy',
            'age_group' => '3-6 سنوات'
        ],
        [
            'title' => '🎵 الآلات الموسيقية',
            'description' => 'اكتشف الأصوات الموسيقية المختلفة',
            'game_type' => 'educational',
            'difficulty_level' => 'easy',
            'age_group' => '3-7 سنوات'
        ],
        [
            'title' => '🌈 مطابقة الألوان',
            'description' => 'اربط الألوان المتشابهة معاً',
            'game_type' => 'memory',
            'difficulty_level' => 'easy',
            'age_group' => '3-6 سنوات'
        ],
        [
            'title' => '🚗 سباق السيارات',
            'description' => 'لعبة سباق ممتعة وآمنة للأطفال',
            'game_type' => 'educational',
            'difficulty_level' => 'easy',
            'age_group' => '4-8 سنوات'
        ],
        [
            'title' => '🍎 عد الفواكه',
            'description' => 'تعلم العد مع الفواكه اللذيذة',
            'game_type' => 'math',
            'difficulty_level' => 'easy',
            'age_group' => '3-6 سنوات'
        ],
        [
            'title' => '⭐ جمع النجوم',
            'description' => 'اجمع النجوم الذهبية في السماء',
            'game_type' => 'educational',
            'difficulty_level' => 'easy',
            'age_group' => '3-7 سنوات'
        ]
    ];
    
    foreach ($default_games as $game) {
        $insert_query = "INSERT INTO app_games (title, description, game_type, difficulty_level, age_group) 
                        VALUES ('{$game['title']}', '{$game['description']}', '{$game['game_type']}', 
                               '{$game['difficulty_level']}', '{$game['age_group']}')";
        mysqli_query($con, $insert_query);
    }
    
    // إعادة جلب الألعاب
    $games_result = mysqli_query($con, $games_query);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#e74c3c">
    <title>🎮 الألعاب التعليمية - أكاديمية كيدز</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/app-style.css">
    <style>
        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .app-content {
            margin-top: 70px;
            padding: 1rem;
            padding-bottom: 80px;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
        }

        .game-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 3px solid rgba(255, 255, 255, 0.8);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .game-card.type-educational::before { background: linear-gradient(45deg, #3498db, #2980b9); }
        .game-card.type-puzzle::before { background: linear-gradient(45deg, #9b59b6, #8e44ad); }
        .game-card.type-memory::before { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .game-card.type-math::before { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .game-card.type-language::before { background: linear-gradient(45deg, #27ae60, #2ecc71); }

        .game-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .game-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            animation: gameIconFloat 3s ease-in-out infinite;
        }

        .type-educational .game-icon { background: linear-gradient(45deg, #3498db, #2980b9); }
        .type-puzzle .game-icon { background: linear-gradient(45deg, #9b59b6, #8e44ad); }
        .type-memory .game-icon { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .type-math .game-icon { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .type-language .game-icon { background: linear-gradient(45deg, #27ae60, #2ecc71); }

        .game-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .game-description {
            color: #34495e;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .game-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .difficulty-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .difficulty-easy { background: #2ecc71; color: white; }
        .difficulty-medium { background: #f39c12; color: white; }
        .difficulty-hard { background: #e74c3c; color: white; }

        .age-group {
            background: rgba(52, 73, 94, 0.1);
            color: #34495e;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .play-btn {
            width: 100%;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .play-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            color: white;
        }

        .play-count {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 3px solid rgba(255, 107, 107, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #7f8c8d;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 15px;
        }

        .nav-item.active, .nav-item:hover {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .nav-item span {
            font-size: 0.7rem;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @keyframes gameIconFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-10px) rotate(5deg); }
            50% { transform: translateY(0) rotate(0deg); }
            75% { transform: translateY(-5px) rotate(-5deg); }
        }

        @media (max-width: 768px) {
            .games-grid {
                grid-template-columns: 1fr;
            }
            
            .game-card {
                padding: 1rem;
            }
            
            .game-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
            
            .game-meta {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التطبيق العلوي -->
    <div class="app-header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
        </a>
        <h1>🎮 الألعاب التعليمية</h1>
        <div style="width: 40px;"></div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="app-content">
        <?php if (mysqli_num_rows($games_result) > 0): ?>
            <div class="games-grid">
                <?php while ($game = mysqli_fetch_assoc($games_result)): ?>
                    <div class="game-card type-<?php echo $game['game_type']; ?> animate__animated animate__fadeInUp">
                        <div class="play-count">
                            <i class="fas fa-play"></i>
                            <?php echo $game['play_count']; ?>
                        </div>
                        
                        <div class="game-icon">
                            <i class="fas fa-<?php 
                                echo $game['game_type'] == 'educational' ? 'graduation-cap' : 
                                    ($game['game_type'] == 'puzzle' ? 'puzzle-piece' : 
                                    ($game['game_type'] == 'memory' ? 'brain' : 
                                    ($game['game_type'] == 'math' ? 'calculator' : 'language')));
                            ?>"></i>
                        </div>
                        
                        <h3 class="game-title"><?php echo htmlspecialchars($game['title']); ?></h3>
                        
                        <p class="game-description">
                            <?php echo htmlspecialchars($game['description']); ?>
                        </p>
                        
                        <div class="game-meta">
                            <span class="difficulty-badge difficulty-<?php echo $game['difficulty_level']; ?>">
                                <?php 
                                echo $game['difficulty_level'] == 'easy' ? 'سهل' : 
                                    ($game['difficulty_level'] == 'medium' ? 'متوسط' : 'صعب');
                                ?>
                            </span>
                            <span class="age-group"><?php echo htmlspecialchars($game['age_group']); ?></span>
                        </div>
                        
                        <button class="play-btn" onclick="playGame(<?php echo $game['id']; ?>, '<?php echo htmlspecialchars($game['title']); ?>')">
                            <i class="fas fa-play"></i>
                            ابدأ اللعب
                        </button>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-gamepad"></i>
                <h3>لا توجد ألعاب متاحة</h3>
                <p>سيتم إضافة ألعاب تعليمية ممتعة قريباً!</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- شريط التنقل السفلي -->
    <div class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="activities.php" class="nav-item">
            <i class="fas fa-palette"></i>
            <span>الأنشطة</span>
        </a>
        <a href="schedule.php" class="nav-item">
            <i class="fas fa-calendar"></i>
            <span>الجدول</span>
        </a>
        <a href="messages.php" class="nav-item">
            <i class="fas fa-comments"></i>
            <span>الرسائل</span>
        </a>
        <a href="games.php" class="nav-item active">
            <i class="fas fa-gamepad"></i>
            <span>الألعاب</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الدخول
            const gameCards = document.querySelectorAll('.game-card');
            gameCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate__animated', 'animate__fadeInUp');
                }, index * 100);
            });

            // تأثيرات اللمس للهواتف
            if (isMobileDevice()) {
                addTouchEffects();
            }
        });

        function playGame(gameId, gameTitle) {
            // تحديث عداد اللعب
            updatePlayCount(gameId);

            // فتح اللعبة مباشرة
            showGameModal(gameTitle);
        }

        function updatePlayCount(gameId) {
            // إرسال طلب AJAX لتحديث عداد اللعب
            fetch('update_play_count.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ game_id: gameId })
            });
        }

        function showGameModal(gameTitle) {
            // تحديد نوع اللعبة بناءً على العنوان
            let gameUrl = 'simple_game.php';

            if (gameTitle.includes('التلوين')) {
                gameUrl = 'coloring_game.php';
            } else if (gameTitle.includes('أصوات الحيوانات')) {
                gameUrl = 'animal_sounds_game.php';
            } else if (gameTitle.includes('بازل')) {
                gameUrl = 'puzzle_game.php';
            } else if (gameTitle.includes('الآلات الموسيقية')) {
                gameUrl = 'music_game.php';
            } else if (gameTitle.includes('مطابقة الألوان')) {
                gameUrl = 'color_match_game.php';
            } else if (gameTitle.includes('سباق')) {
                gameUrl = 'racing_game.php';
            } else if (gameTitle.includes('عد الفواكه')) {
                gameUrl = 'counting_game.php';
            } else if (gameTitle.includes('جمع النجوم')) {
                gameUrl = 'star_collect_game.php';
            }

            // فتح اللعبة في نافذة جديدة
            window.open(gameUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        function addTouchEffects() {
            const touchElements = document.querySelectorAll('.game-card, .nav-item, .back-btn, .play-btn');
            
            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s';
                });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        }
    </script>
</body>
</html>
