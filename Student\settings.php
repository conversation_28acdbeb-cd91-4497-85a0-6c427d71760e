<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

$student = $_SESSION['student'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .settings-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .settings-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .settings-header {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #6c757d, #495057);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .settings-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            border-bottom: 2px solid #6c757d;
            padding-bottom: 0.8rem;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #6c757d;
        }
        
        .setting-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .setting-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #6c757d, #495057);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .setting-details h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1rem;
        }
        
        .setting-details p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .setting-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .setting-toggle.active {
            background: #28a745;
        }
        
        .setting-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            right: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .setting-toggle.active::before {
            transform: translateX(-30px);
        }
        
        .info-box {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .info-box h4 {
            margin-bottom: 0.8rem;
        }
        
        .info-box p {
            margin: 0;
            opacity: 0.9;
        }
        
        .contact-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
        }
        
        .contact-card h3 {
            margin-bottom: 1rem;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
        }
        
        @media (max-width: 768px) {
            .settings-card {
                padding: 1.5rem;
            }
            
            .setting-item {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }
        }
        
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="settings-section">
        <div class="settings-container">
            <!-- رأس الإعدادات -->
            <div class="settings-header animate-in">
                <div class="header-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h1>الإعدادات</h1>
                <p>إدارة تفضيلات حسابك</p>
            </div>
            
            <!-- معلومات مهمة -->
            <div class="info-box animate-in">
                <h4><i class="fas fa-info-circle"></i> معلومة مهمة</h4>
                <p>هذه الإعدادات خاصة بتطبيق الطالب. لتغيير المعلومات الشخصية، يرجى التواصل مع الإدارة.</p>
            </div>
            
            <!-- إعدادات التطبيق -->
            <div class="settings-card animate-in">
                <h2 class="card-title">
                    <i class="fas fa-mobile-alt"></i>
                    إعدادات التطبيق
                </h2>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="setting-details">
                            <h4>الإشعارات</h4>
                            <p>تلقي إشعارات حول الأخبار والإعلانات</p>
                        </div>
                    </div>
                    <div class="setting-toggle active" onclick="toggleSetting(this)"></div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <div class="setting-details">
                            <h4>الوضع الليلي</h4>
                            <p>تفعيل الوضع الليلي للتطبيق</p>
                        </div>
                    </div>
                    <div class="setting-toggle" onclick="toggleSetting(this)"></div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="setting-details">
                            <h4>اللغة</h4>
                            <p>العربية (افتراضي)</p>
                        </div>
                    </div>
                    <div style="color: #6c757d; font-size: 0.9rem;">العربية</div>
                </div>
            </div>
            
            <!-- إعدادات الخصوصية -->
            <div class="settings-card animate-in">
                <h2 class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    الخصوصية والأمان
                </h2>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="setting-details">
                            <h4>إظهار المعلومات الشخصية</h4>
                            <p>السماح بعرض معلوماتك للمعلمين</p>
                        </div>
                    </div>
                    <div class="setting-toggle active" onclick="toggleSetting(this)"></div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <div class="setting-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="setting-details">
                            <h4>تسجيل الدخول الآمن</h4>
                            <p>طلب كلمة المرور عند كل دخول</p>
                        </div>
                    </div>
                    <div class="setting-toggle active" onclick="toggleSetting(this)"></div>
                </div>
            </div>
            
            <!-- معلومات التواصل -->
            <div class="contact-card animate-in">
                <h3><i class="fas fa-headset"></i> تحتاج مساعدة؟</h3>
                <p>تواصل مع الإدارة لأي استفسارات أو مشاكل</p>
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>07901234567</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSetting(element) {
            element.classList.toggle('active');
            
            // حفظ الإعداد في localStorage
            const settingName = element.parentElement.querySelector('h4').textContent;
            const isActive = element.classList.contains('active');
            localStorage.setItem(settingName, isActive);
            
            // إظهار رسالة تأكيد
            showToast(isActive ? 'تم تفعيل الإعداد' : 'تم إلغاء تفعيل الإعداد');
        }
        
        function showToast(message) {
            // إنشاء toast بسيط
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        // تحميل الإعدادات المحفوظة
        document.addEventListener('DOMContentLoaded', function() {
            const toggles = document.querySelectorAll('.setting-toggle');
            toggles.forEach(toggle => {
                const settingName = toggle.parentElement.querySelector('h4').textContent;
                const savedSetting = localStorage.getItem(settingName);
                if (savedSetting === 'false') {
                    toggle.classList.remove('active');
                }
            });
            
            // تأثيرات بصرية
            const cards = document.querySelectorAll('.animate-in');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
        
        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
