<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
$sm=$_GET['input'];
echo $sm;
      if(isset($_GET['input'])){
        $search=$_GET['input'];
      
        $query="SELECT * FROM employ_tb,users_tb WHERE employ_tb.userID=users_tb.id_user AND CONCAT(f_name,b_date,job,date_start,location,salary,user_name) LIKE '%$search%'";
        $query_run=mysqli_query($con,$query);
        if(mysqli_num_rows($query_run)>0){
          foreach($query_run as $items){
            $id=$items['id_employ'];
        
            ?>
            <tr id="tr_<?php echo $id ?>">
            <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" style="text-decoration: none;color:aliceblue;"  >  <a href="../Admin/edit_employ.php?id=<?php echo $id; ?>" style="text-decoration: none;color:aliceblue;">تعديل</a></button> 
            <button type="button" class="btn btn-secondary mb-1" onclick="deletdata(<?php echo $id ?>)" >حذف </button></td>
            <td><?= $items['user_name'];?></td>
            <td><?= number_format($items['salary']);?></td>
            <td><?= $items['location'];?></td>
            <td><?= $items['date_start'];?></td>
            <td><?= $items['job'];?></td>
            <td><?= $items['b_date'];?></td>
            <td><?= $items['f_name'];?></td> 
          </tr>
      <?php
          }
        }else{
         
          echo "<td colspan=8 style='font-size: 25px;'>لاتوجد   معلومات بهذا الوصف </td>";
  
        }
      }
  
  
  
      ?>