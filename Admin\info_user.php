<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات المستخدمين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
     
   </head>
    <body>
      
    <form action="" metho="POST">
       <div class="search">
  <button class="btn btn-secondary text-light" name="finde"  ><a href="add_user.php"> اضافة مستخدم</a></button>
  <button class="btn btn-secondary text-light" name="finde"  ><a href="Search_user.php"> بحث عن مستخدم</a></button>
        </div>
  </form>
  <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
  

    <table class="table" id="Table">
  <thead>
    <tr>
      <th scope="col">العمليات </th>
      <th scope="col">الصلاحية </th>
      <th scope="col">الرقم السري </th>
      <th scope="col">اسم المستخدم </th>
      
    </tr>
  </thead>
  <tbody id="myTable">
    <?php
      $sql="SELECT * FROM users_tb ";
      $result=mysqli_query($con,$sql);
      if($result){
       while($row=mysqli_fetch_assoc($result)) {
          $id=$row['id_user'];
          $user_name=$row['user_name'];
          $user_pass=$row['user_pass'];
          $role=$row['role'];
           ?>
           <tr id="tr_<?php echo $id ?>">
          <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" name="update"> <a href=edit_user.php?id="<?php echo $id ?>" class="text-light">تعديل </a></button> 
          <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove" onclick="deletdata(<?php echo $id ?>)">حذف </button>
          <td><?php echo $role ?></td>
          <td><?php echo $user_pass ?></td>
          <td><?php echo $user_name ?></td>
          
          </tr>
          
         <?php
         }
      }
  
  

    ?>
   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
   </body>

<script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    console.log(icon);
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
    if(icC=="fa fa-circle-check"){
      setInterval(()=>{
        window.location.href="info_user.php"
       },4700)
        }else{
   }
}
  </script>
    <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
       // console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
          // console.log(id)
          $.ajax({url:'addon/remove_user.php',
          method:"POST",
          data:({name:id}),
          success:function(data){
            let c1=parseInt(data);
                if(id===c1){
              $("#deletmodle").removeClass("active");
              StudToast("8px solid rgb(247, 167, 22)","#f7a716","! انتبه "," لايمكن حذف المستخدم وهو متصل","fa-solid fa-circle-info");
            }else{
              StudToast("8px solid rgb(3, 188, 77)","rgb(3, 188, 77)"," ! تمت ","تم حذف المستخدم مع بياناته ","fa fa-circle-info")
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
        }
      }
    });
  });
}
    
    </script>


</html>