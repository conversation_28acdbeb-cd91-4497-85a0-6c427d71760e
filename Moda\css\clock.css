
.clock-dial{
    
    width: 250px;
    height: 250px;
    border: 10px solid#f77d7f;
    border-radius: 50%;
    margin: 0 auto;
    margin-top: 10px;
    position: relative;
    box-shadow: 5px 5px 10px grey;
    box-shadow: 0  0 10px inset gray;
    
}
.point{
    width: 20px;
    height: 20px;
    background-color: #090909;
    border-radius: 50%;
    position: absolute;
    top: 105px;
    left: 107px;
    box-shadow:  0 0 10px  gray;
}
.hour{
    font-size: 20px;
    font-weight: bold;
    color: #283149;
    position: absolute;
}
.hour-1{
    top: 20px;
    right: 60px;

}
.hour-2{
    top: 53px;
    right: 23px;
    
}
.hour-3{
    top:100px;
    right: 10px;
    
}
.hour-4{
    top: 150px;
    right: 22px;
    
}
.hour-5{
    top: 183px;
    right: 54px;
    
}
.hour-6{
    bottom: 1px;
    left: 110px;
    
}
.hour-7{
    top: 183px;
    left: 54px;
    
}
.hour-8{
    top: 150px;
    left: 22px;
    
}
.hour-9{
    top: 100px;
    left: 10px;
    
}
.hour-10{
    top: 53px;
    left: 23px;
}
.hour-11{
    top: 20px;
    left: 60px;
    
}

.hour-12{
    top: 2px;
    left: 110px;
    
}
.brand{
    font-size: 15px;
    font-weight: bold;
    color: #f77d7f;
    position: absolute;
    top: 45px;
    left: 30.5%;
    border: 1px solid;
    border-radius: 10px;
    padding:   1px;
    box-shadow: 0 0 10px black;
}
.minute-hand-warapper{
    width: 200px;
    height: 200px;
    position: absolute;
    top: 15px;
    left: 17px;
    border-radius: 50%;
   
}
.minute-hand{
    position: relative;
}
.minute-hand .hand{
    width: 4px;
    height: 90px;
    background-color: #000000;
    position: absolute;
    left:97px;
}




.hour-hand-warapper{
    width: 200px;
    height: 200px;
    position: absolute;
    top: 15px;
    left: 17px;
    border-radius: 50%;
   
}
.hour-hand{
    position: relative;
}
.hour-hand .hand{
    width: 4px;
    height:60px;
    background-color: #000000;
    position: absolute;
    top: 33px;
    left: 98.5px;
}
.hour-hand .arrow{
    font-size: 20PX;
    font-weight: bold;
    color: #000000;
    transform: rotate(180deg);
    position: absolute;
    left: 94.5px;
    top: 19px;
}


.sec-hand-warapper{
    width: 200px;
    height: 200px;
    position: absolute;
    top: 15px;
    left: 17px;
    border-radius: 50%;
   
}
.sec-hand{
    position: relative;
}
.sec-hand .hand{
    width: 2px;
    height: 95px;
    background-color: #000000;
    position: absolute;
    left: 98.5px
}