<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماذا عنا</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
   </head>
   <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
        direction: rtl;
    }

    .about-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 20px;
    }

    .developer-info {
        margin-bottom: 40px;
    }

    .developer-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .developer-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    }

    .developer-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .developer-avatar i {
        font-size: 50px;
        color: white;
    }

    .developer-name {
        color: #2c3e50;
        font-size: 28px;
        font-weight: bold;
        margin: 20px 0 10px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .developer-title {
        color: #7f8c8d;
        font-size: 18px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .description {
        color: #34495e;
        line-height: 1.8;
        font-size: 16px;
    }

    .description p {
        margin: 10px 0;
    }

    .contact-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .contact-title {
        color: #2c3e50;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .contact-info {
        margin-bottom: 30px;
    }

    .phone-number {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        font-size: 20px;
        color: #2c3e50;
        font-weight: bold;
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 15px 25px;
        border-radius: 15px;
        margin: 0 auto;
        max-width: 300px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .phone-number i {
        color: #667eea;
        font-size: 24px;
    }

    .social-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .contact-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px 30px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: bold;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        min-width: 150px;
        justify-content: center;
    }

    .call-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }

    .call-btn:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .whatsapp-btn {
        background: linear-gradient(45deg, #25D366, #128C7E);
        color: white;
    }

    .whatsapp-btn:hover {
        background: linear-gradient(45deg, #20b358, #0e7a6e);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(37, 211, 102, 0.4);
        color: white;
    }

    .contact-btn i {
        font-size: 20px;
    }

    /* تصميم قسم الأعمال البرمجية */
    .portfolio-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    .portfolio-title {
        text-align: center;
        color: #2c3e50;
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 3px solid #667eea;
        position: relative;
    }

    .portfolio-title::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    .portfolio-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-top: 30px;
    }

    .project-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(102, 126, 234, 0.1);
        position: relative;
        overflow: hidden;
    }

    .project-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .project-card:hover::before {
        left: 100%;
    }

    .project-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        border-color: #667eea;
    }

    .project-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        transition: all 0.3s ease;
    }

    .project-card:hover .project-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .project-icon i {
        font-size: 30px;
        color: white;
    }

    .project-card h4 {
        color: #2c3e50;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .project-card p {
        color: #6c757d;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
    }

    @media (max-width: 768px) {
        .about-container {
            margin: 20px;
            padding: 10px;
        }

        .developer-card, .contact-section, .portfolio-section {
            padding: 20px;
        }

        .portfolio-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .portfolio-title {
            font-size: 24px;
        }

        .project-card {
            padding: 20px;
        }

        .social-buttons {
            flex-direction: column;
            align-items: center;
        }

        .contact-btn {
            width: 100%;
            max-width: 250px;
        }
    }

    @media (max-width: 480px) {
        .portfolio-title {
            font-size: 20px;
        }

        .project-icon {
            width: 60px;
            height: 60px;
        }

        .project-icon i {
            font-size: 25px;
        }

        .project-card h4 {
            font-size: 16px;
        }

        .project-card p {
            font-size: 13px;
        }
    }
   </style>
   <body>
   <div class="about-container">
       <div class="developer-info">
           <div class="developer-card">
               <div class="developer-avatar">
                   <i class="fas fa-user-tie"></i>
               </div>
               <h2 class="developer-name">م.عبدالرحمن حسن الخفاجي</h2>
               <p class="developer-title">مطور ومصمم النظام</p>
               <div class="description">
                   <p>تم بناء وتصميم هذا البرنامج من قبل المبرمج</p>
                   <p>مبني البرنامج على متطلبات خاصة وبالإمكان التطوير على بيئة النظام حسب طلب المستخدم</p>
               </div>
           </div>
       </div>

       <!-- قسم الأعمال البرمجية -->
       <div class="portfolio-section">
           <h3 class="portfolio-title">
               <i class="fas fa-code"></i>
               🛠️ أعمالي البرمجية
           </h3>
           <div class="portfolio-grid">
               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-university"></i>
                   </div>
                   <h4>نظام إدارة متكامل</h4>
                   <p>جامعة المعقل الأهلية</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-building"></i>
                   </div>
                   <h4>موقع إلكتروني احترافي</h4>
                   <p>شركة فجر السماء للمقاولات العامة وخدمات التنظيف والإطعام</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-shopping-cart"></i>
                   </div>
                   <h4>تطبيق تسوّق إلكتروني</h4>
                   <p>مول بصرة تايم سكوير</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-truck"></i>
                   </div>
                   <h4>موقع إلكتروني تعريفي</h4>
                   <p>شركة أبناء البصرة للنقل العام والتجارة العامة</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-users"></i>
                   </div>
                   <h4>نظام موارد بشرية ومشتريات</h4>
                   <p>شركة الدغارة الخضراء للمقاولات</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-calculator"></i>
                   </div>
                   <h4>نظام محاسبي احترافي</h4>
                   <p>مجمع سبع سنابل الطبي</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-hospital"></i>
                   </div>
                   <h4>نظام إدارة مالي وطبي</h4>
                   <p>مستشفى الأميرات الأهلي</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-calendar-check"></i>
                   </div>
                   <h4>موقع ونظام حجز إلكتروني</h4>
                   <p>مركز العافية للعلاج الطبيعي</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-tasks"></i>
                   </div>
                   <h4>نظام متابعة المشاريع</h4>
                   <p>شركة النور الساطع للبناء</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-money-bill-wave"></i>
                   </div>
                   <h4>نظام إدارة رواتب</h4>
                   <p>شركة سما الجنوب</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-mobile-alt"></i>
                   </div>
                   <h4>نظام فواتير ومخزون</h4>
                   <p>محلات رويال فون للهواتف والإكسسوارات</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-child"></i>
                   </div>
                   <h4>تطبيق تعليمي</h4>
                   <p>روضة كيدز التعليمية</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-archive"></i>
                   </div>
                   <h4>نظام أرشفة إلكترونية</h4>
                   <p>شركة ضياء الفجر للخدمات العامة</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-laptop-code"></i>
                   </div>
                   <h4>موقع تعريفي وتطبيقي</h4>
                   <p>شركة الرافدين لتقنية المعلومات</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-graduation-cap"></i>
                   </div>
                   <h4>نظام تسجيل ومتابعة طلابي</h4>
                   <p>مدرسة النجوم الأهلية</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-cash-register"></i>
                   </div>
                   <h4>نظام نقاط بيع (POS)</h4>
                   <p>محلات قصر البصرة للألبسة</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-cut"></i>
                   </div>
                   <h4>تطبيق حجوزات صالونات</h4>
                   <p>صالون لمسة أنوثة</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-shipping-fast"></i>
                   </div>
                   <h4>نظام متابعة وإدارة شحنات</h4>
                   <p>شركة الصقر للشحن والنقل</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-globe"></i>
                   </div>
                   <h4>موقع تفاعلي</h4>
                   <p>معرض البصرة الدولي</p>
               </div>

               <div class="project-card">
                   <div class="project-icon">
                       <i class="fas fa-vote-yea"></i>
                   </div>
                   <h4>نظام إلكتروني للحملات</h4>
                   <p>متابعة الحملات الانتخابية</p>
               </div>
           </div>
       </div>

       <div class="contact-section">
           <h3 class="contact-title">يمكنك التواصل معنا</h3>
           <div class="contact-info">
               <div class="phone-number">
                   <i class="fas fa-phone"></i>
                   <span>07719992716</span>
               </div>
           </div>

           <div class="social-buttons">
               <a href="tel:07719992716" class="contact-btn call-btn">
                   <i class="fas fa-phone"></i>
                   <span>اتصال</span>
               </a>
               <a href="https://wa.me/9647719992716" class="contact-btn whatsapp-btn">
                   <i class="fab fa-whatsapp"></i>
                   <span>واتساب</span>
               </a>
           </div>
       </div>
   </div>
</body>
<script src="js/all.js" crossorigin="anonymous"></script>
</body>
</html>