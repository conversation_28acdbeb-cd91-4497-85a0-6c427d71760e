<?php
session_start();
include "addon/dbcon.php";

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $receipt_number = trim($_POST['receipt_number']);
    $phone_number = trim($_POST['phone_number']);

    if (!empty($receipt_number) && !empty($phone_number)) {
        try {
            // البحث عن الطالب باستخدام رقم الوصل ورقم الهاتف
            $sql = "SELECT s.*, sp.*, u.user_name
                    FROM stud_tb s
                    LEFT JOIN stud_pay sp ON s.id = sp.id_stud
                    LEFT JOIN users_tb u ON s.userID = u.id_user
                    WHERE (s.id_note = ? OR sp.id_pay = ?)
                    AND s.p_phone = ?";

            $stmt = mysqli_prepare($con, $sql);
            mysqli_stmt_bind_param($stmt, "sss", $receipt_number, $receipt_number, $phone_number);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if ($result && mysqli_num_rows($result) > 0) {
                $student = mysqli_fetch_assoc($result);

                // إنشاء كائن الطالب
                $student_obj = (object) [
                    'id' => $student['id'],
                    'name' => $student['name'],
                    'id_note' => $student['id_note'] ?? $student['id'],
                    'age' => $student['age'] ?? 'غير محدد',
                    'sex' => $student['sex'] ?? 'غير محدد',
                    'catg' => $student['catg'] ?? 'غير محدد',
                    'p_name' => $student['p_name'] ?? 'غير محدد',
                    'p_phone' => $student['p_phone'] ?? 'غير محدد',
                    'user_name' => $student['user_name'] ?? 'غير محدد',
                    'datein' => $student['datein'] ?? date('Y-m-d'),
                    'loc' => $student['loc'] ?? 'غير محدد',
                    'cash_stud' => $student['cash_stud'] ?? 0,
                    'date_exp' => $student['date_exp'] ?? null
                ];

                $_SESSION['student'] = $student_obj;
                $success_message = 'مرحباً ' . $student['name'] . '! جاري تحويلك للصفحة الرئيسية...';

                // إعادة توجيه بعد 2 ثانية
                header("refresh:2;url=index.php");
            } else {
                $error_message = 'عذراً! لم نتمكن من العثور على بياناتك. تأكد من رقم الوصل ورقم الهاتف';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
        }
    } else {
        $error_message = 'يرجى ملء رقم الوصل ورقم الهاتف';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🌟 روضة الأطفال - تسجيل الدخول</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/kids-login.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            overflow-x: hidden;
            direction: rtl;
            position: relative;
        }

        /* خلفية متحركة للأطفال */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23fff" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="%23fff" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="1" fill="%23fff" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/></circle></svg>') repeat;
            pointer-events: none;
            z-index: 1;
        }

        .container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 30px;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            border: 3px solid rgba(255, 255, 255, 0.8);
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 3;
        }

        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            animation: gradientShift 3s ease infinite, bounce 2s ease-in-out infinite;
            border: 4px solid rgba(255, 255, 255, 0.8);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .login-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1); }
            to { text-shadow: 2px 2px 8px rgba(255, 105, 180, 0.3); }
        }

        .login-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .alert {
            border-radius: 20px;
            padding: 1.2rem;
            margin-bottom: 1.5rem;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-weight: 600;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .alert-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
            color: white;
            box-shadow: 0 8px 25px rgba(81, 207, 102, 0.3);
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
        }

        .form-group label i {
            color: #ff6b6b;
            width: 24px;
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        .form-control {
            width: 100%;
            padding: 1.2rem;
            border: 3px solid #e9ecef;
            border-radius: 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            text-align: center;
        }

        .form-control:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 0.3rem rgba(255, 107, 107, 0.25);
            background: white;
            transform: scale(1.02);
        }

        .form-control::placeholder {
            color: #adb5bd;
            font-weight: 500;
        }

        .btn-login {
            width: 100%;
            padding: 1.5rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            background-size: 200% 200%;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1.3rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            animation: gradientShift 3s ease infinite;
            border: 3px solid rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-login:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.6);
            animation: gradientShift 1s ease infinite, wiggle 0.5s ease;
        }

        .btn-login:active {
            transform: translateY(0) scale(0.98);
        }

        @keyframes wiggle {
            0%, 7% { transform: rotateZ(0); }
            15% { transform: rotateZ(-15deg); }
            20% { transform: rotateZ(10deg); }
            25% { transform: rotateZ(-10deg); }
            30% { transform: rotateZ(6deg); }
            35% { transform: rotateZ(-4deg); }
            40%, 100% { transform: rotateZ(0); }
        }

        .welcome-slider {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
            border-radius: 20px;
            border: 2px dashed #ff6b6b;
            position: relative;
            overflow: hidden;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            opacity: 0;
            transition: opacity 0.8s ease-in-out;
            animation: slideIn 0.8s ease;
        }

        .slide.active {
            opacity: 1;
            animation: slideIn 0.8s ease;
        }

        .slide h2 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
            animation: textBounce 0.6s ease;
        }

        .slide p {
            color: #7f8c8d;
            font-size: 1rem;
            margin: 0;
            animation: textFadeIn 0.8s ease 0.2s both;
        }

        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        @keyframes textBounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes textFadeIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-emoji {
            position: absolute;
            font-size: 2rem;
            animation: float 6s ease-in-out infinite;
            opacity: 0.7;
        }

        .floating-emoji:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-emoji:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-emoji:nth-child(3) { top: 60%; left: 5%; animation-delay: 2s; }
        .floating-emoji:nth-child(4) { top: 70%; right: 10%; animation-delay: 3s; }
        .floating-emoji:nth-child(5) { top: 40%; left: 20%; animation-delay: 4s; }
        .floating-emoji:nth-child(6) { top: 80%; right: 25%; animation-delay: 5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(5deg); }
            50% { transform: translateY(-10px) rotate(-5deg); }
            75% { transform: translateY(-15px) rotate(3deg); }
        }

        /* استجابة للهواتف المحمولة */
        @media (max-width: 768px) {
            body {
                padding: 0.5rem;
            }

            .container {
                padding: 0.5rem;
            }

            .login-card {
                padding: 1.5rem;
                margin: 0.5rem;
                border-radius: 25px;
            }

            .login-header h1 {
                font-size: 1.6rem;
                line-height: 1.3;
            }

            .logo {
                width: 80px;
                height: 80px;
                font-size: 2rem;
            }

            .form-control {
                padding: 1rem;
                font-size: 1rem;
            }

            .btn-login {
                padding: 1.2rem;
                font-size: 1.1rem;
            }

            .floating-emoji {
                font-size: 1.5rem;
            }

            .welcome-slider {
                padding: 1rem;
                min-height: 100px;
            }

            .slide h2 {
                font-size: 1.3rem;
            }

            .slide p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .login-card {
                padding: 1rem;
                margin: 0.25rem;
            }

            .login-header h1 {
                font-size: 1.4rem;
                line-height: 1.2;
            }

            .logo {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }

            .form-control {
                padding: 0.9rem;
                font-size: 0.95rem;
            }

            .btn-login {
                padding: 1rem;
                font-size: 1rem;
            }

            .floating-emoji {
                font-size: 1.2rem;
            }

            .welcome-slider {
                padding: 0.8rem;
                min-height: 90px;
            }

            .slide h2 {
                font-size: 1.2rem;
            }

            .slide p {
                font-size: 0.85rem;
            }
        }

        /* تحسينات إضافية للشاشات الصغيرة جداً */
        @media (max-width: 360px) {
            .login-header h1 {
                font-size: 1.2rem;
            }

            .logo {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .welcome-slider {
                padding: 0.6rem;
                min-height: 80px;
            }

            .slide h2 {
                font-size: 1.1rem;
            }

            .slide p {
                font-size: 0.8rem;
            }

            .form-control {
                padding: 0.8rem;
                font-size: 0.9rem;
            }

            .btn-login {
                padding: 0.9rem;
                font-size: 0.95rem;
            }
        }

        /* تحسينات إضافية للأطفال */
        .fun-button {
            position: relative;
            overflow: hidden;
        }

        .fun-button::after {
            content: '✨';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            opacity: 0;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
    </style>
</head>
<body>
    <!-- عناصر متحركة للأطفال -->
    <div class="floating-elements">
        <div class="floating-emoji">🌟</div>
        <div class="floating-emoji">🎈</div>
        <div class="floating-emoji">🦋</div>
        <div class="floating-emoji">🌈</div>
        <div class="floating-emoji">🎨</div>
        <div class="floating-emoji">🧸</div>
    </div>

    <div class="container">
        <div class="login-card animate__animated animate__bounceIn">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-child"></i>
                </div>
                <h1>🌟 مرحباً بك في روضة الأطفال 🌟</h1>
                <p>مكان التعلم والمرح</p>
            </div>

            <!-- سلايدر الترحيب -->
            <div class="welcome-slider" id="welcomeSlider">
                <div class="slide active">
                    <h2>🎒 سجل دخولك للروضة</h2>
                    <p>أدخل رقم الوصل ورقم الهاتف للدخول</p>
                </div>
                <div class="slide">
                    <h2>🌟 مرحباً بك في عالم التعلم</h2>
                    <p>استكشف الأنشطة والألعاب التعليمية</p>
                </div>
                <div class="slide">
                    <h2>🎨 اكتشف مواهبك</h2>
                    <p>شارك في الأنشطة الفنية والإبداعية</p>
                </div>
                <div class="slide">
                    <h2>👫 اصنع صداقات جديدة</h2>
                    <p>تعرف على أصدقائك في الروضة</p>
                </div>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger animate__animated animate__shakeX">
                    <i class="fas fa-sad-tear"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success animate__animated animate__bounceIn">
                    <i class="fas fa-smile-beam"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="login-form" onsubmit="return validateForm()">
                <div class="form-group">
                    <label for="receipt_number">
                        <i class="fas fa-receipt"></i>
                        🎫 رقم الوصل
                    </label>
                    <input type="text" id="receipt_number" name="receipt_number" class="form-control"
                           placeholder="أدخل رقم الوصل الخاص بك" required>
                </div>

                <div class="form-group">
                    <label for="phone_number">
                        <i class="fas fa-phone"></i>
                        📱 رقم الهاتف
                    </label>
                    <input type="tel" id="phone_number" name="phone_number" class="form-control"
                           placeholder="أدخل رقم هاتف ولي الأمر" required>
                </div>

                <button type="submit" class="btn-login fun-button">
                    <i class="fas fa-rocket"></i>
                    🚀 دخول الروضة
                </button>
            </form>

            <div style="text-align: center; margin-top: 1.5rem; padding-top: 1rem; border-top: 2px dashed #ff6b6b;">
                <p style="color: #7f8c8d; font-size: 0.9rem; margin: 0;">
                    🎈 مرحباً بك في عالم التعلم والمرح 🎈
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية ممتعة للأطفال
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الدخول
            const loginCard = document.querySelector('.login-card');
            if (loginCard) {
                setTimeout(() => {
                    loginCard.classList.add('animate__animated', 'animate__bounceIn');
                }, 200);
            }

            // تأثيرات تفاعلية للحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 0 20px rgba(255, 107, 107, 0.4)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });

                // تأثير الكتابة
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = '#51cf66';
                        this.style.backgroundColor = '#f8fff8';
                    } else {
                        this.style.borderColor = '#e9ecef';
                        this.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                    }
                });
            });

            // تأثير الزر للكمبيوتر والهاتف
            const submitBtn = document.querySelector('.btn-login');
            if (submitBtn) {
                // تأثيرات الماوس للكمبيوتر
                submitBtn.addEventListener('mouseenter', function() {
                    if (!isMobileDevice()) {
                        this.style.animation = 'wiggle 0.5s ease';
                    }
                });

                // تأثيرات اللمس للهاتف
                submitBtn.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.95)';
                    this.style.boxShadow = '0 5px 15px rgba(255, 107, 107, 0.8)';
                });

                submitBtn.addEventListener('touchend', function(e) {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 8px 25px rgba(255, 107, 107, 0.4)';
                });

                submitBtn.addEventListener('click', function(e) {
                    // تأثير النقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);

                    // إضافة تأثير التحميل
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الدخول...';
                    this.disabled = true;

                    // إعادة تعيين النص إذا فشل التحقق
                    setTimeout(() => {
                        if (!validateForm()) {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }
                    }, 100);
                });
            }

            // تحسينات للهواتف المحمولة
            if (isMobileDevice()) {
                // منع التكبير عند التركيز على الحقول
                const inputs = document.querySelectorAll('input');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    });

                    input.addEventListener('blur', function() {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no');
                    });
                });
            }
        });

        // فحص إذا كان الجهاز هاتف محمول
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
        }

            // تأثيرات الخلفية المتحركة
            createFloatingShapes();

            // تشغيل السلايدر
            startWelcomeSlider();
        });

        // وظيفة السلايدر
        function startWelcomeSlider() {
            const slides = document.querySelectorAll('.slide');
            let currentSlide = 0;

            function showNextSlide() {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % slides.length;
                slides[currentSlide].classList.add('active');
            }

            // تغيير الشريحة كل 4 ثوان
            setInterval(showNextSlide, 4000);
        }

        // التحقق من صحة النموذج
        function validateForm() {
            const receiptNumber = document.getElementById('receipt_number').value.trim();
            const phoneNumber = document.getElementById('phone_number').value.trim();

            if (!receiptNumber || !phoneNumber) {
                showAlert('🤔 يرجى ملء جميع الحقول المطلوبة', 'warning');
                return false;
            }

            if (phoneNumber.length < 10) {
                showAlert('📱 يرجى إدخال رقم هاتف صحيح', 'warning');
                return false;
            }

            return true;
        }

        // عرض التنبيهات بطريقة ممتعة
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'warning' ? 'danger' : 'success'} animate__animated animate__bounceIn`;
            alertDiv.innerHTML = `<i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 'check-circle'}"></i> ${message}`;

            const form = document.querySelector('.login-form');
            form.parentNode.insertBefore(alertDiv, form);

            setTimeout(() => {
                alertDiv.classList.add('animate__animated', 'animate__fadeOut');
                setTimeout(() => {
                    alertDiv.remove();
                }, 500);
            }, 3000);
        }

        // إنشاء أشكال متحركة في الخلفية
        function createFloatingShapes() {
            const shapes = ['⭐', '🌟', '✨', '💫', '🎈', '🎨', '🦋', '🌈'];

            setInterval(() => {
                const shape = document.createElement('div');
                shape.textContent = shapes[Math.floor(Math.random() * shapes.length)];
                shape.style.cssText = `
                    position: fixed;
                    font-size: ${Math.random() * 20 + 15}px;
                    left: ${Math.random() * 100}vw;
                    top: 100vh;
                    pointer-events: none;
                    z-index: 1;
                    animation: floatUp 4s linear forwards;
                    opacity: 0.7;
                `;

                document.body.appendChild(shape);

                setTimeout(() => {
                    shape.remove();
                }, 4000);
            }, 2000);
        }

        // إضافة CSS للتأثيرات الإضافية
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 0.7;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .form-control:valid {
                border-color: #51cf66 !important;
                background-color: #f8fff8 !important;
            }

            .form-control:invalid:not(:placeholder-shown) {
                border-color: #ff6b6b !important;
                background-color: #fff8f8 !important;
            }
        `;
        document.head.appendChild(style);

    </script>
</body>
</html>
