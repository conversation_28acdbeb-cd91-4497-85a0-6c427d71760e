<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول الأخبار
$create_news = "CREATE TABLE IF NOT EXISTS news_announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(100) DEFAULT 'إعلان',
    status VARCHAR(50) DEFAULT 'نشط',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_news);

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_news':
            $title = mysqli_real_escape_string($con, $_POST['title'] ?? '');
            $content = mysqli_real_escape_string($con, $_POST['content'] ?? '');
            $type = mysqli_real_escape_string($con, $_POST['type'] ?? 'إعلان');
            if (!empty($title) && !empty($content)) {
                $sql = "INSERT INTO news_announcements (title, content, type, status, created_date) 
                        VALUES ('$title', '$content', '$type', 'نشط', NOW())";
                if (@mysqli_query($con, $sql)) {
                    $message = 'تم إضافة الخبر بنجاح!';
                } else {
                    $message = 'حدث خطأ أثناء إضافة الخبر!';
                    $messageType = 'error';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة!';
                $messageType = 'error';
            }
            break;
            
        case 'delete_news':
            $id = (int)($_POST['id'] ?? 0);
            if ($id > 0) {
                if (@mysqli_query($con, "DELETE FROM news_announcements WHERE id = $id")) {
                    $message = 'تم حذف الخبر بنجاح!';
                } else {
                    $message = 'حدث خطأ أثناء حذف الخبر!';
                    $messageType = 'error';
                }
            }
            break;
    }
}

// جلب الأخبار
$news = @mysqli_query($con, "SELECT * FROM news_announcements ORDER BY created_date DESC");
if (!$news) $news = false;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📰 إدارة الأخبار - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container-fluid {
            max-width: 1400px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .content-card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .btn-primary-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-danger-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
            color: white;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .table th {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            font-weight: bold;
            border: none;
            padding: 1rem;
            color: #2c3e50;
        }

        .table td {
            padding: 1rem;
            border: none;
            border-bottom: 1px solid #f1f2f6;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .alert-error-custom {
            background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border-right: 5px solid #e74c3c;
        }

        .badge-custom {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .header-card {
                padding: 1.5rem;
            }

            .content-card-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <!-- Header -->
        <div class="header-card">
            <h1>📰 إدارة الأخبار</h1>
            <p style="color: #7f8c8d; margin: 0;">إضافة وحذف وتعديل الأخبار والإعلانات المهمة</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom <?php echo $messageType == 'success' ? 'alert-success-custom' : 'alert-error-custom'; ?>">
                <i class="fas <?php echo $messageType == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Add News Form -->
            <div class="col-md-4">
                <div class="content-card">
                    <div class="content-card-header">
                        <i class="fas fa-plus"></i> إضافة خبر جديد
                    </div>
                    <div class="content-card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_news">
                            <div class="mb-3">
                                <label class="form-label">عنوان الخبر:</label>
                                <input type="text" name="title" class="form-control" required placeholder="اكتب عنوان الخبر...">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">نوع الخبر:</label>
                                <select name="type" class="form-select">
                                    <option value="إعلان">إعلان</option>
                                    <option value="خبر">خبر</option>
                                    <option value="تنبيه">تنبيه</option>
                                    <option value="فعالية">فعالية</option>
                                    <option value="إنجاز">إنجاز</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">محتوى الخبر:</label>
                                <textarea name="content" class="form-control" rows="6" required placeholder="اكتب محتوى الخبر بالتفصيل..."></textarea>
                            </div>
                            <button type="submit" class="btn-primary-custom w-100">
                                <i class="fas fa-plus"></i> إضافة الخبر
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- News List -->
            <div class="col-md-8">
                <div class="content-card">
                    <div class="content-card-header">
                        <i class="fas fa-list"></i> قائمة الأخبار
                    </div>
                    <div class="content-card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>النوع</th>
                                        <th>المحتوى</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($news && mysqli_num_rows($news) > 0): ?>
                                        <?php while ($row = mysqli_fetch_assoc($news)): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($row['title']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge-custom" style="background: #3498db; color: white;">
                                                        <?php echo htmlspecialchars($row['type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo substr(htmlspecialchars($row['content']), 0, 80) . '...'; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('Y-m-d H:i', strtotime($row['created_date'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الخبر؟')">
                                                        <input type="hidden" name="action" value="delete_news">
                                                        <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                                        <button type="submit" class="btn-danger-custom">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center" style="padding: 3rem;">
                                                <i class="fas fa-newspaper" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                                                <h5 style="color: #7f8c8d;">لا توجد أخبار حتى الآن</h5>
                                                <p style="color: #95a5a6;">ابدأ بإضافة أول خبر باستخدام النموذج على اليسار</p>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // Form submission loading state
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.classList.contains('btn-danger-custom')) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                    submitBtn.disabled = true;
                }
            });
        });
    </script>
</body>
</html>
