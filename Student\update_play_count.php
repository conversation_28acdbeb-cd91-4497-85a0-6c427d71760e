<?php
session_start();

// التحقق من تسجيل دخول الطالب
if (!isset($_SESSION['student'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

include "addon/dbcon.php";

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['game_id']) || !is_numeric($input['game_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف اللعبة مطلوب']);
    exit();
}

$game_id = intval($input['game_id']);

// تحديث عداد اللعب
$update_query = "UPDATE app_games SET play_count = play_count + 1 WHERE id = $game_id";

if (mysqli_query($con, $update_query)) {
    // جلب العدد المحدث
    $count_query = "SELECT play_count FROM app_games WHERE id = $game_id";
    $count_result = mysqli_query($con, $count_query);
    
    if ($count_result && mysqli_num_rows($count_result) > 0) {
        $count_row = mysqli_fetch_assoc($count_result);
        echo json_encode([
            'success' => true,
            'play_count' => $count_row['play_count']
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'play_count' => 1
        ]);
    }
} else {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في قاعدة البيانات']);
}
?>
