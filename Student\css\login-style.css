/* تصميم صفحة تسجيل الدخول للطلاب */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    direction: rtl;
    overflow: hidden;
}

.login-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.6s ease;
}

.login-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(45deg, #3498db, #2c3e50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
}

.login-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: bold;
}

.login-header p {
    color: #7f8c8d;
    font-size: 1rem;
}

.alert {
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-danger {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-group label i {
    color: #3498db;
    width: 20px;
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: white;
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.5rem;
    display: block;
}

.btn-login {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(45deg, #3498db, #2c3e50);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4);
    background: linear-gradient(45deg, #2980b9, #34495e);
}

.btn-login:active {
    transform: translateY(0);
}

.login-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.login-footer p {
    color: #7f8c8d;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.login-footer a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.login-footer a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* الأشكال المتحركة في الخلفية */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* تصميم متجاوب للهاتف */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
        max-width: 350px;
    }
    
    .login-card {
        padding: 2rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .logo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .form-control {
        padding: 0.8rem;
    }
    
    .btn-login {
        padding: 0.8rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 0.5rem;
        max-width: 320px;
    }
    
    .login-card {
        padding: 1.5rem;
    }
    
    .floating-shape {
        display: none;
    }
}

/* تأثيرات إضافية */
.form-control::placeholder {
    color: #adb5bd;
    opacity: 1;
}

.form-group {
    position: relative;
}

.form-group::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #3498db, #2c3e50);
    transition: width 0.3s ease;
}

.form-group:focus-within::after {
    width: 100%;
}

/* تحسينات للوصولية */
.form-control:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

.btn-login:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}
