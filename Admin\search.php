<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث عن طالب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>

   </head>
   <body>

  <div class="search">
  <section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
    <input type="text" id="live_search" placeholder="يمكنك البحث عن معلومات الطالب هنا">
    <button class="btn btn-secondary"  ><a href="infost.php" >اظهار حسب المستخدم</a></button>
    
  </div>
  <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p id="titlecash">CASH INFO </p>
                <p id="text">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
  <table class="table">
  <thead>
    <tr>
      <th scope="col">العمليات </th>
      <th scope="col">حاله الاشتراك </th>
      <th scope="col">مستخدم الحضانة </th>
      <th scope="col">تاريخ النفاذ</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ التسجيل</th>
      <th scope="col">السكن</th>
      <th scope="col">  رقم ولي الامر</th>
      <th scope="col"> اسم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
      <th scope="col">اسم  الطالب</th>
      <th scope="col">رقم   الوصل </th>
      
    </tr>
  </thead>
  <tbody id="tb">
  </tbody>
 
   </body>
   <script >
    let x;
    let toast = document.getElementById("toast2"),
    text=document.getElementById("text"),
    titlecash=document.getElementById("titlecash"),
    icon=document.getElementById("icon")
    function nodata(){
    clearTimeout(x);
    titlecash.innerText=" لاتوجد بيانات "
    icon.className="fa-solid fa-money-bills";
    text.style.fontSize="18px"
    text.innerText="لاتوجد معلومات بهذا الوصف";
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
        toast.style.transform = "translateX(-500px)"
    }, 5000);
}
</script>
   <script>
      $(document).ready(function () {
        $("#live_search").keydown(function(){
          var input = $(this).val();
          
          if(input != ""){
            $.ajax({
              method: "POST",
              url: "addon/searchF.php",
              data: {input:input},
              success:function (data) {
                $("#tb").html(data);
              }
            });

          }else{
          
          }
        })
      });
    </script>
     <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        //console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           //console.log(id)
          $.ajax({url:'addon/reomves.php',
          method:"POST",
          data:({removeid:id}),
          success:function(response){
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

      }
    });
        });
      }
    
    </script>
</html>