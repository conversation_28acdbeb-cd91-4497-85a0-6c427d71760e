<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=UTF-8');

// التحقق من وجود ملف قاعدة البيانات
if (!file_exists("addon/dbcon.php")) {
    die("ملف قاعدة البيانات غير موجود");
}

include "addon/dbcon.php";

// التحقق من الاتصال بقاعدة البيانات
if (!$con) {
    die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول الإعدادات بطريقة آمنة
$create_settings_table = "CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key (setting_key)
) DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

$result = @mysqli_query($con, $create_settings_table);
if (!$result) {
    // إذا فشل إنشاء الجدول، نتجاهل الخطأ ونكمل
}

// إنشاء جدول معلومات التواصل
$create_contact_table = "CREATE TABLE IF NOT EXISTS contact_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    info_key VARCHAR(100) NOT NULL,
    info_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_info_key (info_key)
) DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

$result = @mysqli_query($con, $create_contact_table);

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action == 'update_app_settings') {
        $app_name = isset($_POST['app_name']) ? mysqli_real_escape_string($con, $_POST['app_name']) : 'أكاديمية كيدز';
        $app_description = isset($_POST['app_description']) ? mysqli_real_escape_string($con, $_POST['app_description']) : 'تطبيق تعليمي';
        $app_version = isset($_POST['app_version']) ? mysqli_real_escape_string($con, $_POST['app_version']) : '1.0.0';
        $primary_color = isset($_POST['primary_color']) ? mysqli_real_escape_string($con, $_POST['primary_color']) : '#ff6b6b';
        $secondary_color = isset($_POST['secondary_color']) ? mysqli_real_escape_string($con, $_POST['secondary_color']) : '#4ecdc4';
        $header_gradient = isset($_POST['header_gradient']) ? mysqli_real_escape_string($con, $_POST['header_gradient']) : 'linear-gradient(45deg, #ff6b6b, #4ecdc4)';
        $background_gradient = isset($_POST['background_gradient']) ? mysqli_real_escape_string($con, $_POST['background_gradient']) : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;
        $notifications_enabled = isset($_POST['notifications_enabled']) ? 1 : 0;
        $games_enabled = isset($_POST['games_enabled']) ? 1 : 0;
        $activities_enabled = isset($_POST['activities_enabled']) ? 1 : 0;
        $news_enabled = isset($_POST['news_enabled']) ? 1 : 0;
        $contact_enabled = isset($_POST['contact_enabled']) ? 1 : 0;
        
        $settings = array(
            'app_name' => $app_name,
            'app_description' => $app_description,
            'app_version' => $app_version,
            'primary_color' => $primary_color,
            'secondary_color' => $secondary_color,
            'header_gradient' => $header_gradient,
            'background_gradient' => $background_gradient,
            'maintenance_mode' => $maintenance_mode,
            'notifications_enabled' => $notifications_enabled,
            'games_enabled' => $games_enabled,
            'activities_enabled' => $activities_enabled,
            'news_enabled' => $news_enabled,
            'contact_enabled' => $contact_enabled
        );
        
        $success = true;
        foreach ($settings as $key => $value) {
            $check_sql = "SELECT id FROM app_settings WHERE setting_key = '$key'";
            $check_result = @mysqli_query($con, $check_sql);
            
            if ($check_result && mysqli_num_rows($check_result) > 0) {
                $update_sql = "UPDATE app_settings SET setting_value = '$value' WHERE setting_key = '$key'";
                $update_result = @mysqli_query($con, $update_sql);
            } else {
                $insert_sql = "INSERT INTO app_settings (setting_key, setting_value) VALUES ('$key', '$value')";
                $insert_result = @mysqli_query($con, $insert_sql);
            }
        }
        
        $message = 'تم حفظ الإعدادات بنجاح!';
    }
}

// جلب الإعدادات الحالية
$current_settings = array(
    'app_name' => 'أكاديمية كيدز',
    'app_description' => 'تطبيق تعليمي متميز للأطفال',
    'app_version' => '1.0.0',
    'primary_color' => '#ff6b6b',
    'secondary_color' => '#4ecdc4',
    'header_gradient' => 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
    'background_gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'maintenance_mode' => '0',
    'notifications_enabled' => '1',
    'games_enabled' => '1',
    'activities_enabled' => '1',
    'news_enabled' => '1',
    'contact_enabled' => '1'
);

$settings_result = @mysqli_query($con, "SELECT setting_key, setting_value FROM app_settings");
if ($settings_result) {
    while ($row = mysqli_fetch_assoc($settings_result)) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إعدادات التطبيق - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-weight: bold;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            background: #218838;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            text-decoration: none;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: #7f8c8d;
            margin: 0;
        }

        .settings-card {
            background: white;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .settings-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .settings-card-body {
            padding: 2rem;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 2rem;
            font-weight: 600;
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 4px solid #27ae60;
        }

        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid #ddd;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .home-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 1rem;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <?php 
    if (file_exists('includes/sidebar.php')) {
        include 'includes/sidebar.php'; 
    }
    ?>

    <!-- Home Button -->
    <a href="home.php" class="home-btn">
        <i class="fas fa-home"></i>
        الصفحة الرئيسية
    </a>

    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>⚙️ إعدادات التطبيق</h1>
            <p>إدارة وتخصيص إعدادات تطبيق أكاديمية كيدز</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- App Settings -->
        <div class="settings-card">
            <div class="settings-card-header">
                <i class="fas fa-mobile-alt"></i> إعدادات التطبيق الأساسية
            </div>
            <div class="settings-card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_app_settings">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم التطبيق:</label>
                            <input type="text" name="app_name" class="form-control" value="<?php echo htmlspecialchars($current_settings['app_name']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">إصدار التطبيق:</label>
                            <input type="text" name="app_version" class="form-control" value="<?php echo htmlspecialchars($current_settings['app_version']); ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف التطبيق:</label>
                        <textarea name="app_description" class="form-control" rows="3" required><?php echo htmlspecialchars($current_settings['app_description']); ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اللون الأساسي:</label>
                            <div class="d-flex align-items-center">
                                <input type="color" name="primary_color" class="form-control" value="<?php echo $current_settings['primary_color']; ?>" style="width: 60px; height: 40px;">
                                <span class="color-preview" style="background-color: <?php echo $current_settings['primary_color']; ?>;"></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اللون الثانوي:</label>
                            <div class="d-flex align-items-center">
                                <input type="color" name="secondary_color" class="form-control" value="<?php echo $current_settings['secondary_color']; ?>" style="width: 60px; height: 40px;">
                                <span class="color-preview" style="background-color: <?php echo $current_settings['secondary_color']; ?>;"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تدرج لون الهيدر:</label>
                            <input type="text" name="header_gradient" class="form-control" value="<?php echo htmlspecialchars($current_settings['header_gradient']); ?>" required>
                            <small class="text-muted">مثال: linear-gradient(45deg, #ff6b6b, #4ecdc4)</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تدرج لون الخلفية:</label>
                            <input type="text" name="background_gradient" class="form-control" value="<?php echo htmlspecialchars($current_settings['background_gradient']); ?>" required>
                            <small class="text-muted">مثال: linear-gradient(135deg, #667eea 0%, #764ba2 100%)</small>
                        </div>
                    </div>

                    <h5 class="mb-3 mt-4">تفعيل/تعطيل الميزات:</h5>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenance_mode" <?php echo $current_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenance_mode">
                                    وضع الصيانة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="notifications_enabled" id="notifications_enabled" <?php echo $current_settings['notifications_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="notifications_enabled">
                                    تفعيل الإشعارات
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="games_enabled" id="games_enabled" <?php echo $current_settings['games_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="games_enabled">
                                    تفعيل الألعاب
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="activities_enabled" id="activities_enabled" <?php echo $current_settings['activities_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="activities_enabled">
                                    تفعيل الأنشطة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="news_enabled" id="news_enabled" <?php echo $current_settings['news_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="news_enabled">
                                    تفعيل الأخبار
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="contact_enabled" id="contact_enabled" <?php echo $current_settings['contact_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="contact_enabled">
                                    تفعيل التواصل
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-primary-custom">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>

                    <a href="../Student/index.php" target="_blank" class="btn btn-success" style="margin-right: 10px;">
                        <i class="fas fa-eye"></i> معاينة صفحة الطالب
                    </a>
                </form>
            </div>
        </div>

        <!-- Settings Preview -->
        <div class="settings-card">
            <div class="settings-card-header">
                <i class="fas fa-eye"></i> معاينة الإعدادات الحالية
            </div>
            <div class="settings-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-mobile-alt"></i> معلومات التطبيق:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الاسم:</strong> <?php echo htmlspecialchars($current_settings['app_name']); ?></li>
                            <li><strong>الإصدار:</strong> <?php echo htmlspecialchars($current_settings['app_version']); ?></li>
                            <li><strong>الوصف:</strong> <?php echo htmlspecialchars(substr($current_settings['app_description'], 0, 50)) . '...'; ?></li>
                            <li><strong>وضع الصيانة:</strong> <?php echo $current_settings['maintenance_mode'] ? '🔧 مفعل' : '✅ معطل'; ?></li>
                            <li><strong>الإشعارات:</strong> <?php echo $current_settings['notifications_enabled'] ? '🔔 مفعل' : '🔕 معطل'; ?></li>
                            <li><strong>الألعاب:</strong> <?php echo $current_settings['games_enabled'] ? '🎮 مفعل' : '❌ معطل'; ?></li>
                            <li><strong>الأنشطة:</strong> <?php echo $current_settings['activities_enabled'] ? '🎨 مفعل' : '❌ معطل'; ?></li>
                            <li><strong>الأخبار:</strong> <?php echo $current_settings['news_enabled'] ? '📰 مفعل' : '❌ معطل'; ?></li>
                            <li><strong>التواصل:</strong> <?php echo $current_settings['contact_enabled'] ? '📞 مفعل' : '❌ معطل'; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-palette"></i> الألوان:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span class="color-preview" style="background-color: <?php echo $current_settings['primary_color']; ?>;"></span>
                            <span>اللون الأساسي: <?php echo $current_settings['primary_color']; ?></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <span class="color-preview" style="background-color: <?php echo $current_settings['secondary_color']; ?>;"></span>
                            <span>اللون الثانوي: <?php echo $current_settings['secondary_color']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="settings-card">
            <div class="settings-card-header">
                <i class="fas fa-link"></i> روابط مفيدة للاختبار
            </div>
            <div class="settings-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-test-tube"></i> اختبار النظام:</h6>
                        <ul class="list-unstyled">
                            <li><a href="test_real_settings.php" target="_blank" class="btn btn-outline-primary btn-sm mb-2">
                                <i class="fas fa-vial"></i> اختبار النظام الكامل
                            </a></li>
                            <li><a href="../Student/get_app_settings.php" target="_blank" class="btn btn-outline-info btn-sm mb-2">
                                <i class="fas fa-code"></i> API الإعدادات
                            </a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-eye"></i> معاينة التطبيق:</h6>
                        <ul class="list-unstyled">
                            <li><a href="../Student/index.php" target="_blank" class="btn btn-outline-success btn-sm mb-2">
                                <i class="fas fa-external-link-alt"></i> صفحة الطالب
                            </a></li>
                            <li><a href="../Student/login.php" target="_blank" class="btn btn-outline-warning btn-sm mb-2">
                                <i class="fas fa-sign-in-alt"></i> صفحة تسجيل الدخول
                            </a></li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> جميع التغييرات تطبق فوراً على صفحة الطالب. لا حاجة لإعادة تحميل الخادم!
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // Color preview update
        document.querySelectorAll('input[type="color"]').forEach(input => {
            input.addEventListener('change', function() {
                const preview = this.parentElement.querySelector('.color-preview');
                if (preview) {
                    preview.style.backgroundColor = this.value;
                }
            });
        });
    </script>
</body>
</html>
