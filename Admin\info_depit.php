<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المصروفات  </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php";
      include "addon/dbcon.php"?>

      
   </head>
    <body>
       <div class="search">
      <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0">اختر احد المستخدمين</option>
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>   
        </select>
  <button class="btn btn-success text-light" name="sub"><a href="../Admin/addon/export_depit.php" style="color: white; text-decoration: none;">تحميل اكسل</a></button>
  <button class="btn btn-primary text-light" onclick="printDepitTable()">طباعة</button>
  <button class="btn btn-info text-light" name="sub"><a href="searchDepit.php" style="color: white; text-decoration: none;">بحث</a></button>
  <button class="btn btn-warning text-dark" name="finde"><a href="add_depit.php" style="color: #212529; text-decoration: none;">اضافة مصروف</a></button>
    
    
        </div>
  

    <table class="table">
  <thead>
    <tr>
    <th scope="col">العمليات</th>
      <th scope="col">المستخدم</th>
      <th scope="col"> تاريخ عملية المصروفات</th>
      <th scope="col">قيمة المصروفات</th>
      <th scope="col">وصف المصروفات</th>
      
    </tr>
  </thead>
  <tbody id="myTable">
 
  </tbody>
</table>
<script>
    $("#selc").change(function(){
      var selc = $(this).val();
      console.log("المستخدم المختار:", selc);

      if(selc == "0" || selc == "") {
        $("#myTable").html("<tr><td colspan='5' style='text-align: center; padding: 30px; color: #6c757d;'>يرجى اختيار مستخدم لعرض المصاريف</td></tr>");
        return;
      }

      $.ajax({
        method: "POST",
        url: "addon/info_depitF.php",
        data: {id: selc},
        beforeSend: function() {
          $("#myTable").html("<tr><td colspan='5' style='text-align: center; padding: 30px;'><i class='fas fa-spinner fa-spin'></i> جاري تحميل البيانات...</td></tr>");
        },
        success: function (data) {
          console.log("تم تحميل البيانات بنجاح:", data.substring(0, 100));
          $("#myTable").html(data);
        },
        error: function(xhr, status, error) {
          console.error("خطأ في تحميل البيانات:", error);
          console.error("تفاصيل الخطأ:", xhr.responseText);
          $("#myTable").html("<tr><td colspan='5' style='text-align: center; color: red; padding: 30px;'>خطأ في تحميل البيانات: " + error + "</td></tr>");
        }
      });
    });
</script>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>

</body>
    <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           console.log(id)
          $.ajax({url:'addon/removedepit.php',
          method:"POST",
          data:({name:id}),
          success:function(response){
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

      }
    });
        });
      }
    
    </script>

<script>
function printDepitTable(){
    // التحقق من وجود بيانات في الجدول
    var tableRows = document.querySelectorAll('#myTable tr');
    if (tableRows.length === 0 || tableRows[0].textContent.includes('يرجى اختيار مستخدم')) {
        alert('يرجى اختيار مستخدم أولاً لعرض البيانات');
        return;
    }

    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول وإزالة عمود العمليات
    var table = document.getElementById('myTable').cloneNode(true);

    // إنشاء جدول جديد للطباعة
    var printTable = document.createElement('table');
    printTable.className = 'table';

    // إضافة رأس الجدول
    var thead = document.createElement('thead');
    var headerRow = document.createElement('tr');
    var headers = ['المستخدم', 'تاريخ عملية المصروفات', 'قيمة المصروفات', 'وصف المصروفات'];
    headers.forEach(function(headerText) {
        var th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    printTable.appendChild(thead);

    // إضافة بيانات الجدول (تجاهل عمود العمليات)
    var tbody = document.createElement('tbody');
    var rows = table.querySelectorAll('tr');
    rows.forEach(function(row) {
        var cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            var newRow = document.createElement('tr');
            // تجاهل العمود الأول (العمليات) وأخذ باقي الأعمدة
            for (var i = 1; i < cells.length; i++) {
                var newCell = document.createElement('td');
                newCell.textContent = cells[i].textContent;
                newRow.appendChild(newCell);
            }
            tbody.appendChild(newRow);
        }
    });
    printTable.appendChild(tbody);

    var tableContent = printTable.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = '<!DOCTYPE html>' +
    '<html dir="rtl" lang="ar">' +
    '<head>' +
        '<meta charset="UTF-8">' +
        '<title>تقرير المصروفات</title>' +
        '<style>' +
            'body {' +
                'font-family: Arial, sans-serif;' +
                'direction: rtl;' +
                'margin: 20px;' +
                'background: white;' +
            '}' +
            '.print-header {' +
                'text-align: center;' +
                'margin-bottom: 30px;' +
                'border-bottom: 2px solid #333;' +
                'padding-bottom: 20px;' +
            '}' +
            '.print-header h1 {' +
                'color: #333;' +
                'margin-bottom: 10px;' +
                'font-size: 24px;' +
            '}' +
            '.print-header p {' +
                'color: #666;' +
                'margin: 5px 0;' +
                'font-size: 14px;' +
            '}' +
            'table {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin: 20px 0;' +
                'font-size: 12px;' +
            '}' +
            'table th, table td {' +
                'border: 1px solid #333;' +
                'padding: 8px;' +
                'text-align: center;' +
            '}' +
            'table thead th {' +
                'background-color: #333;' +
                'color: white;' +
                'font-weight: bold;' +
            '}' +
            '.print-footer {' +
                'margin-top: 30px;' +
                'text-align: center;' +
                'font-size: 12px;' +
                'color: #666;' +
                'border-top: 1px solid #ccc;' +
                'padding-top: 10px;' +
            '}' +
        '</style>' +
    '</head>' +
    '<body>' +
        '<div class="print-header">' +
            '<h1>تقرير المصروفات</h1>' +
            '<p>تاريخ التقرير: ' + new Date().toLocaleDateString('ar-EG') + '</p>' +
            '<p>وقت الطباعة: ' + new Date().toLocaleTimeString('ar-EG') + '</p>' +
        '</div>' +
        tableContent +
        '<div class="print-footer">' +
            '<p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>' +
        '</div>' +
    '</body>' +
    '</html>';

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    setTimeout(function() {
        printWindow.print();
        printWindow.close();
    }, 500);
}
</script>

</body>
</html>