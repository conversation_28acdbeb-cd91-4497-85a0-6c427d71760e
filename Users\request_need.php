<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

$message = '';
$messageType = '';

if(isset($_POST['submit_need'])){
    try {
        $user_id = $_SESSION['user']->id_user;
        $need_name = trim($_POST['need_name']);
        $need_type = $_POST['need_type'];
        $need_details = trim($_POST['need_details']);
        $request_date = date('Y-m-d');
        
        if(empty($need_name) || empty($need_details)) {
            $message = "يرجى ملء جميع الحقول المطلوبة";
            $messageType = 'error';
        } else {
            $stmt = $con->prepare("INSERT INTO needs_requests (user_id, need_name, request_date, need_type, need_details) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("issss", $user_id, $need_name, $request_date, $need_type, $need_details);
            
            if($stmt->execute()) {
                $message = "تم إرسال طلب الاحتياج بنجاح";
                $messageType = 'success';

                // الحصول على معرف الطلب الجديد
                $need_id = $con->insert_id;

                // إرسال إشعار للإدارة والمدقق باستخدام النظام الجديد
                include "../Admin/add_notification.php";
                addNeedRequestNotification($con, $_SESSION['user']->user_name, $need_type, $need_id);
            } else {
                $message = "حدث خطأ أثناء إرسال الطلب";
                $messageType = 'error';
            }
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب احتياج</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .request-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .request-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .form-select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }
        
        .back-btn {
            display: inline-block;
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: linear-gradient(45deg, #5a6268, #3d4043);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="request-container">
        <a href="home.php" class="back-btn">
            <i class="fas fa-arrow-right"></i> العودة للرئيسية
        </a>
        
        <div class="request-card">
            <h2 class="card-title">
                <i class="fas fa-clipboard-list"></i>
                طلب احتياج جديد
            </h2>
            
            <?php if($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">اسم الاحتياج *</label>
                    <input type="text" name="need_name" class="form-control" placeholder="أدخل اسم الاحتياج" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">نوع الاحتياج *</label>
                    <select name="need_type" class="form-select" required>
                        <option value="">اختر نوع الاحتياج</option>
                        <option value="احتياج">احتياج</option>
                        <option value="صيانة">صيانة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">تفاصيل الاحتياج *</label>
                    <textarea name="need_details" class="form-control" rows="5" placeholder="اكتب تفاصيل الاحتياج بوضوح..." required></textarea>
                </div>
                
                <button type="submit" name="submit_need" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الطلب
                </button>
            </form>
        </div>
    </div>
</body>
</html>
