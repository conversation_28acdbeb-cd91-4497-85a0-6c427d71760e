<?php
// ملف بسيط لتشغيل تحديث النظام
include 'addon/dbcon.php';

echo "<h2>تحديث نظام حالة الطلاب</h2>";

// إضافة عمود حالة الطالب إذا لم يكن موجوداً
$check_column = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'student_status'");
if (mysqli_num_rows($check_column) == 0) {
    echo "<p>إضافة عمود حالة الطالب...</p>";
    $add_column = "ALTER TABLE stud_tb ADD COLUMN student_status ENUM('جديد', 'مشترك') DEFAULT 'جديد'";
    if (mysqli_query($con, $add_column)) {
        echo "<p style='color: green;'>✅ تم إضافة عمود حالة الطالب بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في إضافة عمود حالة الطالب</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ عمود حالة الطالب موجود مسبقاً</p>";
}

echo "<p style='color: green; font-weight: bold;'>✅ تم تحديث النظام بنجاح!</p>";
echo "<p><a href='statistics.php'>العودة إلى صفحة الإحصائيات</a></p>";
?>
