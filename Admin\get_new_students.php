<?php
session_start();
header('Content-Type: application/json');

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

if ($user_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المستخدم غير صحيح']);
    exit;
}

$datenow = date('Y-m-d');
$date_30_days_ago = date('Y-m-d', strtotime($datenow. ' - 30 days'));

// جلب الطلاب الجدد (المسجلين حديثاً ولم يتم تفعيل اشتراكهم بعد)
$query = "SELECT s.*, sp.cash_stud, sp.date_exp, u.user_name,
          DATEDIFF('$datenow', s.datein) as days_since_registration
          FROM stud_tb s
          LEFT JOIN stud_pay sp ON s.id = sp.id_stud
          INNER JOIN users_tb u ON s.userID = u.id_user
          WHERE s.userID = ?
          AND s.datein >= '$date_30_days_ago'
          AND (sp.cash_stud IS NULL OR sp.cash_stud = 0)
          ORDER BY s.datein DESC";

$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

$students = [];
while ($row = $result->fetch_assoc()) {
    $students[] = $row;
}

echo json_encode(['success' => true, 'students' => $students]);

$con->close();
?>
