<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo "غير مصرح";
    exit();
}

include "dbcon.php";

if (!$con) {
    echo "خطأ في الاتصال بقاعدة البيانات";
    exit();
}

// معالجة البيانات المرسلة
$input = isset($_POST['myinput']) ? $_POST['myinput'] : (isset($_GET['myinput']) ? $_GET['myinput'] : '');
$user_id = isset($_POST['user_id']) ? $_POST['user_id'] : (isset($_GET['user_id']) ? $_GET['user_id'] : '0');

// إزالة رسالة الاختبار وعرض البيانات مباشرة

if ($input == 'الكل' || empty($input)) {
    // عرض جميع البيانات
    ?>
    <div class="alert alert-info mb-4">
        <h5><i class="fas fa-chart-bar"></i> عرض شامل للمعاملات المالية</h5>
        <p class="mb-0">آخر المعاملات في النظام</p>
    </div>

    <div class="row">
        <!-- الإيرادات -->
        <div class="col-md-4 mb-4">
            <div class="card border-success h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-arrow-up"></i> آخر الإيرادات</h6>
                </div>
                <div class="card-body">
    <?php
    
    $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
    $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name 
                      FROM stud_tb, stud_pay, users_tb 
                      WHERE stud_pay.id_stud = stud_tb.id 
                      AND stud_tb.userID = users_tb.id_user 
                      $user_condition 
                      ORDER BY stud_pay.datein DESC LIMIT 5";
    $revenue_result = mysqli_query($con, $revenue_query);
    
    if ($revenue_result && mysqli_num_rows($revenue_result) > 0) {
        $total_revenue = 0;
        while ($item = mysqli_fetch_assoc($revenue_result)) {
            $total_revenue += $item['cash_stud'];
            echo "<div class='mb-3 p-3 border-left border-success bg-light rounded'>";
            echo "<div class='d-flex justify-content-between align-items-center'>";
            echo "<div>";
            echo "<strong class='text-dark'>" . htmlspecialchars($item['name']) . "</strong><br>";
            echo "<small class='text-muted'>" . htmlspecialchars($item['user_name']) . "</small>";
            echo "</div>";
            echo "<div class='text-right'>";
            echo "<span class='badge badge-success'>IQD " . number_format($item['cash_stud']) . "</span><br>";
            echo "<small class='text-muted'>" . date('Y-m-d', strtotime($item['datein'])) . "</small>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "<div class='mt-3 p-2 bg-success text-white rounded text-center'>";
        echo "<strong>إجمالي آخر 5 معاملات: IQD " . number_format($total_revenue) . "</strong>";
        echo "</div>";
    } else {
        echo "<div class='text-center py-4'>";
        echo "<i class='fas fa-exclamation-circle fa-3x text-muted mb-3'></i>";
        echo "<p class='text-muted'>لا توجد إيرادات</p>";
        echo "</div>";
    }
    ?>
                </div>
            </div>
        </div>

        <!-- المصروفات -->
        <div class="col-md-4 mb-4">
            <div class="card border-danger h-100">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-arrow-down"></i> آخر المصروفات</h6>
                </div>
                <div class="card-body">
    <?php
    
    $user_condition = ($user_id != '0') ? " AND depit_tb.userID = '$user_id'" : "";
    $expenses_query = "SELECT depit_tb.depit_note, depit_tb.depit_cash, depit_tb.depit_date, users_tb.user_name 
                       FROM users_tb, depit_tb 
                       WHERE depit_tb.userID = users_tb.id_user 
                       $user_condition 
                       ORDER BY depit_tb.depit_date DESC LIMIT 5";
    $expenses_result = mysqli_query($con, $expenses_query);
    
    if ($expenses_result && mysqli_num_rows($expenses_result) > 0) {
        while ($item = mysqli_fetch_assoc($expenses_result)) {
            echo "<div class='mb-2 p-2 border-left border-danger'>";
            echo "<strong>" . htmlspecialchars($item['depit_note']) . "</strong><br>";
            echo "<small class='text-danger'>IQD " . number_format($item['depit_cash']) . "</small><br>";
            echo "<small class='text-muted'>" . date('Y-m-d', strtotime($item['depit_date'])) . "</small>";
            echo "</div>";
        }
    } else {
        echo "<p class='text-muted'>لا توجد مصروفات</p>";
    }
    
    echo "</div></div></div>";
    
    // الرواتب
    echo "<div class='col-md-4'>";
    echo "<div class='card border-warning'>";
    echo "<div class='card-header bg-warning text-dark'>الرواتب</div>";
    echo "<div class='card-body'>";
    
    $user_condition = ($user_id != '0') ? " AND employ_tb.userID = '$user_id'" : "";
    $salaries_query = "SELECT employ_tb.f_name, employ_tb.job, employ_tb.salary, employ_tb.date_start, users_tb.user_name 
                       FROM users_tb, employ_tb 
                       WHERE employ_tb.userID = users_tb.id_user 
                       $user_condition 
                       ORDER BY employ_tb.date_start DESC LIMIT 5";
    $salaries_result = mysqli_query($con, $salaries_query);
    
    if ($salaries_result && mysqli_num_rows($salaries_result) > 0) {
        while ($item = mysqli_fetch_assoc($salaries_result)) {
            echo "<div class='mb-2 p-2 border-left border-warning'>";
            echo "<strong>" . htmlspecialchars($item['f_name']) . "</strong><br>";
            echo "<small>" . htmlspecialchars($item['job']) . "</small><br>";
            echo "<small class='text-warning'>IQD " . number_format($item['salary']) . "</small><br>";
            echo "<small class='text-muted'>" . date('Y-m-d', strtotime($item['date_start'])) . "</small>";
            echo "</div>";
        }
    } else {
        echo "<p class='text-muted'>لا توجد رواتب</p>";
    }
    
    echo "</div></div></div>";
    echo "</div>";
    
} elseif ($input == 'ايرادات') {
    echo "<div class='alert alert-success'>";
    echo "<h5>الإيرادات</h5>";
    echo "</div>";
    
    $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
    $dates = isset($_POST['dates']) ? $_POST['dates'] : '';
    $datee = isset($_POST['datee']) ? $_POST['datee'] : '';
    $date_condition = ($dates && $datee) ? " AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'" : "";
    
    $query = "SELECT stud_tb.*, stud_pay.*, users_tb.user_name 
              FROM stud_tb, stud_pay, users_tb 
              WHERE stud_pay.id_stud = stud_tb.id 
              AND stud_tb.userID = users_tb.id_user 
              $date_condition $user_condition 
              ORDER BY stud_tb.datein DESC LIMIT 50";
    
    $result = mysqli_query($con, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='thead-dark'>";
        echo "<tr><th>الطالب</th><th>المبلغ</th><th>التاريخ</th><th>المستخدم</th></tr>";
        echo "</thead><tbody>";
        
        $total = 0;
        while ($row = mysqli_fetch_assoc($result)) {
            $total += $row['cash_stud'];
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td class='text-success'><strong>IQD " . number_format($row['cash_stud']) . "</strong></td>";
            echo "<td>" . date('Y-m-d', strtotime($row['datein'])) . "</td>";
            echo "<td>" . htmlspecialchars($row['user_name']) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "<tfoot><tr class='table-success'>";
        echo "<th>الإجمالي:</th><th><strong>IQD " . number_format($total) . "</strong></th>";
        echo "<th colspan='2'>" . mysqli_num_rows($result) . " معاملة</th>";
        echo "</tr></tfoot>";
        echo "</table></div>";
    } else {
        echo "<div class='alert alert-warning'>لا توجد إيرادات للفترة المحددة</div>";
    }
    
} else {
    echo "<div class='alert alert-info'>نوع البيانات غير مدعوم: " . htmlspecialchars($input) . "</div>";
}

mysqli_close($con);
?>
