<?php
session_start();

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول");
}

include "addon/dbcon.php";

$type = isset($_GET['type']) ? $_GET['type'] : '';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($type) || $id <= 0) {
    die("معاملات غير صحيحة");
}

$details = [];

switch ($type) {
    case 'ايرادات':
        $query = "SELECT sp.*, s.stud_name, s.stud_age, s.catg, u.user_name 
                  FROM stud_pay sp
                  JOIN stud_tb s ON sp.id_stud = s.id
                  JOIN users_tb u ON s.userID = u.id_user
                  WHERE sp.id = ?";
        break;
        
    case 'مصروفات':
        $query = "SELECT d.*, u.user_name 
                  FROM depit_tb d
                  JOIN users_tb u ON d.user_id = u.id_user
                  WHERE d.id = ?";
        break;
        
    case 'رواتب':
        $query = "SELECT e.*, u.user_name 
                  FROM employ_tb e
                  JOIN users_tb u ON e.user_id = u.id_user
                  WHERE e.id = ?";
        break;
        
    default:
        die("نوع غير صحيح");
}

$stmt = mysqli_prepare($con, $query);
mysqli_stmt_bind_param($stmt, "i", $id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    echo '<div class="transaction-details">';
    
    switch ($type) {
        case 'ايرادات':
            echo '<h6 class="text-success"><i class="fas fa-arrow-up"></i> تفاصيل الإيراد</h6>';
            echo '<table class="table table-bordered">';
            echo '<tr><td><strong>اسم الطالب:</strong></td><td>' . htmlspecialchars($row['stud_name']) . '</td></tr>';
            echo '<tr><td><strong>عمر الطالب:</strong></td><td>' . $row['stud_age'] . ' سنة</td></tr>';
            echo '<tr><td><strong>الفئة:</strong></td><td>' . htmlspecialchars($row['catg']) . '</td></tr>';
            echo '<tr><td><strong>المستخدم:</strong></td><td>' . htmlspecialchars($row['user_name']) . '</td></tr>';
            echo '<tr><td><strong>المبلغ المدفوع:</strong></td><td class="text-success"><strong>' . number_format($row['stud_pay']) . ' د.ع</strong></td></tr>';
            echo '<tr><td><strong>تاريخ الدفع:</strong></td><td>' . date('Y-m-d', strtotime($row['date_pay'])) . '</td></tr>';
            echo '<tr><td><strong>تاريخ انتهاء الاشتراك:</strong></td><td>' . date('Y-m-d', strtotime($row['date_exp'])) . '</td></tr>';
            echo '</table>';
            break;
            
        case 'مصروفات':
            echo '<h6 class="text-danger"><i class="fas fa-arrow-down"></i> تفاصيل المصروف</h6>';
            echo '<table class="table table-bordered">';
            echo '<tr><td><strong>اسم المصروف:</strong></td><td>' . htmlspecialchars($row['depit_name']) . '</td></tr>';
            echo '<tr><td><strong>المستخدم:</strong></td><td>' . htmlspecialchars($row['user_name']) . '</td></tr>';
            echo '<tr><td><strong>المبلغ:</strong></td><td class="text-danger"><strong>' . number_format($row['depit_amount']) . ' د.ع</strong></td></tr>';
            echo '<tr><td><strong>التاريخ:</strong></td><td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td></tr>';
            if (!empty($row['depit_desc'])) {
                echo '<tr><td><strong>الوصف:</strong></td><td>' . htmlspecialchars($row['depit_desc']) . '</td></tr>';
            }
            echo '</table>';
            break;
            
        case 'رواتب':
            echo '<h6 class="text-warning"><i class="fas fa-users"></i> تفاصيل الراتب</h6>';
            echo '<table class="table table-bordered">';
            echo '<tr><td><strong>اسم الموظف:</strong></td><td>' . htmlspecialchars($row['employ_name']) . '</td></tr>';
            echo '<tr><td><strong>المستخدم:</strong></td><td>' . htmlspecialchars($row['user_name']) . '</td></tr>';
            echo '<tr><td><strong>الراتب:</strong></td><td class="text-warning"><strong>' . number_format($row['employ_salary']) . ' د.ع</strong></td></tr>';
            echo '<tr><td><strong>التاريخ:</strong></td><td>' . date('Y-m-d', strtotime($row['employ_date'])) . '</td></tr>';
            if (!empty($row['employ_phone'])) {
                echo '<tr><td><strong>رقم الهاتف:</strong></td><td>' . htmlspecialchars($row['employ_phone']) . '</td></tr>';
            }
            if (!empty($row['employ_address'])) {
                echo '<tr><td><strong>العنوان:</strong></td><td>' . htmlspecialchars($row['employ_address']) . '</td></tr>';
            }
            echo '</table>';
            break;
    }
    
    echo '</div>';
    
    // إضافة أزرار الإجراءات
    echo '<div class="mt-3">';
    echo '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>';
    
    if ($type == 'مصروفات') {
        echo ' <a href="edit_depit.php?id=' . $row['id'] . '" class="btn btn-warning">تعديل</a>';
    }
    
    echo '</div>';
    
} else {
    echo '<div class="alert alert-warning">لم يتم العثور على تفاصيل هذه المعاملة</div>';
}

mysqli_stmt_close($stmt);
mysqli_close($con);
?>
