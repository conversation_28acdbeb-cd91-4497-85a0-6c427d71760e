<?php
session_start();

// محاكاة جلسة المستخدم
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) [
        'role' => 'User',
        'user_name' => 'مستخدم تجريبي',
        'id_user' => 1
    ];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شريط التنقل الجديد</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        
        .demo-content {
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .demo-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        
        .feature-list {
            text-align: right;
            margin: 20px 0;
        }
        
        .feature-list li {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="demo-content">
        <div class="demo-card">
            <h1 style="color: #333; margin-bottom: 20px;">
                <i class="fas fa-star" style="color: #ffc107;"></i>
                شريط التنقل الجديد والمحسن
            </h1>
            
            <p style="font-size: 18px; color: #666; margin-bottom: 30px;">
                تم إعادة تنظيم جميع الأزرار وجعلها في أعلى الصفحة بجانب "مرحباً بك"
            </p>
            
            <div class="feature-list">
                <h3 style="color: #007bff; margin-bottom: 15px;">المميزات الجديدة:</h3>
                <ul style="list-style: none; padding: 0;">
                    <li>✅ <strong>الأزرار الأساسية:</strong> الرئيسية، إضافة طالب، الطلاب، حضور الموظفين، حضور الطلاب، المصاريف، الموظفين، ماذا عنا</li>
                    <li>✅ <strong>أزرار الطلبات:</strong> طلب احتياج، احتياجاتي، طلب إجازة، إجازاتي</li>
                    <li>✅ <strong>ألوان مميزة:</strong> كل مجموعة أزرار لها لون مختلف</li>
                    <li>✅ <strong>فاصل بصري:</strong> خط فاصل بين المجموعات</li>
                    <li>✅ <strong>تصميم متجاوب:</strong> يتكيف مع جميع أحجام الشاشات</li>
                    <li>✅ <strong>تأثيرات حركية:</strong> تأثيرات جميلة عند التمرير والنقر</li>
                    <li>✅ <strong>أيقونات واضحة:</strong> أيقونة مناسبة لكل زر</li>
                    <li>✅ <strong>ثابت في الأعلى:</strong> الأزرار تبقى مرئية دائماً</li>
                </ul>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 10px;">
                <h4 style="color: #1976d2; margin-bottom: 10px;">
                    <i class="fas fa-info-circle"></i> كيفية الاستخدام:
                </h4>
                <p style="color: #333; margin: 0;">
                    انقر على أي زر في الأعلى للانتقال السريع إلى الصفحة المطلوبة. 
                    الأزرار منظمة حسب الاستخدام: الأزرار الأساسية أولاً، ثم أزرار الطلبات.
                </p>
            </div>
            
            <div style="margin-top: 20px;">
                <a href="home.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // إضافة تأثير تفاعلي للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            const quickBtns = document.querySelectorAll('.quick-btn');
            
            quickBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // رسالة ترحيب
            setTimeout(() => {
                console.log('🎉 شريط التنقل الجديد جاهز!');
            }, 1000);
        });
    </script>
</body>
</html>
