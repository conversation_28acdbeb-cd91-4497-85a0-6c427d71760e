# تحديث صفحة حضور الطلاب

## التغييرات المطبقة

تم تحديث صفحة حضور الطلاب (`Admin/attandec.php`) لتصبح مطابقة تماماً لصفحة حضور الموظفين من حيث التصميم والوظائف.

## الملفات المحدثة/المضافة

### 1. Admin/attandec.php
- **التحديث الرئيسي**: تم إعادة كتابة الملف بالكامل ليطابق تصميم صفحة حضور الموظفين
- **التصميم الجديد**: واجهة حديثة مع Bootstrap وتدرجات لونية جميلة
- **الوظائف الجديدة**:
  - فلاتر متقدمة (حسب المستخدم، الحالة، البحث في الأسماء)
  - إحصائيات مباشرة (إجمالي الطلاب، الحاضرين، المتأخرين، الغائبين، إلخ)
  - ترتيب النتائج تصاعدي/تنازلي
  - طباعة وتصدير التقارير
  - نوافذ منبثقة لتعديل الحضور وعرض التفاصيل

### 2. Admin/student_attendance_data.php (جديد)
- ملف API لجلب بيانات حضور الطلاب
- يدعم الفلترة حسب التاريخ، الطالب، والصف الدراسي
- يربط بيانات الطلاب من جدول `stud_tb` مع بيانات الحضور من جدول `stat`

### 3. Admin/save_student_attendance.php (جديد)
- ملف API لحفظ وتحديث بيانات حضور الطلاب
- يدعم إضافة سجلات جديدة أو تحديث السجلات الموجودة
- يتعامل مع جدول `stat` لحفظ بيانات الحضور

## الميزات الجديدة

### 1. واجهة المستخدم
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **ألوان متدرجة**: تصميم جذاب مع تدرجات لونية حديثة
- **أيقونات Font Awesome**: أيقونات واضحة ومعبرة
- **رسوم متحركة**: تأثيرات hover وانتقالات سلسة

### 2. البحث والفلترة
- **فلتر المستخدم**: عرض الطلاب حسب المستخدم المسؤول
- **فلتر الحالة**: فلترة حسب حالة الحضور (حاضر، غائب، متأخر، إجازة)
- **البحث في الأسماء**: بحث مباشر في أسماء الطلاب
- **فلتر الصف الدراسي**: فلترة حسب الصف (روضة، حضانة، تمهيدي، تحضيري)

### 3. الإحصائيات
- **إجمالي المعروض**: عدد الطلاب المعروضين حالياً
- **الحاضرين**: عدد الطلاب الحاضرين
- **المتأخرين**: عدد الطلاب المتأخرين
- **الغائبين**: عدد الطلاب الغائبين
- **في إجازة**: عدد الطلاب في إجازة
- **لم يسجل**: عدد الطلاب الذين لم يتم تسجيل حضورهم

### 4. إدارة الحضور
- **تسجيل حضور جديد**: إمكانية تسجيل حضور للطلاب
- **تعديل الحضور**: تعديل حالة الحضور الموجودة
- **أوقات الحضور**: تسجيل وقت الحضور الدقيق
- **الملاحظات**: إضافة ملاحظات على حضور الطلاب

### 5. التقارير
- **طباعة التقارير**: طباعة تقرير حضور مفصل
- **تصدير Excel**: تصدير البيانات إلى ملف CSV
- **تقارير مخصصة**: تقارير حسب التاريخ والفلاتر المحددة

## هيكل قاعدة البيانات المستخدم

### جدول الطلاب (stud_tb)
- `id`: معرف الطالب
- `name`: اسم الطالب
- `catg`: الصف الدراسي
- `p_name`: اسم ولي الأمر
- `userID`: معرف المستخدم المسؤول

### جدول الحضور (stat)
- `id`: معرف السجل
- `id_stud`: معرف الطالب
- `stat_stud`: حالة الحضور
- `data_stat`: تاريخ ووقت الحضور

### جدول المستخدمين (users_tb)
- `id_user`: معرف المستخدم
- `user_name`: اسم المستخدم
- `role`: دور المستخدم

## كيفية الاستخدام

1. **الوصول للصفحة**: انتقل إلى `Admin/attandec.php`
2. **اختيار الفلاتر**: استخدم القوائم المنسدلة لاختيار الطالب، الصف، والتاريخ
3. **عرض النتائج**: اضغط على "عرض الحضور" لعرض البيانات
4. **استخدام الفلاتر المتقدمة**: استخدم فلاتر المستخدم والحالة والبحث
5. **تعديل الحضور**: اضغط على أي سجل لتعديل حالة الحضور
6. **طباعة/تصدير**: استخدم أزرار الطباعة والتصدير في أعلى الجدول

## الملاحظات التقنية

- الصفحة تستخدم AJAX لتحميل البيانات بشكل ديناميكي
- التصميم متجاوب ويعمل على الهواتف والأجهزة اللوحية
- يتم حفظ البيانات في قاعدة البيانات الموجودة دون تغيير الهيكل
- الكود محسن للأداء ويدعم عدد كبير من الطلاب

## التوافق

- متوافق مع جميع المتصفحات الحديثة
- يعمل مع PHP 7.0 وما فوق
- يتطلب MySQL/MariaDB
- يستخدم Bootstrap 5 و Font Awesome 6
