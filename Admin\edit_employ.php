<?php
session_start();
if (isset($_SESSION['user'])) {
    if ($_SESSION['user']->role === "Admin") {
    } else {
        header("location:../login.php", true);
        die("");
        echo "dont work";
    }
} else {
    header("location:../login.php", true);
    die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الموظف</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php include "addon/topbar.php" ?>
    <?php include "addon/dbcon.php" ?>
</head>

<body>
    <?php
    $id = $_GET['UpDate'];
    $sql = "SELECT * FROM employ_tb WHERE id_employ=$id";
    $resul = mysqli_query($con, $sql);
    $row = mysqli_fetch_assoc($resul);
    $id = $row['id_employ'];
    $f_name = $row['f_name'];
    $b_date = $row['b_date'];
    $job = $row['job'];
    $location = $row['location'];
    $date_start = $row['date_start'];
    $salary = $row['salary'];
   
    ?>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
                <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <div class='contin_employ_admin'>
        <form method="POST">
            <div class='input-box'>
                <label for="f_name"> الاسم الثلاثي<label>
                        <input type="text" name="f_name" required value="<?php echo $row['f_name']; ?>">
                        <label for="b_date"> تاريخ التولد <label>
                                <input type="date" name="b_date" required value="<?php echo $row['b_date']; ?>">
                                <label for="job">العنوان الوضيفي</label>
                                <input type="text" name="job" required value="<?php echo $row['job']; ?>">
                                <label for="date_start">تاريخ المباشرة</label>
                                <input type="date" name="date_start" required value="<?php echo $row['date_start']; ?>">
                                <label for="location">السكن </label>
                                <input type="text" name="location" required value="<?php echo $row['location']; ?>">
                                <label for="salary">الراتب الشهري</label>
                                <input type="number" name="salary" required value="<?php echo $row['salary']; ?>">
                            
                                <div>
                                    <button class=btn name="addS" id="23">حفظ </button>
                                </div>


            </div>

</body>
<script>
    let x;
    let toast = document.getElementById("toast2"),
        text = document.querySelector(".p1"),
        text2 = document.querySelector(".p2");

    function ToastdepitADD() {
        let  icon = document.getElementById("icon");
        clearTimeout(x);
        icon.className="fa fa-circle-check";
        text.innerText="! تمت "
        text2.innerText = "تم  تعديل بيانات الموظف  "
        toast.style.transition = '1s';
        toast.style.transform = "translateX(0)";
        toast.style.transition = '1s';
        x = setTimeout(() => {
            toast.style.transform = "translateX(-500px)"
              
        }, 4000);
        dispable();
        setInterval(()=>{
            
            window.location.href="info_employ.php"
        },4800)
        
    }

</script>
<script>
     function dispable () { 

        jQuery("#23").prop("disabled",true)
        jQuery(".contin_employ_admin").css("transition","3s")
        jQuery(".contin_employ_admin").css("opacity","0.0")
        
        
    };
</script>
<?php

 if (isset($_POST['addS'])) {
    $id = $_GET['UpDate'];
    $f_name = $_POST['f_name'];
    $b_date = $_POST['b_date'];
    $location = $_POST['location'];
    $date_start = $_POST['date_start'];
    $job = $_POST['job'];
    $salary = $_POST['salary'];
    //$userID = $_POST['userID'];
    $addData = "UPDATE employ_tb SET f_name='$f_name',b_date='$b_date',location='$location',date_start='$date_start',job='$job',salary='$salary' WHERE id_employ='$id'";
    $resul = mysqli_query($con, $addData);
    if ($resul) {
        
        echo "<script>ToastdepitADD()</script>";
        
        
    }
}
?>

</html>