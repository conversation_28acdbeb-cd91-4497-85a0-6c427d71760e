<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

include 'addon/dbcon.php';

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Baghdad');

echo "<h2>اختبار نظام الإشعارات</h2>";

// اختبار 1: إنشاء إشعار تجريبي
if (isset($_POST['test_admin_notification'])) {
    $user_id = $_SESSION['user']->id_user;
    $title = "إشعار تجريبي للأدمن";
    $message = "هذا إشعار تجريبي تم إنشاؤه في " . date('Y-m-d H:i:s');
    
    $query = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'general')";
    $stmt = $con->prepare($query);
    $stmt->bind_param("iss", $user_id, $title, $message);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ تم إنشاء إشعار تجريبي للأدمن</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في إنشاء الإشعار</p>";
    }
}

// اختبار 2: إنشاء إشعار لجميع المستخدمين
if (isset($_POST['test_all_users'])) {
    $title = "إشعار عام لجميع المستخدمين";
    $message = "هذا إشعار تجريبي لجميع المستخدمين تم إنشاؤه في " . date('Y-m-d H:i:s');
    
    $users_query = "SELECT id_user FROM users_tb";
    $users_result = mysqli_query($con, $users_query);
    $count = 0;
    
    while ($user_row = mysqli_fetch_assoc($users_result)) {
        $query = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'general')";
        $stmt = $con->prepare($query);
        $stmt->bind_param("iss", $user_row['id_user'], $title, $message);
        if ($stmt->execute()) {
            $count++;
        }
    }
    
    echo "<p style='color: green;'>✅ تم إنشاء إشعار لـ $count مستخدم</p>";
}

// اختبار 3: محاكاة طلب إجازة
if (isset($_POST['simulate_leave'])) {
    $admin_users = "SELECT id_user FROM users_tb WHERE role IN ('Admin', 'Moda', 'Modaqeq')";
    $admin_result = mysqli_query($con, $admin_users);
    $count = 0;
    
    $title = "طلب إجازة تجريبي";
    $message = "تم تقديم طلب إجازة تجريبي من قبل " . $_SESSION['user']->user_name . " من تاريخ " . date('Y-m-d') . " إلى " . date('Y-m-d', strtotime('+3 days'));
    
    while ($admin_row = mysqli_fetch_assoc($admin_result)) {
        $query = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'leave_request')";
        $stmt = $con->prepare($query);
        $stmt->bind_param("iss", $admin_row['id_user'], $title, $message);
        if ($stmt->execute()) {
            $count++;
        }
    }
    
    echo "<p style='color: green;'>✅ تم إرسال إشعار طلب إجازة تجريبي لـ $count مدير</p>";
}

// اختبار 4: محاكاة رد على طلب
if (isset($_POST['simulate_response'])) {
    $user_id = $_SESSION['user']->id_user;
    $title = "تم الرد على طلبك";
    $message = "تم الموافقة على طلبك التجريبي في " . date('Y-m-d H:i:s');
    
    $query = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'leave_response')";
    $stmt = $con->prepare($query);
    $stmt->bind_param("iss", $user_id, $title, $message);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ تم إنشاء إشعار رد تجريبي</p>";
    }
}

// عرض إحصائيات النظام
echo "<h3>إحصائيات النظام:</h3>";
$stats = [
    'إجمالي الإشعارات' => "SELECT COUNT(*) as count FROM notifications",
    'الإشعارات غير المقروءة' => "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0",
    'إشعارات اليوم' => "SELECT COUNT(*) as count FROM notifications WHERE DATE(created_at) = CURDATE()",
    'إشعارات الساعة الأخيرة' => "SELECT COUNT(*) as count FROM notifications WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)"
];

foreach ($stats as $label => $query) {
    $result = mysqli_query($con, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>📊 $label: <strong>{$row['count']}</strong></p>";
    }
}

// عرض آخر 10 إشعارات
echo "<h3>آخر 10 إشعارات:</h3>";
$recent = "SELECT n.*, u.user_name, u.role FROM notifications n 
           LEFT JOIN users_tb u ON n.user_id = u.id_user 
           ORDER BY n.created_at DESC LIMIT 10";
$result = mysqli_query($con, $recent);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>المستخدم</th><th>الدور</th><th>العنوان</th><th>النوع</th><th>التاريخ</th><th>مقروء</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $read_status = $row['is_read'] ? '✅' : '❌';
        $time_diff = time() - strtotime($row['created_at']);
        $time_ago = '';
        
        if ($time_diff < 60) $time_ago = 'الآن';
        elseif ($time_diff < 3600) $time_ago = floor($time_diff/60) . ' دقيقة';
        elseif ($time_diff < 86400) $time_ago = floor($time_diff/3600) . ' ساعة';
        else $time_ago = floor($time_diff/86400) . ' يوم';
        
        echo "<tr>";
        echo "<td>{$row['user_name']}</td>";
        echo "<td>{$row['role']}</td>";
        echo "<td>{$row['title']}</td>";
        echo "<td>{$row['type']}</td>";
        echo "<td>{$row['created_at']} ($time_ago)</td>";
        echo "<td>$read_status</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// أزرار الاختبار
echo "<h3>اختبارات النظام:</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";

echo "<form method='POST' style='display: inline;'>";
echo "<button type='submit' name='test_admin_notification' style='background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer;'>إشعار تجريبي للأدمن</button>";
echo "</form>";

echo "<form method='POST' style='display: inline;'>";
echo "<button type='submit' name='test_all_users' style='background: #28a745; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer;'>إشعار لجميع المستخدمين</button>";
echo "</form>";

echo "<form method='POST' style='display: inline;'>";
echo "<button type='submit' name='simulate_leave' style='background: #ffc107; color: black; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer;'>محاكاة طلب إجازة</button>";
echo "</form>";

echo "<form method='POST' style='display: inline;'>";
echo "<button type='submit' name='simulate_response' style='background: #17a2b8; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer;'>محاكاة رد على طلب</button>";
echo "</form>";

echo "</div>";

// تحقق من حالة النظام
echo "<h3>تحقق من حالة النظام:</h3>";

// التحقق من وجود الجداول
$tables = ['notifications', 'users_tb', 'leave_requests', 'needs_requests'];
foreach ($tables as $table) {
    $check = mysqli_query($con, "SHOW TABLES LIKE '$table'");
    $status = mysqli_num_rows($check) > 0 ? '✅' : '❌';
    echo "<p>$status جدول $table</p>";
}

// التحقق من وجود الملفات
$files = [
    'notifications_api.php' => 'API الإشعارات',
    'notifications_component.php' => 'مكون الإشعارات',
    '../Users/<USER>' => 'مكون إشعارات المستخدمين'
];

foreach ($files as $file => $description) {
    $status = file_exists($file) ? '✅' : '❌';
    echo "<p>$status $description</p>";
}

echo "<p style='margin-top: 30px;'>";
echo "<a href='home.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للرئيسية</a>";
echo "<a href='fix_notifications.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إصلاح المشاكل</a>";
echo "</p>";

$con->close();
?>
