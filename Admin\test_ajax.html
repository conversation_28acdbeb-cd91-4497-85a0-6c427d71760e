<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار AJAX</title>
    <script src="js/jquery.min.js"></script>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { border: 2px solid #007bff; padding: 15px; margin: 10px 0; background: #f8f9fa; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .success { border-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <h2>اختبار AJAX للمستخدم رقم 7</h2>
    
    <button id="testBtn" class="btn btn-primary">اختبار AJAX</button>
    <button id="clearBtn" class="btn btn-secondary">مسح النتائج</button>
    
    <div id="result" class="result" style="display: none;">
        <h4>النتيجة:</h4>
        <div id="content"></div>
    </div>
    
    <div id="rawData" class="result" style="display: none;">
        <h4>البيانات الخام:</h4>
        <pre id="rawContent"></pre>
    </div>

    <script>
        $("#testBtn").click(function() {
            console.log("بدء اختبار AJAX...");
            
            $.ajax({
                method: "POST",
                url: "addon/infostudF.php",
                data: {id: 7},
                success: function (data) {
                    console.log("نجح AJAX:", data);
                    
                    $("#result").show();
                    $("#content").html(data);
                    
                    $("#rawData").show();
                    $("#rawContent").text(data);
                    
                    // البحث عن الإحصائيات في البيانات
                    if (data.includes('showToast2')) {
                        console.log("✅ تم العثور على الإحصائيات في البيانات");
                        $("#result").removeClass("error").addClass("success");
                    } else {
                        console.log("❌ لم يتم العثور على الإحصائيات");
                        $("#result").removeClass("success").addClass("error");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("خطأ في AJAX:", error);
                    $("#result").show().removeClass("success").addClass("error");
                    $("#content").html("خطأ: " + error);
                }
            });
        });
        
        $("#clearBtn").click(function() {
            $("#result, #rawData").hide();
            $("#content, #rawContent").empty();
        });
        
        // دالة showToast2 للاختبار
        function showToast2(data) {
            console.log("تم استدعاء showToast2 مع البيانات:", data);
            alert("الإحصائيات: " + JSON.stringify(data, null, 2));
        }
    </script>
</body>
</html>
