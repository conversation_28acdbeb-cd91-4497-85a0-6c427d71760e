<?php
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

$student = $_SESSION['student'];
?>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">
            <div class="brand-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <span class="brand-text">نظام الطالب</span>
        </a>

        <button class="navbar-toggler custom-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span></span>
            <span></span>
            <span></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link nav-btn" href="index.php">
                        <div class="nav-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="nav-text">الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-btn special-btn" href="request_leave.php">
                        <div class="nav-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <span class="nav-text">طلب إجازة</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-btn" href="my_leaves.php">
                        <div class="nav-icon">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <span class="nav-text">إجازاتي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-btn" href="holidays.php">
                        <div class="nav-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <span class="nav-text">العطل</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-btn" href="news.php">
                        <div class="nav-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <span class="nav-text">الأخبار</span>
                    </a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle user-dropdown" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="user-info">
                            <span class="user-name"><?php echo htmlspecialchars($student->name); ?></span>
                            <small class="user-role">طالب</small>
                        </div>
                    </a>
                    <ul class="dropdown-menu custom-dropdown">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-edit"></i>
                            <span>الملف الشخصي</span>
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item logout-item" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* تحسين شريط التنقل */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    padding: 1rem 0;
    border-bottom: 3px solid rgba(255,255,255,0.2);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: bold;
    font-size: 1.4rem;
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: #ffd700 !important;
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

.brand-text {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

/* أزرار التنقل المحسنة */
.nav-btn {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 0.8rem 1.2rem !important;
    margin: 0 0.3rem;
    border-radius: 15px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    color: #ffd700 !important;
    border-color: rgba(255,215,0,0.5);
}

.nav-icon {
    width: 35px;
    height: 35px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.nav-btn:hover .nav-icon {
    background: rgba(255,215,0,0.3);
    transform: scale(1.1);
}

.nav-text {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
}

/* زر طلب الإجازة المميز */
.special-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    animation: pulse 2s infinite;
}

.special-btn:hover {
    background: linear-gradient(45deg, #ff5252, #d63031) !important;
    transform: translateY(-5px) scale(1.05);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
}

/* قائمة المستخدم المحسنة */
.user-dropdown {
    display: flex !important;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.2rem !important;
    background: rgba(255,255,255,0.15);
    border-radius: 25px;
    border: 1px solid rgba(255,255,255,0.3);
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.user-dropdown:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-2px);
    color: #ffd700 !important;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: bold;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* القائمة المنسدلة المحسنة */
.custom-dropdown {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.3) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.custom-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.5rem;
    color: #2c3e50 !important;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 0.2rem 0.5rem;
}

.custom-dropdown .dropdown-item:hover {
    background: linear-gradient(45deg, #3498db, #2980b9) !important;
    color: white !important;
    transform: translateX(5px);
}

.custom-dropdown .dropdown-item i {
    width: 20px;
    text-align: center;
}

.logout-item:hover {
    background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
}

/* زر القائمة المخصص */
.custom-toggler {
    border: none;
    padding: 0.5rem;
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.custom-toggler span {
    display: block;
    width: 20px;
    height: 2px;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.custom-toggler:hover span {
    background: #ffd700;
}

/* تصميم متجاوب للهاتف */
@media (max-width: 991px) {
    .navbar-nav {
        margin-top: 1rem;
        padding: 1rem;
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        backdrop-filter: blur(10px);
    }

    .nav-btn {
        flex-direction: row !important;
        justify-content: flex-start !important;
        margin: 0.3rem 0;
        padding: 1rem !important;
    }

    .nav-icon {
        margin-left: 0.8rem;
    }

    .user-dropdown {
        justify-content: flex-start !important;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .navbar {
        padding: 0.8rem 0;
    }

    .brand-text {
        font-size: 1.1rem;
    }

    .brand-icon {
        width: 35px;
        height: 35px;
        font-size: 1.2rem;
    }

    .nav-text {
        font-size: 0.9rem;
    }
}
</style>
