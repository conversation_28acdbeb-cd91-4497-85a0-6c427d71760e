<?php
include "addon/dbcon.php";

echo "<h2>اختبار قاعدة البيانات</h2>";

// عرض جميع المستخدمين
echo "<h3>جميع المستخدمين:</h3>";
$users_sql = "SELECT id_user, user_name FROM users_tb ORDER BY user_name";
$users_result = mysqli_query($con, $users_sql);

if ($users_result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th></tr>";
    while ($user = mysqli_fetch_assoc($users_result)) {
        echo "<tr><td>" . $user['id_user'] . "</td><td>" . $user['user_name'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "خطأ في استعلام المستخدمين: " . mysqli_error($con);
}

// عرض جميع الطلاب
echo "<h3>جميع الطلاب:</h3>";
$students_sql = "SELECT stud_tb.id, stud_tb.name, stud_tb.userID, users_tb.user_name 
                 FROM stud_tb 
                 LEFT JOIN users_tb ON stud_tb.userID = users_tb.id_user 
                 ORDER BY stud_tb.userID";
$students_result = mysqli_query($con, $students_sql);

if ($students_result) {
    echo "<table border='1'>";
    echo "<tr><th>ID الطالب</th><th>اسم الطالب</th><th>ID المستخدم</th><th>اسم المستخدم</th></tr>";
    while ($student = mysqli_fetch_assoc($students_result)) {
        echo "<tr><td>" . $student['id'] . "</td><td>" . $student['name'] . "</td><td>" . $student['userID'] . "</td><td>" . $student['user_name'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "خطأ في استعلام الطلاب: " . mysqli_error($con);
}

// عرض جميع المدفوعات
echo "<h3>جميع المدفوعات:</h3>";
$payments_sql = "SELECT id_pay, id_stud, cash_stud, date_exp FROM stud_pay ORDER BY id_stud";
$payments_result = mysqli_query($con, $payments_sql);

if ($payments_result) {
    echo "<table border='1'>";
    echo "<tr><th>ID الدفع</th><th>ID الطالب</th><th>المبلغ</th><th>تاريخ الانتهاء</th></tr>";
    while ($payment = mysqli_fetch_assoc($payments_result)) {
        echo "<tr><td>" . $payment['id_pay'] . "</td><td>" . $payment['id_stud'] . "</td><td>" . $payment['cash_stud'] . "</td><td>" . $payment['date_exp'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "خطأ في استعلام المدفوعات: " . mysqli_error($con);
}
?>
