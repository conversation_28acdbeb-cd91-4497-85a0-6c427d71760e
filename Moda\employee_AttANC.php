<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        // المستخدم مخول
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حضور الموظفين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php include "addon/topbar.php" ?>
    <?php include "addon/dbcon.php" ?>
    
    <style>
        .status-tooltip {
          position: relative;
          cursor: pointer;
        }
        
        .status-tooltip .tooltiptext {
          visibility: hidden;
          width: 250px;
          background-color: #333;
          color: #fff;
          text-align: center;
          border-radius: 6px;
          padding: 8px;
          position: absolute;
          z-index: 1;
          bottom: 125%;
          left: 50%;
          margin-left: -125px;
          opacity: 0;
          transition: opacity 0.3s;
          font-size: 12px;
        }
        
        .status-tooltip .tooltiptext::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: #333 transparent transparent transparent;
        }
        
        .status-tooltip:hover .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
        
        .search_ac {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 8px;
        }
        
        .search_ac label {
          color: #666;
          font-weight: bold;
          margin-left: 10px;
        }
        
        .date-range {
          display: flex;
          gap: 10px;
          align-items: center;
          margin: 10px 0;
        }
        
        .status-present { color: #28a745; font-weight: bold; }
        .status-absent { color: #dc3545; font-weight: bold; }
        .status-leave { color: #ffc107; font-weight: bold; }
        .status-scheduled { color: #007bff; font-weight: bold; }
        .status-ended { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
<form action="" method="POST">
    <div class="search_ac">
        <select name="acout_type" id="selc2" placeholder='اختر مستخدم'>
            <option value="0" selected disabled>اختر مستخدم</option>
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                    $selected = (isset($_POST['acout_type']) && $_POST['acout_type'] == $id) ? 'selected' : '';
                    echo '<option value='.$id.' '.$selected.'>'.$user_name.'</option>';
                }
            }
            ?>
        </select>
        
        <div class="date-range">
            <label>من تاريخ:</label>
            <input name="dateFrom" type="date" value="<?php echo isset($_POST['dateFrom']) ? $_POST['dateFrom'] : ''; ?>">
            <label>إلى تاريخ:</label>
            <input name="dateTo" type="date" value="<?php echo isset($_POST['dateTo']) ? $_POST['dateTo'] : ''; ?>">
        </div>
        
        <button class="btn btn-warning" name='myInput'>اظهار</button> 
    </div>
</form>

<section class="button_excel" id="btn_ex" style="visibility: hidden;">
    <button type="button" class="bt_execl" onclick="exportex()">تحميل أكسل</button>
</section>

<div class="wrapper2" id="tost_info">
    <div id="toast2">
        <div class="container-11">
            <i id="icon" class="fa-solid fa-money-bills"></i>
        </div>
        <div class="container-22">
            <p class="p1">EMPLOYEE INFO</p>
            <p class="p2">تم اضافة البيانات</p>
        </div>
    </div>
</div>

<table class="table" id="Table">
    <thead>
        <tr>
            <td>تاريخ الحضور</td>
            <td> حالة الحضور </td>
            <td>الوظيفة</td>
            <td>اسم المستخدم</td>
            <td>اسم الموظف</td>
        </tr>
    </thead>
    <tbody>
        <!-- البيانات ستظهر هنا -->
    </tbody>
</table>

<script>
function exportex(){
    const userElement = document.getElementsByClassName('user')[0];
    const dateFromElement = document.getElementsByClassName('date-from')[0];
    const dateToElement = document.getElementsByClassName('date-to')[0];
    
    if(!userElement || !dateFromElement || !dateToElement){
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    const useratt = userElement.id;
    const dateFrom = dateFromElement.id;
    const dateTo = dateToElement.id;
    
    window.location.href = "addon/export_employee.php?useratt=" + useratt + "&dateFrom=" + dateFrom + "&dateTo=" + dateTo;
}

function secRun(){
    let sec = document.querySelector(".button_excel");
    sec.style.visibility = "visible";
}

let x;
let toast = document.getElementById("toast2");
p1 = document.querySelector(".p1");
p2 = document.querySelector(".p2");

function Warning(ts,ic,tx1,ss,icC,time) {
    clearTimeout(x);
    icon.className = icC;
    toast.style.borderRight = ts;
    icon.style.color = ic;
    p1.innerText = tx1;
    p2.style.fontSize = "16px";
    p2.innerText = ss;
    toast.style.transition = '1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition = '1s';
    x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
    }, time);
}

// دالة محسنة لتحديد الحالة العامة
function getOverallStatus(stat_employee, currentDate, leaveStart, leaveEnd) {
    // التحقق من حالة الإجازة أولاً إذا كانت التواريخ متوفرة
    if (leaveStart && leaveEnd && currentDate) {
        const current = new Date(currentDate);
        const start = new Date(leaveStart);
        const end = new Date(leaveEnd);
        
        if (current >= start && current <= end) {
            return {
                status: 'إجازة',
                color: 'status-leave',
                tooltip: 'الموظف في إجازة رسمية من ' + leaveStart + ' إلى ' + leaveEnd
            };
        }
    }
    
    // إذا كان stat_employee يحتوي على "إجازة" مباشرة
    if (stat_employee === 'إجازة') {
        return {
            status: 'إجازة',
            color: 'status-leave',
            tooltip: 'الموظف في إجازة'
        };
    }
    
    // إذا كان حاضر
    if (stat_employee === 'حاضر') {
        return {
            status: 'حاضر',
            color: 'status-present',
            tooltip: 'الموظف حاضر اليوم'
        };
    }
    
    // إذا كان غائب
    return {
        status: 'غائب',
        color: 'status-absent',
        tooltip: 'الموظف غائب اليوم'
    };
}
</script>

<?php
if(isset($_POST['myInput'])){
    if(!isset($_POST['acout_type']) || $_POST['acout_type'] == '0' || 
       empty($_POST['dateFrom']) || empty($_POST['dateTo'])){
        $msg1 = "! انتبه";
        $msg2 = "يرجى اختيار جميع الحقول";
        $iconC = "fa-solid fa-circle-info";
        $time = 5000;
        echo "<script> Warning('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','$time')</script>";
    } else {
        $user = $_POST['acout_type'];
        $dateFrom = $_POST['dateFrom'];
        $dateTo = $_POST['dateTo'];
        
        // التحقق من صحة التواريخ
        if($dateFrom > $dateTo){
            $msg1 = "! خطأ";
            $msg2 = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية";
            $iconC = "fa-solid fa-circle-exclamation";
            $time = 5000;
            echo "<script> Warning('8px solid rgb(243, 35, 82)','rgb(243, 35, 82)','$msg1','$msg2','$iconC','$time')</script>";
        } else {
            // استعلام محسن ومُصحح
            $sql = "SELECT 
                        users_tb.id_user, 
                        users_tb.user_name, 
                        employ_tb.f_name, 
                        employ_tb.job,
                        stat2.data_stat, 
                        stat2.stat_employee, 
                        stat2.leave_start_date, 
                        stat2.leave_end_date
                    FROM users_tb
                    INNER JOIN employ_tb ON users_tb.id_user = employ_tb.userID
                    INNER JOIN stat2 ON employ_tb.id_employ = stat2.id_employee
                    WHERE users_tb.id_user = '$user'
                    AND stat2.data_stat BETWEEN '$dateFrom' AND '$dateTo'
                    ORDER BY stat2.data_stat DESC";
            
            $resel = mysqli_query($con, $sql);
            
            if($resel && mysqli_num_rows($resel) > 0){
                $count = mysqli_num_rows($resel);
                $msg1 = "حضور";
                $msg2 = "$count : مجموع السجلات";
                $iconC = "fa-solid fa-circle-info";
                $time = 5000;
                echo "<script> Warning('8px solid rgb(34, 196, 242)','rgb(34, 196, 242)','$msg1','$msg2','$iconC','$time')</script>";
                echo "<script>secRun()</script>";

                while($row = mysqli_fetch_assoc($resel)){
                    // تحديد الحالة العامة بناءً على البيانات الفعلية
                    $overall_status = '';
                    $overall_color = '';
                    $overall_tooltip = '';
                    
                    // التحقق من حالة الإجازة أولاً
                    $is_on_leave = false;
                    if(!empty($row['leave_start_date']) && !empty($row['leave_end_date'])) {
                        $leave_start = new DateTime($row['leave_start_date']);
                        $leave_end = new DateTime($row['leave_end_date']);
                        $current_date = new DateTime($row['data_stat']);
                        
                        if($current_date >= $leave_start && $current_date <= $leave_end) {
                            $is_on_leave = true;
                        }
                    }
                    
                    // تحديد الحالة النهائية
                    if($row['stat_employee'] == 'إجازة' || $is_on_leave) {
                        $overall_status = 'إجازة';
                        $overall_color = 'status-leave';
                        $overall_tooltip = 'الموظف في إجازة';
                        if(!empty($row['leave_start_date']) && !empty($row['leave_end_date'])) {
                            $overall_tooltip .= ' من ' . $row['leave_start_date'] . ' إلى ' . $row['leave_end_date'];
                        }
                    } elseif($row['stat_employee'] == 'حاضر') {
                        $overall_status = 'حاضر';
                        $overall_color = 'status-present';
                        $overall_tooltip = 'الموظف حاضر في هذا اليوم';
                    } else {
                        $overall_status = 'غائب';
                        $overall_color = 'status-absent';
                        $overall_tooltip = 'الموظف غائب في هذا اليوم';
                    }
                    
                    echo "<script>
                        document.querySelector('#Table tbody').innerHTML += `
                        <tr>
                            <td class='date-from' id='{$dateFrom}' style='display:none;'></td>
                            <td class='date-to' id='{$dateTo}' style='display:none;'></td>
                            <td>{$row['data_stat']}</td>
                            <td>
                                <div class='status-tooltip'>
                                    <span class='{$overall_color}'>{$overall_status}</span>
                                    <span class='tooltiptext'>{$overall_tooltip}</span>
                                </div>
                            </td>
                            <td>{$row['job']}</td>
                            <td class='user' id='{$row['id_user']}'>{$row['user_name']}</td>
                            <td>{$row['f_name']}</td>
                        </tr>`;
                    </script>";
                }
            } else {
                $msg1 = "! لا يوجد";
                $msg2 = "لا توجد بيانات للفترة المحددة";
                $iconC = "fa-solid fa-circle-info";
                $time = 5000;
                echo "<script> Warning('8px solid rgb(243, 35, 82)','rgb(243, 35, 82)','$msg1','$msg2','$iconC','$time')</script>";
            }
        }
    }
}
?>

<script>
// إخفاء رسائل خطأ DataTables
$.fn.dataTable.ext.errMode = 'none';

$(document).ready(function() {
    // معالج الأخطاء لمنع ظهور التحذيرات
    $('#Table').on('error.dt', function(e, settings, techNote, message) {
        console.log('DataTables error: ', message);
        e.preventDefault(); // منع ظهور الرسالة للمستخدم
    });
    
    setTimeout(function() {
        try {
            var tableRows = $("#Table tbody tr");
            var hasValidData = tableRows.length > 0 && 
                              !tableRows.first().find('td').attr('colspan') &&
                              tableRows.first().find('td').length > 0;
            
            if (hasValidData && !$.fn.DataTable.isDataTable('#Table')) {
                $("#Table").DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
                    },
                    "order": [[ 2, "desc" ]],
                    "columnDefs": [
                        { "orderable": true, "targets": "_all" },
                        { "visible": false, "targets": [0, 1] }
                    ]
                });
            }
        } catch (error) {
            console.log('DataTables initialization error: ', error);
        }
    }, 1000);
});
</script>
</body>
</html>