<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin" || $_SESSION['user']->role==="Mod"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";
include "../Admin/add_notification.php";

$message = '';
$messageType = '';

// التحقق من رسالة الحذف
if(isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = "تم حذف الطلب بنجاح";
    $messageType = 'success';
}

// معالجة حذف الطلب
if(isset($_POST['delete_need'])){
    try {
        $need_id = $_POST['need_id'];

        // جلب معلومات الطلب قبل الحذف
        $need_info_query = "SELECT nr.*, u.user_name
                           FROM needs_requests nr
                           JOIN users_tb u ON nr.user_id = u.id_user
                           WHERE nr.id_need = ?";
        $stmt_info = $con->prepare($need_info_query);
        $stmt_info->bind_param("i", $need_id);
        $stmt_info->execute();
        $need_info = $stmt_info->get_result()->fetch_assoc();

        $stmt = $con->prepare("DELETE FROM needs_requests WHERE id_need = ?");
        $stmt->bind_param("i", $need_id);

        if($stmt->execute()) {
            // إعادة توجيه مع رسالة نجاح
            header("Location: manage_needs.php?deleted=1");
            exit();
        } else {
            header("Location: manage_needs.php?error=فشل في حذف الطلب");
            exit();
        }
    } catch (Exception $e) {
        header("Location: manage_needs.php?error=حدث خطأ في الحذف");
        exit();
    }
}

// التحقق من رسالة الخطأ
if(isset($_GET['error'])) {
    $message = "حدث خطأ: " . htmlspecialchars($_GET['error']);
    $messageType = 'error';
}

// معالجة الرد على الطلب
if(isset($_POST['respond_need'])){
    try {
        $need_id = $_POST['need_id'];
        $status = $_POST['status'];
        $admin_response = trim($_POST['admin_response']);
        $response_by = $_SESSION['user']->id_user;
        $response_date = date('Y-m-d H:i:s');

        // جلب معلومات الطلب والمستخدم
        $need_info_query = "SELECT nr.*, u.user_name
                           FROM needs_requests nr
                           JOIN users_tb u ON nr.user_id = u.id_user
                           WHERE nr.id_need = ?";
        $stmt_info = $con->prepare($need_info_query);
        $stmt_info->bind_param("i", $need_id);
        $stmt_info->execute();
        $need_info = $stmt_info->get_result()->fetch_assoc();

        $stmt = $con->prepare("UPDATE needs_requests SET status = ?, admin_response = ?, response_by = ?, response_date = ? WHERE id_need = ?");
        $stmt->bind_param("ssisi", $status, $admin_response, $response_by, $response_date, $need_id);

        if($stmt->execute()) {
            // إضافة إشعار تحديث حالة الطلب
            if($need_info) {
                addRequestStatusUpdateNotification($con, $need_info['user_name'], 'احتياج', $status, $need_id);
            }

            $message = "تم تحديث حالة الطلب بنجاح";
            $messageType = 'success';
        } else {
            $message = "حدث خطأ أثناء تحديث الطلب";
            $messageType = 'error';
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

// فلترة البيانات
$where_conditions = [];
$params = [];
$param_types = "";

if(isset($_GET['from_date']) && !empty($_GET['from_date'])) {
    $where_conditions[] = "nr.request_date >= ?";
    $params[] = $_GET['from_date'];
    $param_types .= "s";
}

if(isset($_GET['to_date']) && !empty($_GET['to_date'])) {
    $where_conditions[] = "nr.request_date <= ?";
    $params[] = $_GET['to_date'];
    $param_types .= "s";
}

if(isset($_GET['user_id']) && !empty($_GET['user_id'])) {
    $where_conditions[] = "nr.user_id = ?";
    $params[] = $_GET['user_id'];
    $param_types .= "i";
}

if(isset($_GET['status']) && !empty($_GET['status'])) {
    $where_conditions[] = "nr.status = ?";
    $params[] = $_GET['status'];
    $param_types .= "s";
}

$where_clause = "";
if(!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$query = "SELECT nr.*, u.user_name, ur.user_name as response_by_name 
          FROM needs_requests nr 
          JOIN users_tb u ON nr.user_id = u.id_user 
          LEFT JOIN users_tb ur ON nr.response_by = ur.id_user 
          $where_clause 
          ORDER BY nr.created_at DESC";

if(!empty($params)) {
    $stmt = $con->prepare($query);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $con->query($query);
}

// جلب قائمة المستخدمين للفلترة
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = $con->query($users_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاحتياجات</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .manage-container {
            max-width: 1400px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .filter-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-control, .form-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .filter-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }
        
        .need-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .need-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .need-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .need-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
            font-size: 14px;
        }
        
        .info-item i {
            color: #667eea;
            width: 16px;
        }
        
        .need-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .response-form {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .response-form h5 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-print {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-print:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }

        .btn-delete:hover {
            background: linear-gradient(45deg, #c82333, #bd2130);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="manage-container">
        <div class="page-header">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <h1 class="page-title">
                    <i class="fas fa-clipboard-list"></i>
                    إدارة الاحتياجات
                </h1>
                <button onclick="printNeedsReport()" class="filter-btn" style="background: linear-gradient(45deg, #28a745, #20c997);">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
            </div>
        </div>
        
        <?php if($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- فلترة البيانات -->
        <div class="filter-card">
            <h5><i class="fas fa-filter"></i> فلترة البيانات</h5>
            <form method="GET">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="from_date" class="form-control" value="<?php echo $_GET['from_date'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="to_date" class="form-control" value="<?php echo $_GET['to_date'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">المستخدم</label>
                        <select name="user_id" class="form-select">
                            <option value="">جميع المستخدمين</option>
                            <?php while($user = $users_result->fetch_assoc()): ?>
                                <option value="<?php echo $user['id_user']; ?>" 
                                    <?php echo (isset($_GET['user_id']) && $_GET['user_id'] == $user['id_user']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['user_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="قيد المراجعة" <?php echo (isset($_GET['status']) && $_GET['status'] == 'قيد المراجعة') ? 'selected' : ''; ?>>قيد المراجعة</option>
                            <option value="تم التوفير" <?php echo (isset($_GET['status']) && $_GET['status'] == 'تم التوفير') ? 'selected' : ''; ?>>تم التوفير</option>
                            <option value="لم يتوفر" <?php echo (isset($_GET['status']) && $_GET['status'] == 'لم يتوفر') ? 'selected' : ''; ?>>لم يتوفر</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="filter-btn">
                    <i class="fas fa-search"></i> فلترة
                </button>
                <a href="manage_needs.php" class="filter-btn" style="background: #6c757d; margin-right: 10px; text-decoration: none;">
                    <i class="fas fa-refresh"></i> إعادة تعيين
                </a>
            </form>
        </div>

        <!-- عرض الاحتياجات -->
        <?php if($result->num_rows > 0): ?>
            <?php while($need = $result->fetch_assoc()): ?>
                <div class="need-card">
                    <div class="need-header">
                        <h3 class="need-title"><?php echo htmlspecialchars($need['need_name']); ?></h3>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span class="status-badge status-<?php
                                echo $need['status'] == 'قيد المراجعة' ? 'pending' :
                                    ($need['status'] == 'تم التوفير' ? 'approved' : 'rejected');
                            ?>">
                                <?php echo $need['status']; ?>
                            </span>
                            <a href="print_need.php?id=<?php echo $need['id_need']; ?>" target="_blank"
                               class="action-btn btn-print" title="طباعة هذا الطلب">
                                <i class="fas fa-print"></i> طباعة
                            </a>
                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                <input type="hidden" name="need_id" value="<?php echo $need['id_need']; ?>">
                                <input type="hidden" name="delete_need" value="1">
                                <button type="submit" class="action-btn btn-delete" title="حذف الطلب">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="need-info">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>المستخدم: <?php echo htmlspecialchars($need['user_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($need['request_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-tag"></i>
                            <span>النوع: <?php echo $need['need_type']; ?></span>
                        </div>
                        <?php if($need['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($need['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if($need['response_by_name']): ?>
                            <div class="info-item">
                                <i class="fas fa-user-tie"></i>
                                <span>رد بواسطة: <?php echo htmlspecialchars($need['response_by_name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="need-details">
                        <strong>تفاصيل الاحتياج:</strong><br>
                        <?php echo nl2br(htmlspecialchars($need['need_details'])); ?>
                    </div>

                    <?php if($need['admin_response']): ?>
                        <div class="need-details" style="background: #e8f5e8; border-right-color: #28a745;">
                            <strong>رد الإدارة:</strong><br>
                            <?php echo nl2br(htmlspecialchars($need['admin_response'])); ?>
                        </div>
                    <?php endif; ?>

                    <?php if($need['status'] == 'قيد المراجعة'): ?>
                        <div class="response-form">
                            <h5><i class="fas fa-reply"></i> الرد على الطلب</h5>
                            <form method="POST">
                                <input type="hidden" name="need_id" value="<?php echo $need['id_need']; ?>">

                                <div class="form-group">
                                    <label class="form-label">حالة الطلب</label>
                                    <select name="status" class="form-select" required>
                                        <option value="">اختر الحالة</option>
                                        <option value="تم التوفير">تم التوفير</option>
                                        <option value="لم يتوفر">لم يتوفر</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">ملاحظات الإدارة</label>
                                    <textarea name="admin_response" class="form-control" rows="3"
                                        placeholder="اكتب ملاحظاتك هنا..." required></textarea>
                                </div>

                                <div class="btn-group">
                                    <button type="submit" name="respond_need" class="btn btn-success">
                                        <i class="fas fa-check"></i> إرسال الرد
                                    </button>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="need-card">
                <div style="text-align: center; padding: 50px; color: #6c757d;">
                    <i class="fas fa-clipboard-list" style="font-size: 64px; margin-bottom: 20px; color: #dee2e6;"></i>
                    <h3>لا توجد طلبات احتياج</h3>
                    <p>لا توجد طلبات احتياج تطابق معايير البحث المحددة</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // وظيفة طباعة التقرير الكامل
        function printNeedsReport() {
            const elementsToHide = document.querySelectorAll('.filter-btn, .response-form, .action-btn, button, form');
            elementsToHide.forEach(el => el.style.display = 'none');

            const printHeader = document.createElement('div');
            printHeader.innerHTML = '<div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;"><h1>تقرير طلبات الاحتياجات</h1><p>تاريخ الطباعة: ' + new Date().toLocaleDateString('ar-SA') + '</p><p>المدقق: <?php echo $_SESSION['user']->user_name; ?></p></div>';
            document.body.insertBefore(printHeader, document.body.firstChild);

            window.print();

            setTimeout(() => {
                elementsToHide.forEach(el => el.style.display = '');
                document.body.removeChild(printHeader);
            }, 1000);
        }
    </script>
</body>
</html>
