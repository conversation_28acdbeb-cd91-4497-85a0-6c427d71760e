<?php
// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF']);
?>

<div class="sidebar">
    <div class="sidebar-header">
        <h3>🎓 أكاديمية كيدز</h3>
        <p>لوحة التحكم الإدارية</p>
    </div>
    
    <div class="sidebar-menu">
        <a href="student_app_control.php" class="menu-item <?php echo ($current_page == 'student_app_control.php') ? 'active' : ''; ?>">
            <i class="fas fa-home"></i>
            الرئيسية
        </a>
        <a href="admin_settings.php" class="menu-item <?php echo ($current_page == 'admin_settings.php') ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i>
            إعدادات التطبيق
        </a>
        <a href="admin_news.php" class="menu-item <?php echo ($current_page == 'admin_news.php') ? 'active' : ''; ?>">
            <i class="fas fa-newspaper"></i>
            إدارة الأخبار
        </a>
        <a href="admin_activities.php" class="menu-item <?php echo ($current_page == 'admin_activities.php') ? 'active' : ''; ?>">
            <i class="fas fa-calendar-check"></i>
            إدارة الأنشطة
        </a>
        <a href="admin_students.php" class="menu-item <?php echo ($current_page == 'admin_students.php') ? 'active' : ''; ?>">
            <i class="fas fa-users"></i>
            إدارة الطلاب
        </a>
        <a href="admin_contact_messages.php" class="menu-item <?php echo ($current_page == 'admin_contact_messages.php') ? 'active' : ''; ?>">
            <i class="fas fa-comments"></i>
            رسائل التواصل
        </a>
        <a href="admin_leave_requests.php" class="menu-item <?php echo ($current_page == 'admin_leave_requests.php') ? 'active' : ''; ?>">
            <i class="fas fa-calendar-minus"></i>
            طلبات الإجازة
        </a>
        <a href="admin_contact.php" class="menu-item <?php echo ($current_page == 'admin_contact.php') ? 'active' : ''; ?>">
            <i class="fas fa-phone"></i>
            معلومات التواصل
        </a>
        <a href="admin_app_content.php" class="menu-item <?php echo ($current_page == 'admin_app_content.php') ? 'active' : ''; ?>">
            <i class="fas fa-mobile-alt"></i>
            محتوى التطبيق
        </a>
        <a href="admin_notifications.php" class="menu-item <?php echo ($current_page == 'admin_notifications.php') ? 'active' : ''; ?>">
            <i class="fas fa-bell"></i>
            الإشعارات
        </a>
        <a href="admin_reports.php" class="menu-item <?php echo ($current_page == 'admin_reports.php') ? 'active' : ''; ?>">
            <i class="fas fa-chart-bar"></i>
            التقارير
        </a>
        <a href="admin_backup.php" class="menu-item <?php echo ($current_page == 'admin_backup.php') ? 'active' : ''; ?>">
            <i class="fas fa-database"></i>
            النسخ الاحتياطي
        </a>

        <a href="api/app_api.php?endpoint=app_sections" class="menu-item" target="_blank">
            <i class="fas fa-code"></i>
            API التطبيق
        </a>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s ease;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

.sidebar-menu {
    padding: 1rem 0;
}

.menu-item {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    position: relative;
}

.menu-item:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
    padding-right: 2rem;
}

.menu-item.active {
    background: rgba(255,255,255,0.2);
    border-right: 4px solid white;
    font-weight: bold;
}

.menu-item i {
    width: 20px;
    margin-left: 1rem;
}

.main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
    background: #f8f9fa;
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
}
</style>
