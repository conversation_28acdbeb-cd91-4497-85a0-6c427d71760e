<?php
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>🔧 اختبار الاتصال المحدث بقاعدة البيانات</h2>";
echo "<hr>";

// اختبار ملف dbcon.php المحدث
echo "<h3>1. اختبار ملف dbcon.php المحدث:</h3>";

try {
    include "addon/dbcon.php";
    
    if (isset($con) && $con instanceof mysqli) {
        echo "<p style='color: green;'>✅ تم تضمين ملف dbcon.php بنجاح</p>";
        echo "<p style='color: green;'>✅ متغير \$con موجود ومن نوع mysqli</p>";
        
        // اختبار الاتصال
        if ($con->ping()) {
            echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نشط</p>";
            
            // اختبار استعلام بسيط
            $test_query = "SELECT COUNT(*) as total_users FROM users_tb";
            $result = mysqli_query($con, $test_query);
            
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                echo "<p style='color: green;'>✅ استعلام قاعدة البيانات يعمل</p>";
                echo "<p><strong>عدد المستخدمين في النظام: " . $row['total_users'] . "</strong></p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . mysqli_error($con) . "</p>";
            }
            
            // اختبار جداول النظام
            echo "<h4>اختبار الجداول:</h4>";
            
            // جدول الإيرادات
            $revenue_test = "SELECT COUNT(*) as count, SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
            $revenue_result = mysqli_query($con, $revenue_test);
            if ($revenue_result) {
                $revenue_data = mysqli_fetch_assoc($revenue_result);
                echo "<p style='color: green;'>✅ جدول الإيرادات (stud_pay): " . $revenue_data['count'] . " سجل، المجموع: " . number_format($revenue_data['total']) . " د.ع</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في جدول الإيرادات: " . mysqli_error($con) . "</p>";
            }
            
            // جدول المصروفات
            $expenses_test = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
            $expenses_result = mysqli_query($con, $expenses_test);
            if ($expenses_result) {
                $expenses_data = mysqli_fetch_assoc($expenses_result);
                echo "<p style='color: green;'>✅ جدول المصروفات (depit_tb): " . $expenses_data['count'] . " سجل، المجموع: " . number_format($expenses_data['total']) . " د.ع</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في جدول المصروفات: " . mysqli_error($con) . "</p>";
            }
            
            // جدول الرواتب
            $salaries_test = "SELECT COUNT(*) as count, SUM(salary) as total FROM employ_tb WHERE salary > 0";
            $salaries_result = mysqli_query($con, $salaries_test);
            if ($salaries_result) {
                $salaries_data = mysqli_fetch_assoc($salaries_result);
                echo "<p style='color: green;'>✅ جدول الرواتب (employ_tb): " . $salaries_data['count'] . " سجل، المجموع: " . number_format($salaries_data['total']) . " د.ع</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في جدول الرواتب: " . mysqli_error($con) . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ الاتصال بقاعدة البيانات غير نشط</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ متغير \$con غير موجود أو غير صحيح</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تضمين ملف dbcon.php: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// اختبار صفحة الحسابات
echo "<h3>2. اختبار صفحة الحسابات المحدثة:</h3>";

echo "<p><a href='acounting.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 فتح صفحة الحسابات المحدثة</a></p>";

echo "<hr>";

// معلومات قاعدة البيانات
echo "<h3>3. معلومات قاعدة البيانات:</h3>";

if (isset($con)) {
    echo "<ul>";
    echo "<li><strong>اسم الخادم:</strong> " . $con->host_info . "</li>";
    echo "<li><strong>إصدار MySQL:</strong> " . $con->server_info . "</li>";
    echo "<li><strong>اسم قاعدة البيانات:</strong> kidzrcle_rwda</li>";
    echo "<li><strong>ترميز الأحرف:</strong> " . $con->character_set_name() . "</li>";
    echo "</ul>";
}

echo "<hr>";

// اختبار محاكاة صفحة الحسابات
echo "<h3>4. اختبار محاكاة صفحة الحسابات:</h3>";

if (isset($con)) {
    // حساب الإحصائيات مثل صفحة الحسابات
    $stats = [
        'revenue' => 0,
        'expenses' => 0,
        'salaries' => 0,
        'revenue_count' => 0,
        'expenses_count' => 0,
        'salaries_count' => 0
    ];

    // إجمالي الإيرادات
    $revenue_query = "SELECT COUNT(*) as count, SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
    $revenue_result = mysqli_query($con, $revenue_query);
    if ($revenue_result && $row = mysqli_fetch_assoc($revenue_result)) {
        $stats['revenue'] = $row['total'] ? intval($row['total']) : 0;
        $stats['revenue_count'] = $row['count'] ? intval($row['count']) : 0;
    }

    // إجمالي المصروفات
    $expenses_query = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
    $expenses_result = mysqli_query($con, $expenses_query);
    if ($expenses_result && $row = mysqli_fetch_assoc($expenses_result)) {
        $stats['expenses'] = $row['total'] ? intval($row['total']) : 0;
        $stats['expenses_count'] = $row['count'] ? intval($row['count']) : 0;
    }

    // إجمالي الرواتب
    $salaries_query = "SELECT COUNT(*) as count, SUM(salary) as total FROM employ_tb WHERE salary > 0";
    $salaries_result = mysqli_query($con, $salaries_query);
    if ($salaries_result && $row = mysqli_fetch_assoc($salaries_result)) {
        $stats['salaries'] = $row['total'] ? intval($row['total']) : 0;
        $stats['salaries_count'] = $row['count'] ? intval($row['count']) : 0;
    }

    // حساب صافي الربح
    $net_profit = $stats['revenue'] - $stats['expenses'] - $stats['salaries'];

    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 الإحصائيات المالية:</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h5 style='color: #28a745; margin: 0;'>الإيرادات</h5>";
    echo "<p style='font-size: 1.5rem; font-weight: bold; margin: 5px 0;'>" . number_format($stats['revenue']) . " د.ع</p>";
    echo "<small>" . $stats['revenue_count'] . " معاملة</small>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
    echo "<h5 style='color: #dc3545; margin: 0;'>المصروفات</h5>";
    echo "<p style='font-size: 1.5rem; font-weight: bold; margin: 5px 0;'>" . number_format($stats['expenses']) . " د.ع</p>";
    echo "<small>" . $stats['expenses_count'] . " معاملة</small>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
    echo "<h5 style='color: #ffc107; margin: 0;'>الرواتب</h5>";
    echo "<p style='font-size: 1.5rem; font-weight: bold; margin: 5px 0;'>" . number_format($stats['salaries']) . " د.ع</p>";
    echo "<small>" . $stats['salaries_count'] . " موظف</small>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;'>";
    echo "<h5 style='color: #007bff; margin: 0;'>صافي الربح</h5>";
    echo "<p style='font-size: 1.5rem; font-weight: bold; margin: 5px 0; color: " . ($net_profit >= 0 ? '#28a745' : '#dc3545') . ";'>" . number_format($net_profit) . " د.ع</p>";
    echo "<small>الربح الصافي</small>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    if ($stats['revenue'] > 0 || $stats['expenses'] > 0 || $stats['salaries'] > 0) {
        echo "<p style='color: green; font-size: 1.2rem; font-weight: bold;'>✅ صفحة الحسابات ستعمل بشكل مثالي!</p>";
    } else {
        echo "<p style='color: orange; font-size: 1.2rem; font-weight: bold;'>⚠️ لا توجد بيانات في قاعدة البيانات</p>";
    }
}

if (isset($con)) {
    mysqli_close($con);
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
    direction: rtl;
}
h2, h3, h4 {
    color: #333;
}
p {
    margin: 10px 0;
}
hr {
    margin: 20px 0;
    border: 1px solid #ddd;
}
</style>
