<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['student'])) {
    echo "<script>
        alert('يرجى تسجيل الدخول أولاً');
        window.location.href = 'login.php';
    </script>";
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

$student = $_SESSION['student'];
$student_id = $student->id;
$student_name = $student->name;

// تعيين عدادات الإشعارات إلى صفر (مؤقتاً)
$notifications_count = [
    'news' => 0,
    'activity' => 0,
    'holiday' => 0,
    'message' => 0,
    'leave' => 0,
    'schedule' => 0
];

// جلب معلومات الطالب بطريقة مبسطة
$sql = "SELECT s.*, sp.date_exp, sp.cash_stud, u.user_name
        FROM stud_tb s
        LEFT JOIN stud_pay sp ON s.id = sp.id_stud
        LEFT JOIN users_tb u ON s.userID = u.id_user
        WHERE s.id = $student_id";
$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $student_info = mysqli_fetch_assoc($result);
} else {
    // إذا لم توجد معلومات مفصلة، استخدم البيانات من الجلسة
    $student_info = [
        'name' => $student->name,
        'age' => $student->age ?? 'غير محدد',
        'sex' => $student->sex ?? 'غير محدد',
        'catg' => $student->catg ?? 'غير محدد',
        'p_name' => $student->p_name ?? 'غير محدد',
        'p_phone' => $student->p_phone ?? 'غير محدد',
        'date_exp' => null,
        'cash_stud' => null,
        'user_name' => $student->user_name ?? 'غير محدد'
    ];
}

// حساب حالة الاشتراك
$subscription_status = 'غير محدد';
$days_remaining = 0;
if (!empty($student_info['date_exp'])) {
    $expiry_date = new DateTime($student_info['date_exp']);
    $current_date = new DateTime();
    $interval = $current_date->diff($expiry_date);
    $days_remaining = $expiry_date > $current_date ? $interval->days : -$interval->days;
    
    if ($days_remaining > 10) {
        $subscription_status = 'نشط';
    } elseif ($days_remaining > 0) {
        $subscription_status = 'قريب الانتهاء';
    } else {
        $subscription_status = 'منتهي';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#ff6b6b">
    <title>🎓 أكاديمية كيدز - <?php echo htmlspecialchars($student_name); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/app-style.css">

    <!-- نظام الإعدادات الحقيقي -->
    <script src="js/app-settings.js"></script>
    <style>
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #4ecdc4;
            --main-color: #ff6b6b;
            --accent-color: #4ecdc4;
            --header-gradient: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            --background-gradient: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-gradient);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23fff" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="%23fff" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="1" fill="%23fff" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/></circle></svg>') repeat;
            pointer-events: none;
            z-index: 1;
        }

        /* شريط التطبيق العلوي */
        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: var(--header-gradient);
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-bottom: 3px solid rgba(255, 255, 255, 0.3);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .header-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .header-btn:hover, .header-btn:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* المحتوى الرئيسي */
        .app-content {
            margin-top: 70px;
            padding: 1rem;
            position: relative;
            z-index: 2;
            min-height: calc(100vh - 140px);
            padding-bottom: 80px;
        }

        /* بطاقة الترحيب */
        .welcome-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 3px solid rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .student-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--header-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            animation: bounce 2s ease-in-out infinite;
            border: 4px solid rgba(255, 255, 255, 0.8);
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .welcome-text {
            text-align: center;
            position: relative;
            z-index: 3;
        }

        .welcome-text h2 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .welcome-text p {
            color: #7f8c8d;
            font-size: 1rem;
            margin: 0;
        }

        /* بطاقة المعلومات المدمجة */
        .info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .info-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1rem;
            padding-bottom: 0.8rem;
            border-bottom: 2px dashed #ff6b6b;
        }

        .info-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .info-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.8rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.05), rgba(78, 205, 196, 0.05));
            border-radius: 15px;
            border: 2px solid rgba(255, 107, 107, 0.1);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.2);
            border-color: rgba(255, 107, 107, 0.3);
        }

        .info-label {
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-value {
            color: #34495e;
            font-weight: 600;
        }

        /* حالة الاشتراك */
        .subscription-status {
            text-align: center;
            padding: 1rem;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            border-radius: 15px;
            margin-bottom: 1rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1rem;
            margin-bottom: 0.8rem;
            animation: pulse 2s infinite;
        }

        .status-active {
            background: linear-gradient(45deg, #51cf66, #40c057);
            color: white;
            box-shadow: 0 5px 15px rgba(81, 207, 102, 0.4);
        }

        .status-warning {
            background: linear-gradient(45deg, #ffd43b, #fab005);
            color: white;
            box-shadow: 0 5px 15px rgba(255, 212, 59, 0.4);
        }

        .status-expired {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* شبكة التطبيقات */
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .app-icon {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: inherit;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 3px solid rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
        }

        .app-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }

        .app-icon:hover::before {
            opacity: 1;
            animation: shine 0.5s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .app-icon:hover, .app-icon:active {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            color: inherit;
        }

        .app-icon:active {
            transform: translateY(-2px) scale(0.98);
        }

        .icon-wrapper {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: iconFloat 3s ease-in-out infinite;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
            border: 2px solid white;
            animation: pulse 2s infinite;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
        }

        .nav-icon-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            font-weight: bold;
            border: 2px solid white;
            animation: pulse 2s infinite;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        /* ألوان مختلفة لكل تطبيق */
        .app-icon:nth-child(1) .icon-wrapper { background: linear-gradient(45deg, #ff6b6b, #ee5a52); }
        .app-icon:nth-child(2) .icon-wrapper { background: linear-gradient(45deg, #4ecdc4, #44a08d); }
        .app-icon:nth-child(3) .icon-wrapper { background: linear-gradient(45deg, #45b7d1, #96c93d); }
        .app-icon:nth-child(4) .icon-wrapper { background: linear-gradient(45deg, #f093fb, #f5576c); }
        .app-icon:nth-child(5) .icon-wrapper { background: linear-gradient(45deg, #4facfe, #00f2fe); }
        .app-icon:nth-child(6) .icon-wrapper { background: linear-gradient(45deg, #43e97b, #38f9d7); }

        .app-title {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1rem;
            font-weight: bold;
        }

        .app-subtitle {
            color: #7f8c8d;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* شريط التنقل السفلي */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 3px solid rgba(255, 107, 107, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #7f8c8d;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 15px;
            min-width: 60px;
        }

        .nav-item.active, .nav-item:hover {
            color: var(--primary-color);
            background: rgba(255, 107, 107, 0.1);
            transform: translateY(-2px);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .nav-item span {
            font-size: 0.7rem;
            font-weight: 600;
        }

        /* عناصر متحركة للأطفال */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-emoji {
            position: absolute;
            font-size: 1.5rem;
            animation: float 6s ease-in-out infinite;
            opacity: 0.6;
        }

        .floating-emoji:nth-child(1) { top: 15%; left: 10%; animation-delay: 0s; }
        .floating-emoji:nth-child(2) { top: 25%; right: 15%; animation-delay: 1s; }
        .floating-emoji:nth-child(3) { top: 65%; left: 5%; animation-delay: 2s; }
        .floating-emoji:nth-child(4) { top: 75%; right: 10%; animation-delay: 3s; }
        .floating-emoji:nth-child(5) { top: 45%; left: 20%; animation-delay: 4s; }
        .floating-emoji:nth-child(6) { top: 85%; right: 25%; animation-delay: 5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(5deg); }
            50% { transform: translateY(-10px) rotate(-5deg); }
            75% { transform: translateY(-15px) rotate(3deg); }
        }

        /* استجابة للهواتف المحمولة */
        @media (max-width: 768px) {
            .app-header h1 {
                font-size: 1rem;
            }

            .welcome-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .student-avatar {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .welcome-text h2 {
                font-size: 1.3rem;
            }

            .info-card {
                padding: 1rem;
            }

            .info-item {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
                padding: 0.8rem;
            }

            .apps-grid {
                gap: 0.8rem;
            }

            .app-icon {
                padding: 1rem;
            }

            .icon-wrapper {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .app-title {
                font-size: 0.9rem;
            }

            .app-subtitle {
                font-size: 0.75rem;
            }

            .floating-emoji {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .app-content {
                padding: 0.5rem;
            }

            .welcome-card {
                padding: 0.8rem;
            }

            .info-card {
                padding: 0.8rem;
            }

            .apps-grid {
                gap: 0.6rem;
            }

            .app-icon {
                padding: 0.8rem;
            }

            .icon-wrapper {
                width: 45px;
                height: 45px;
                font-size: 1.3rem;
            }
        }

        /* تأثيرات الدخول */
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسينات الأداء */
        .gpu-accelerated {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000;
        }

        /* تأثيرات اللمس للهواتف */
        @media (hover: none) and (pointer: coarse) {
            .app-icon:active {
                transform: scale(0.95);
                transition: transform 0.1s;
            }

            .header-btn:active {
                transform: scale(0.9);
                transition: transform 0.1s;
            }

            .nav-item:active {
                transform: scale(0.95);
                transition: transform 0.1s;
            }
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingScreen" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        color: white;
        text-align: center;
    ">
        <div style="font-size: 4rem; margin-bottom: 1rem; animation: bounce 1s infinite;">🌟</div>
        <h2 style="margin-bottom: 1rem; font-weight: bold;">روضة الأطفال</h2>
        <div style="width: 50px; height: 50px; border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        <p style="margin-top: 1rem; opacity: 0.8;">جاري تحميل التطبيق...</p>
    </div>

    <!-- عناصر متحركة للأطفال -->
    <div class="floating-elements">
        <div class="floating-emoji">🌟</div>
        <div class="floating-emoji">🎈</div>
        <div class="floating-emoji">🦋</div>
        <div class="floating-emoji">🌈</div>
        <div class="floating-emoji">🎨</div>
        <div class="floating-emoji">🧸</div>
    </div>

    <!-- شريط التطبيق العلوي -->
    <div class="app-header">
        <h1 class="app-title">🎓 أكاديمية كيدز</h1>
        <div class="header-actions">
            <a href="profile.php" class="header-btn" title="الملف الشخصي">
                <i class="fas fa-user"></i>
            </a>
            <a href="logout.php" class="header-btn" title="تسجيل الخروج">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="app-content">
        <!-- بطاقة الترحيب -->
        <div class="welcome-card animate__animated animate__fadeInDown">
            <div class="student-avatar">
                <?php
                $first_letter = mb_substr($student_name, 0, 1, 'UTF-8');
                echo $first_letter;
                ?>
            </div>
            <div class="welcome-text">
                <h2>مرحباً <?php echo htmlspecialchars($student_name); ?>! 🎉</h2>
                <p>أهلاً وسهلاً بك في تطبيق الروضة</p>
            </div>
        </div>

        <!-- بطاقة المعلومات المدمجة -->
        <div class="info-card animate__animated animate__fadeInUp">
            <div class="info-header">
                <div class="info-icon">
                    <i class="fas fa-id-card"></i>
                </div>
                <h3 class="info-title">معلوماتي</h3>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-user"></i> الاسم
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($student_info['name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-birthday-cake"></i> العمر
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($student_info['age']); ?> سنة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-graduation-cap"></i> المرحلة
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($student_info['catg']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-user-friends"></i> ولي الأمر
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($student_info['p_name']); ?></span>
                </div>
            </div>

            <!-- حالة الاشتراك -->
            <div class="subscription-status">
                <div class="status-badge status-<?php echo $subscription_status == 'نشط' ? 'active' : ($subscription_status == 'قريب الانتهاء' ? 'warning' : 'expired'); ?>">
                    <?php
                    $status_icon = $subscription_status == 'نشط' ? '✅' : ($subscription_status == 'قريب الانتهاء' ? '⚠️' : '❌');
                    echo $status_icon . ' ' . $subscription_status;
                    ?>
                </div>
                <?php if (!empty($student_info['date_exp'])): ?>
                    <div style="font-size: 0.9rem; color: #7f8c8d;">
                        <p><strong>📅 ينتهي في:</strong> <?php echo date('Y/m/d', strtotime($student_info['date_exp'])); ?></p>
                        <p><strong>⏰ الأيام المتبقية:</strong> <?php echo $days_remaining > 0 ? $days_remaining : 0; ?> يوم</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- شبكة التطبيقات -->
        <div class="apps-grid">
            <a href="request_leave.php" class="app-icon ripple-effect glow-effect" data-app="leave">
                <div class="icon-wrapper">
                    <i class="fas fa-calendar-times"></i>
                    <?php if ($notifications_count['leave'] > 0): ?>
                        <span class="notification-badge"><?php echo $notifications_count['leave']; ?></span>
                    <?php endif; ?>
                </div>
                <div class="app-title">طلب إجازة</div>
                <div class="app-subtitle">تقديم طلب إجازة جديد</div>
            </a>

            <a href="my_leaves.php" class="app-icon ripple-effect glow-effect" data-app="my-leaves">
                <div class="icon-wrapper">
                    <i class="fas fa-list-alt"></i>
                </div>
                <div class="app-title">إجازاتي</div>
                <div class="app-subtitle">عرض طلبات الإجازة</div>
            </a>

            <a href="holidays.php" class="app-icon ripple-effect glow-effect" data-app="holidays">
                <div class="icon-wrapper">
                    <i class="fas fa-calendar-day"></i>
                    <?php if ($notifications_count['holiday'] > 0): ?>
                        <span class="notification-badge"><?php echo $notifications_count['holiday']; ?></span>
                    <?php endif; ?>
                </div>
                <div class="app-title">العطل الرسمية</div>
                <div class="app-subtitle">عرض العطل والإجازات</div>
            </a>

            <a href="news.php" class="app-icon ripple-effect glow-effect news-section" data-app="news">
                <div class="icon-wrapper">
                    <i class="fas fa-newspaper"></i>
                    <?php if ($notifications_count['news'] > 0): ?>
                        <span class="notification-badge"><?php echo $notifications_count['news']; ?></span>
                    <?php endif; ?>
                </div>
                <div class="app-title">الأخبار</div>
                <div class="app-subtitle">آخر الأخبار والإعلانات</div>
            </a>

            <a href="contact.php" class="app-icon ripple-effect glow-effect contact-section" data-app="contact">
                <div class="icon-wrapper">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="app-title">التواصل</div>
                <div class="app-subtitle">التواصل مع الإدارة</div>
            </a>

            <a href="games.php" class="app-icon ripple-effect glow-effect games-section" data-app="games">
                <div class="icon-wrapper">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="app-title">الألعاب</div>
                <div class="app-subtitle">ألعاب تعليمية ممتعة</div>
            </a>
        </div>
    </div>

    <!-- شريط التنقل السفلي -->
    <div class="bottom-nav">
        <a href="index.php" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="activities.php" class="nav-item activities-section">
            <div class="nav-icon-wrapper">
                <i class="fas fa-palette"></i>
                <?php if ($notifications_count['activity'] > 0): ?>
                    <span class="nav-notification-badge"><?php echo $notifications_count['activity']; ?></span>
                <?php endif; ?>
            </div>
            <span>الأنشطة</span>
        </a>
        <a href="schedule.php" class="nav-item">
            <div class="nav-icon-wrapper">
                <i class="fas fa-calendar"></i>
                <?php if ($notifications_count['schedule'] > 0): ?>
                    <span class="nav-notification-badge"><?php echo $notifications_count['schedule']; ?></span>
                <?php endif; ?>
            </div>
            <span>الجدول</span>
        </a>
        <a href="messages.php" class="nav-item">
            <div class="nav-icon-wrapper">
                <i class="fas fa-comments"></i>
                <?php if ($notifications_count['message'] > 0): ?>
                    <span class="nav-notification-badge"><?php echo $notifications_count['message']; ?></span>
                <?php endif; ?>
            </div>
            <span>الرسائل</span>
        </a>
        <a href="news.php" class="nav-item news-section">
            <div class="nav-icon-wrapper">
                <i class="fas fa-newspaper"></i>
                <?php if ($notifications_count['news'] > 0): ?>
                    <span class="nav-notification-badge"><?php echo $notifications_count['news']; ?></span>
                <?php endif; ?>
            </div>
            <span>الأخبار</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات تطبيق الهاتف المحمول
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء شاشة التحميل
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.transition = 'opacity 0.5s ease';
                    setTimeout(() => {
                        loadingScreen.remove();
                    }, 500);
                }
            }, 2000);
            // تأثيرات الدخول
            setTimeout(() => {
                const welcomeCard = document.querySelector('.welcome-card');
                if (welcomeCard) {
                    welcomeCard.classList.add('animate__animated', 'animate__fadeInDown');
                }
            }, 200);

            setTimeout(() => {
                const infoCard = document.querySelector('.info-card');
                if (infoCard) {
                    infoCard.classList.add('animate__animated', 'animate__fadeInUp');
                }
            }, 400);

            // تأثيرات التطبيقات
            const appIcons = document.querySelectorAll('.app-icon');
            appIcons.forEach((icon, index) => {
                setTimeout(() => {
                    icon.classList.add('animate__animated', 'animate__zoomIn');
                }, 600 + (index * 100));
            });

            // تأثيرات اللمس للهواتف
            if (isMobileDevice()) {
                addTouchEffects();
                addHapticFeedback();
            }

            // تأثيرات الخلفية المتحركة
            createFloatingShapes();

            // تأثيرات الأيقونات
            animateIcons();

            // تأثيرات التطبيقات المتقدمة
            addAppInteractions();

            // تحديث الحالة
            updateAppStatus();

            // رسالة ترحيب ممتعة
            setTimeout(function() {
                if (localStorage.getItem('welcomed_app') !== 'true') {
                    showWelcomeMessage();
                    localStorage.setItem('welcomed_app', 'true');
                }
            }, 2000);
        });

        // فحص إذا كان الجهاز هاتف محمول
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
        }

        // إضافة تأثيرات اللمس
        function addTouchEffects() {
            const touchElements = document.querySelectorAll('.app-icon, .header-btn, .nav-item');

            touchElements.forEach(element => {
                element.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.95)';
                    this.style.transition = 'transform 0.1s';
                });

                element.addEventListener('touchend', function(e) {
                    this.style.transform = 'scale(1)';
                    setTimeout(() => {
                        this.style.transition = '';
                    }, 100);
                });
            });
        }

        // إنشاء أشكال متحركة في الخلفية
        function createFloatingShapes() {
            const shapes = ['⭐', '🌟', '✨', '💫', '🎈', '🎨', '🦋', '🌈'];

            setInterval(() => {
                if (document.querySelectorAll('.floating-shape').length < 3) {
                    const shape = document.createElement('div');
                    shape.className = 'floating-shape';
                    shape.textContent = shapes[Math.floor(Math.random() * shapes.length)];
                    shape.style.cssText = `
                        position: fixed;
                        font-size: ${Math.random() * 15 + 10}px;
                        left: ${Math.random() * 100}vw;
                        top: 100vh;
                        pointer-events: none;
                        z-index: 1;
                        animation: floatUp 6s linear forwards;
                        opacity: 0.6;
                    `;

                    document.body.appendChild(shape);

                    setTimeout(() => {
                        if (shape.parentNode) {
                            shape.remove();
                        }
                    }, 6000);
                }
            }, 4000);
        }

        // تحريك الأيقونات
        function animateIcons() {
            const iconWrappers = document.querySelectorAll('.icon-wrapper');

            iconWrappers.forEach((wrapper, index) => {
                setTimeout(() => {
                    wrapper.style.animation = `iconFloat 3s ease-in-out infinite`;
                    wrapper.style.animationDelay = `${index * 0.2}s`;
                }, 1000);
            });
        }

        // رسالة ترحيب تفاعلية
        function showWelcomeMessage() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.5s ease;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                padding: 2rem;
                border-radius: 25px;
                text-align: center;
                color: white;
                max-width: 300px;
                animation: bounceIn 0.5s ease;
            `;

            content.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🎉</div>
                <h2 style="margin-bottom: 1rem;">مرحباً بك!</h2>
                <p style="margin-bottom: 1.5rem;">أهلاً وسهلاً بك في تطبيق الروضة</p>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="background: white; color: #ff6b6b; border: none; padding: 0.8rem 1.5rem; border-radius: 15px; font-weight: bold; cursor: pointer;">
                    ابدأ الاستكشاف! 🚀
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        // إضافة تأثيرات الاهتزاز للهواتف
        function addHapticFeedback() {
            const appIcons = document.querySelectorAll('.app-icon');

            appIcons.forEach(icon => {
                icon.addEventListener('touchstart', function() {
                    // اهتزاز خفيف للهواتف التي تدعمه
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                });
            });
        }

        // تأثيرات التطبيقات المتقدمة
        function addAppInteractions() {
            const appIcons = document.querySelectorAll('.app-icon');

            appIcons.forEach((icon, index) => {
                // تأثير الدخول المتدرج
                setTimeout(() => {
                    icon.classList.add('scale-in');
                }, index * 100);

                // تأثيرات التمرير
                icon.addEventListener('mouseenter', function() {
                    if (!isMobileDevice()) {
                        this.classList.add('glow-effect', 'shadow-dance');
                        this.querySelector('.icon-wrapper').classList.add('gentle-spin');
                    }
                });

                icon.addEventListener('mouseleave', function() {
                    if (!isMobileDevice()) {
                        this.classList.remove('glow-effect', 'shadow-dance');
                        this.querySelector('.icon-wrapper').classList.remove('gentle-spin');
                    }
                });

                // تأثير النقر
                icon.addEventListener('click', function(e) {
                    // تأثير الاهتزاز
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }

                    // تأثير بصري بسيط
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        }

        // تحديث حالة التطبيق
        function updateAppStatus() {
            // تحديث الوقت في الشريط العلوي
            setInterval(() => {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-EG', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const header = document.querySelector('.app-header h1');
                if (header) {
                    const originalText = header.dataset.originalText || header.textContent;
                    header.dataset.originalText = originalText;
                    header.innerHTML = `${originalText} <small style="font-size: 0.8em; opacity: 0.8;">${timeString}</small>`;
                }
            }, 60000); // تحديث كل دقيقة

            // تحديث حالة الاتصال
            window.addEventListener('online', function() {
                showConnectionStatus('متصل', 'success');
            });

            window.addEventListener('offline', function() {
                showConnectionStatus('غير متصل', 'warning');
            });
        }

        // عرض حالة الاتصال
        function showConnectionStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#51cf66' : '#ffd43b'};
                color: white;
                padding: 0.8rem 1.5rem;
                border-radius: 25px;
                font-weight: bold;
                z-index: 10000;
                animation: slideInDown 0.5s ease;
            `;
            statusDiv.textContent = message;

            document.body.appendChild(statusDiv);

            setTimeout(() => {
                statusDiv.style.animation = 'slideInUp 0.5s ease reverse';
                setTimeout(() => {
                    statusDiv.remove();
                }, 500);
            }, 3000);
        }

        // إضافة CSS للتأثيرات الإضافية
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 0.6;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }

            @keyframes iconFloat {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-5px); }
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes bounceIn {
                0% {
                    transform: scale(0.3);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.05);
                }
                70% {
                    transform: scale(0.9);
                }
                100% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            .floating-shape {
                user-select: none;
                -webkit-user-select: none;
            }

            /* تحسينات الأداء */
            .app-icon, .header-btn, .nav-item {
                will-change: transform;
            }

            /* تأثيرات الحالة النشطة */
            .nav-item.active {
                animation: activeGlow 2s ease-in-out infinite;
            }

            @keyframes activeGlow {
                0%, 100% { box-shadow: 0 0 5px rgba(255, 107, 107, 0.3); }
                50% { box-shadow: 0 0 15px rgba(255, 107, 107, 0.6); }
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes slideInUp {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-30px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
