<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "../db_connection.php";

// الحصول على الفترة المطلوبة
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-t');

// استعلام المصروفات التفصيلية
$expenses_query = "
    SELECT 
        depit_tb.*,
        users_tb.user_name,
        DATE(depit_tb.depit_date) as expense_date
    FROM depit_tb 
    INNER JOIN users_tb ON depit_tb.userID = users_tb.id_user
    WHERE depit_tb.depit_cash > 0
    AND DATE(depit_tb.depit_date) BETWEEN '$date_from' AND '$date_to'
    ORDER BY depit_tb.depit_date DESC
";

$expenses_result = $conn->query($expenses_query);

// تجميع المصروفات حسب النوع
$expense_categories = [];
$total_expenses = 0;
$daily_expenses = [];

if ($expenses_result && $expenses_result->num_rows > 0) {
    while ($row = $expenses_result->fetch_assoc()) {
        $total_expenses += $row['depit_cash'];
        
        // تصنيف المصروفات (يمكن تحسينه بإضافة حقل category في قاعدة البيانات)
        $category = 'مصروفات عامة';
        $note = strtolower($row['depit_note']);
        
        if (strpos($note, 'كهرباء') !== false || strpos($note, 'ماء') !== false) {
            $category = 'المرافق';
        } elseif (strpos($note, 'قرطاسية') !== false || strpos($note, 'أدوات') !== false) {
            $category = 'القرطاسية والأدوات';
        } elseif (strpos($note, 'صيانة') !== false || strpos($note, 'إصلاح') !== false) {
            $category = 'الصيانة';
        } elseif (strpos($note, 'نظافة') !== false || strpos($note, 'تنظيف') !== false) {
            $category = 'النظافة';
        } elseif (strpos($note, 'طعام') !== false || strpos($note, 'وجبة') !== false) {
            $category = 'الطعام';
        }
        
        if (!isset($expense_categories[$category])) {
            $expense_categories[$category] = 0;
        }
        $expense_categories[$category] += $row['depit_cash'];
        
        // تجميع المصروفات اليومية
        $date = $row['expense_date'];
        if (!isset($daily_expenses[$date])) {
            $daily_expenses[$date] = 0;
        }
        $daily_expenses[$date] += $row['depit_cash'];
    }
}

// إعادة تعيين مؤشر النتائج
$expenses_result->data_seek(0);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المصروفات التفصيلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .report-header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
        @media print { .no-print { display: none !important; } body { background: white !important; } }
        .category-card { border-left: 4px solid #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- Header -->
        <div class="report-header text-center">
            <h1><i class="fas fa-arrow-down"></i> تقرير المصروفات التفصيلي</h1>
            <h3>روضة الأطفال</h3>
            <p class="mb-0">الفترة: <?= $date_from ?> إلى <?= $date_to ?></p>
            <p class="mb-0">تاريخ التقرير: <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Controls -->
        <div class="row no-print mb-4">
            <div class="col-md-8">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">عرض</button>
                    </div>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <button onclick="window.print()" class="btn btn-success">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="../accounting_system.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>

        <!-- Summary -->
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-money-bill-wave fa-3x text-danger mb-3"></i>
                    <h4 class="text-danger"><?= number_format($total_expenses) ?></h4>
                    <p class="text-muted">إجمالي المصروفات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-list fa-3x text-primary mb-3"></i>
                    <h4 class="text-primary"><?= count($expense_categories) ?></h4>
                    <p class="text-muted">عدد الفئات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <i class="fas fa-calendar-day fa-3x text-info mb-3"></i>
                    <h4 class="text-info"><?= count($daily_expenses) ?></h4>
                    <p class="text-muted">عدد الأيام</p>
                </div>
            </div>
        </div>

        <!-- Categories Breakdown -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-chart-pie"></i> توزيع المصروفات حسب الفئة</h5>
            <div class="row">
                <?php foreach ($expense_categories as $category => $amount): 
                    $percentage = $total_expenses > 0 ? ($amount / $total_expenses) * 100 : 0;
                ?>
                <div class="col-md-6 mb-3">
                    <div class="category-card p-3 border rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?= $category ?></h6>
                                <small class="text-muted"><?= number_format($percentage, 1) ?>% من الإجمالي</small>
                            </div>
                            <div class="text-end">
                                <h5 class="text-danger mb-0"><?= number_format($amount) ?></h5>
                            </div>
                        </div>
                        <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar bg-danger" style="width: <?= $percentage ?>%"></div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Detailed Expenses Table -->
        <div class="stat-card">
            <h5 class="mb-4"><i class="fas fa-table"></i> المصروفات التفصيلية</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>وصف المصروف</th>
                            <th>المبلغ</th>
                            <th>المستخدم</th>
                            <th>الفئة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($expenses_result && $expenses_result->num_rows > 0): ?>
                            <?php while ($row = $expenses_result->fetch_assoc()): 
                                // تحديد الفئة
                                $category = 'مصروفات عامة';
                                $note = strtolower($row['depit_note']);
                                
                                if (strpos($note, 'كهرباء') !== false || strpos($note, 'ماء') !== false) {
                                    $category = 'المرافق';
                                } elseif (strpos($note, 'قرطاسية') !== false || strpos($note, 'أدوات') !== false) {
                                    $category = 'القرطاسية والأدوات';
                                } elseif (strpos($note, 'صيانة') !== false || strpos($note, 'إصلاح') !== false) {
                                    $category = 'الصيانة';
                                } elseif (strpos($note, 'نظافة') !== false || strpos($note, 'تنظيف') !== false) {
                                    $category = 'النظافة';
                                } elseif (strpos($note, 'طعام') !== false || strpos($note, 'وجبة') !== false) {
                                    $category = 'الطعام';
                                }
                            ?>
                            <tr>
                                <td><?= date('Y-m-d', strtotime($row['depit_date'])) ?></td>
                                <td><strong><?= htmlspecialchars($row['depit_note']) ?></strong></td>
                                <td><span class="badge bg-danger fs-6"><?= number_format($row['depit_cash']) ?></span></td>
                                <td><span class="badge bg-primary"><?= htmlspecialchars($row['user_name']) ?></span></td>
                                <td><span class="badge bg-secondary"><?= $category ?></span></td>
                            </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center text-muted">لا توجد مصروفات في هذه الفترة</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-danger">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th><?= number_format($total_expenses) ?></th>
                            <th colspan="2"><?= $expenses_result ? $expenses_result->num_rows : 0 ?> معاملة</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <div class="col-md-6">
                <div class="stat-card">
                    <h5 class="mb-4"><i class="fas fa-chart-pie"></i> توزيع الفئات</h5>
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <h5 class="mb-4"><i class="fas fa-chart-line"></i> المصروفات اليومية</h5>
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Categories Pie Chart
        const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php foreach ($expense_categories as $category => $amount) echo "'" . $category . "',"; ?>],
                datasets: [{
                    data: [<?php foreach ($expense_categories as $category => $amount) echo $amount . ","; ?>],
                    backgroundColor: [
                        '#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997', 
                        '#17a2b8', '#6f42c1', '#e83e8c', '#6c757d', '#343a40'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Daily Expenses Line Chart
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [<?php foreach ($daily_expenses as $date => $amount) echo "'" . $date . "',"; ?>],
                datasets: [{
                    label: 'المصروفات اليومية',
                    data: [<?php foreach ($daily_expenses as $date => $amount) echo $amount . ","; ?>],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>

<?php $conn->close(); ?>
