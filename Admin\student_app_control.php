<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// جلب الإحصائيات
$stats = [
    'students' => 0,
    'news' => 0,
    'activities' => 0,
    'messages' => 0
];

$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM stud_tb");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['students'] = $row['count'];
}

$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM news_announcements");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['news'] = $row['count'];
}

$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM app_activities");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['activities'] = $row['count'];
}

$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM contact_messages");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['messages'] = $row['count'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: #7f8c8d;
            margin: 0;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #7f8c8d;
            font-weight: 600;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .welcome-card h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .welcome-card p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .stats-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>مرحباً بك في لوحة التحكم</h1>
            <p>إدارة شاملة ومتكاملة لتطبيق أكاديمية كيدز</p>
        </div>

        <!-- Statistics -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="number"><?php echo $stats['students']; ?></div>
                <div class="label">إجمالي الطلاب</div>
            </div>
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="number"><?php echo $stats['news']; ?></div>
                <div class="label">الأخبار المنشورة</div>
            </div>
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="number"><?php echo $stats['activities']; ?></div>
                <div class="label">الأنشطة المضافة</div>
            </div>
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="number"><?php echo $stats['messages']; ?></div>
                <div class="label">رسائل التواصل</div>
            </div>
        </div>

        <!-- Welcome Card -->
        <div class="welcome-card">
            <h2>مرحباً بك في نظام إدارة أكاديمية كيدز</h2>
            <p>استخدم القائمة الجانبية للوصول إلى جميع أقسام الإدارة والتحكم في التطبيق</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
