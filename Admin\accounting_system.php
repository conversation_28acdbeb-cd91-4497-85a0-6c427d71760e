<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "db_connection.php";

// التحقق من الاتصال
if (!$conn) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// معالجة الفلاتر مع التحقق من صحة البيانات
$filter_type = isset($_GET['filter_type']) ? trim($_GET['filter_type']) : 'dashboard';
$filter_user = isset($_GET['filter_user']) ? intval($_GET['filter_user']) : 0;
$date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = 20;

// التحقق من صحة نوع الفلتر
$allowed_filters = ['dashboard', 'revenue', 'expenses', 'salaries', 'profit_loss', 'balance_sheet', 'cash_flow', 'reports'];
if (!in_array($filter_type, $allowed_filters)) {
    $filter_type = 'dashboard';
}

// التحقق من صحة التواريخ
if ($date_from && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_from)) {
    $date_from = '';
}
if ($date_to && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_to)) {
    $date_to = '';
}

// حساب الإحصائيات الشاملة
$stats = [
    'revenue' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'expenses' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'salaries' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'net_profit' => 0,
    'cash_balance' => 0
];

// إجمالي الإيرادات مع معالجة الأخطاء
try {
    $revenue_query = "SELECT COUNT(*) as count, COALESCE(SUM(cash_stud), 0) as total FROM stud_pay WHERE cash_stud > 0";
    $revenue_result = $conn->query($revenue_query);
    if ($revenue_result && $row = $revenue_result->fetch_assoc()) {
        $stats['revenue']['total'] = intval($row['total']);
        $stats['revenue']['count'] = intval($row['count']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام الإيرادات: " . $e->getMessage());
}

// إيرادات الشهر الحالي مع معالجة الأخطاء
try {
    $current_month = date('Y-m');
    $monthly_revenue_query = "SELECT COALESCE(SUM(cash_stud), 0) as total FROM stud_pay WHERE cash_stud > 0 AND DATE_FORMAT(datein, '%Y-%m') = '$current_month'";
    $monthly_revenue_result = $conn->query($monthly_revenue_query);
    if ($monthly_revenue_result && $row = $monthly_revenue_result->fetch_assoc()) {
        $stats['revenue']['monthly'] = intval($row['total']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام الإيرادات الشهرية: " . $e->getMessage());
}

// إجمالي المصروفات
$expenses_query = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
$expenses_result = $conn->query($expenses_query);
if ($expenses_result && $row = $expenses_result->fetch_assoc()) {
    $stats['expenses']['total'] = $row['total'] ? intval($row['total']) : 0;
    $stats['expenses']['count'] = $row['count'] ? intval($row['count']) : 0;
}

// مصروفات الشهر الحالي
$monthly_expenses_query = "SELECT SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0 AND DATE_FORMAT(depit_date, '%Y-%m') = '$current_month'";
$monthly_expenses_result = $conn->query($monthly_expenses_query);
if ($monthly_expenses_result && $row = $monthly_expenses_result->fetch_assoc()) {
    $stats['expenses']['monthly'] = $row['total'] ? intval($row['total']) : 0;
}

// إجمالي الرواتب
$salaries_query = "SELECT COUNT(*) as count, SUM(salary) as total FROM employ_tb WHERE salary > 0";
$salaries_result = $conn->query($salaries_query);
if ($salaries_result && $row = $salaries_result->fetch_assoc()) {
    $stats['salaries']['total'] = $row['total'] ? intval($row['total']) : 0;
    $stats['salaries']['count'] = $row['count'] ? intval($row['count']) : 0;
}

// حساب صافي الربح
$stats['net_profit'] = $stats['revenue']['total'] - $stats['expenses']['total'] - $stats['salaries']['total'];
$stats['cash_balance'] = $stats['revenue']['total'] - $stats['expenses']['total'];

// جلب المستخدمين
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = $conn->query($users_query);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المحاسبي المتكامل - روضة الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            padding: 0;
            max-width: 1600px;
            overflow: hidden;
        }

        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 100vh;
            padding: 20px 0;
            position: fixed;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 5px 0;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 15px 25px;
            display: block;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin-right: 10px;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }

        .main-content {
            margin-right: 280px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .header-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-card.revenue { border-left-color: #28a745; }
        .stat-card.expenses { border-left-color: #dc3545; }
        .stat-card.salaries { border-left-color: #ffc107; }
        .stat-card.profit { border-left-color: #007bff; }
        .stat-card.balance { border-left-color: #17a2b8; }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-title {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .stat-subtitle {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .content-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background: #343a40;
            color: white;
            border: none;
            font-weight: 600;
        }

        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
            }
        }

        .toggle-sidebar {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.2rem;
            cursor: pointer;
        }

        .breadcrumb-custom {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-calculator"></i> النظام المحاسبي</h4>
            <small>روضة الأطفال</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="?filter_type=dashboard" class="<?= $filter_type == 'dashboard' ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a></li>
            <li><a href="?filter_type=revenue" class="<?= $filter_type == 'revenue' ? 'active' : '' ?>">
                <i class="fas fa-arrow-up text-success"></i> الإيرادات
            </a></li>
            <li><a href="?filter_type=expenses" class="<?= $filter_type == 'expenses' ? 'active' : '' ?>">
                <i class="fas fa-arrow-down text-danger"></i> المصروفات
            </a></li>
            <li><a href="?filter_type=salaries" class="<?= $filter_type == 'salaries' ? 'active' : '' ?>">
                <i class="fas fa-users text-warning"></i> الرواتب
            </a></li>
            <li><a href="?filter_type=profit_loss" class="<?= $filter_type == 'profit_loss' ? 'active' : '' ?>">
                <i class="fas fa-chart-line text-info"></i> الأرباح والخسائر
            </a></li>
            <li><a href="?filter_type=balance_sheet" class="<?= $filter_type == 'balance_sheet' ? 'active' : '' ?>">
                <i class="fas fa-balance-scale text-primary"></i> الميزانية العمومية
            </a></li>
            <li><a href="?filter_type=cash_flow" class="<?= $filter_type == 'cash_flow' ? 'active' : '' ?>">
                <i class="fas fa-exchange-alt text-secondary"></i> التدفق النقدي
            </a></li>
            <li><a href="?filter_type=reports" class="<?= $filter_type == 'reports' ? 'active' : '' ?>">
                <i class="fas fa-file-alt text-dark"></i> التقارير
            </a></li>
        </ul>

        <div class="sidebar-footer" style="position: absolute; bottom: 20px; width: 100%; text-align: center;">
            <small class="text-muted">© 2024 نظام الروضة</small>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header Bar -->
        <div class="header-bar">
            <div class="d-flex align-items-center">
                <button class="toggle-sidebar me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom">
                        <li class="breadcrumb-item"><a href="home.php">الرئيسية</a></li>
                        <li class="breadcrumb-item active">النظام المحاسبي</li>
                        <li class="breadcrumb-item active"><?= ucfirst($filter_type) ?></li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <i class="fas fa-user text-primary"></i>
                    <?= $_SESSION['user']->user_name ?? 'المدير' ?>
                </span>
                <span class="me-3">
                    <i class="fas fa-clock text-muted"></i>
                    <?= date('Y-m-d H:i') ?>
                </span>
                <a href="../logout.php" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> خروج
                </a>
            </div>
        </div>

        <?php if ($filter_type == 'dashboard'): ?>
        <!-- Dashboard Statistics -->
        <div class="stats-grid">
            <div class="stat-card revenue" onclick="location.href='?filter_type=revenue'">
                <i class="fas fa-arrow-up stat-icon text-success"></i>
                <div class="stat-value text-success"><?= number_format($stats['revenue']['total']) ?></div>
                <div class="stat-title">إجمالي الإيرادات</div>
                <div class="stat-subtitle"><?= $stats['revenue']['count'] ?> معاملة</div>
                <small class="text-muted">هذا الشهر: <?= number_format($stats['revenue']['monthly']) ?></small>
            </div>

            <div class="stat-card expenses" onclick="location.href='?filter_type=expenses'">
                <i class="fas fa-arrow-down stat-icon text-danger"></i>
                <div class="stat-value text-danger"><?= number_format($stats['expenses']['total']) ?></div>
                <div class="stat-title">إجمالي المصروفات</div>
                <div class="stat-subtitle"><?= $stats['expenses']['count'] ?> معاملة</div>
                <small class="text-muted">هذا الشهر: <?= number_format($stats['expenses']['monthly']) ?></small>
            </div>

            <div class="stat-card salaries" onclick="location.href='?filter_type=salaries'">
                <i class="fas fa-users stat-icon text-warning"></i>
                <div class="stat-value text-warning"><?= number_format($stats['salaries']['total']) ?></div>
                <div class="stat-title">إجمالي الرواتب</div>
                <div class="stat-subtitle"><?= $stats['salaries']['count'] ?> موظف</div>
                <small class="text-muted">متوسط الراتب: <?= $stats['salaries']['count'] > 0 ? number_format($stats['salaries']['total'] / $stats['salaries']['count']) : 0 ?></small>
            </div>

            <div class="stat-card profit" onclick="location.href='?filter_type=profit_loss'">
                <i class="fas fa-chart-line stat-icon <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"></i>
                <div class="stat-value <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($stats['net_profit']) ?></div>
                <div class="stat-title">صافي الربح</div>
                <div class="stat-subtitle"><?= $stats['net_profit'] >= 0 ? 'ربح' : 'خسارة' ?></div>
                <small class="text-muted">نسبة الربح: <?= $stats['revenue']['total'] > 0 ? number_format(($stats['net_profit'] / $stats['revenue']['total']) * 100, 2) : 0 ?>%</small>
            </div>

            <div class="stat-card balance" onclick="location.href='?filter_type=cash_flow'">
                <i class="fas fa-wallet stat-icon text-info"></i>
                <div class="stat-value text-info"><?= number_format($stats['cash_balance']) ?></div>
                <div class="stat-title">الرصيد النقدي</div>
                <div class="stat-subtitle">دينار عراقي</div>
                <small class="text-muted">بعد خصم المصروفات</small>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-md-8">
                <div class="content-area">
                    <h5><i class="fas fa-chart-bar"></i> الرسم البياني الشهري</h5>
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="content-area">
                    <h5><i class="fas fa-chart-pie"></i> توزيع المصروفات</h5>
                    <div class="chart-container">
                        <canvas id="expensesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'revenue'): ?>
        <!-- Revenue Section -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-arrow-up text-success"></i> تفاصيل الإيرادات</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('revenue')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('revenue')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="filter_type" value="revenue">
                    <div class="col-md-3">
                        <label class="form-label">المستخدم:</label>
                        <select name="filter_user" class="form-select">
                            <option value="0">جميع المستخدمين</option>
                            <?php
                            if ($users_result && $users_result->num_rows > 0) {
                                $users_result->data_seek(0); // Reset pointer
                                while ($user = $users_result->fetch_assoc()) {
                                    $selected = ($filter_user == $user['id_user']) ? 'selected' : '';
                                    echo '<option value="' . $user['id_user'] . '" ' . $selected . '>' . htmlspecialchars($user['user_name']) . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>

            <?php
            // بناء الاستعلام للإيرادات
            $user_condition = ($filter_user > 0) ? " AND stud_tb.userID = " . intval($filter_user) : "";
            $date_condition = "";
            if ($date_from && $date_to) {
                $date_condition = " AND DATE(stud_pay.datein) BETWEEN '" . $conn->real_escape_string($date_from) . "' AND '" . $conn->real_escape_string($date_to) . "'";
            }

            $offset = ($page - 1) * $per_page;
            $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name, stud_pay.id_pay
                              FROM stud_tb
                              INNER JOIN stud_pay ON stud_pay.id_stud = stud_tb.id
                              INNER JOIN users_tb ON stud_tb.userID = users_tb.id_user
                              WHERE stud_pay.cash_stud > 0
                              $user_condition $date_condition
                              ORDER BY stud_pay.datein DESC
                              LIMIT $per_page OFFSET $offset";

            $revenue_result = $conn->query($revenue_query);

            // حساب العدد الإجمالي للصفحات
            $count_query = "SELECT COUNT(*) as total FROM stud_tb
                           INNER JOIN stud_pay ON stud_pay.id_stud = stud_tb.id
                           INNER JOIN users_tb ON stud_tb.userID = users_tb.id_user
                           WHERE stud_pay.cash_stud > 0 $user_condition $date_condition";
            $count_result = $conn->query($count_query);
            $total_records = $count_result ? $count_result->fetch_assoc()['total'] : 0;
            $total_pages = ceil($total_records / $per_page);

            if ($revenue_result && $revenue_result->num_rows > 0) {
                $total = 0;
                echo '<div class="table-responsive">';
                echo '<table class="table table-striped table-hover" id="revenueTable">';
                echo '<thead>';
                echo '<tr>';
                echo '<th><i class="fas fa-child"></i> اسم الطالب</th>';
                echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                echo '<th><i class="fas fa-receipt"></i> رقم الوصل</th>';
                echo '</tr>';
                echo '</thead><tbody>';

                while ($row = $revenue_result->fetch_assoc()) {
                    $total += $row['cash_stud'];
                    echo '<tr>';
                    echo '<td><strong>' . htmlspecialchars($row['name']) . '</strong></td>';
                    echo '<td><span class="badge bg-success fs-6">IQD ' . number_format($row['cash_stud']) . '</span></td>';
                    echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
                    echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                    echo '<td><span class="badge bg-info">' . $row['id_pay'] . '</span></td>';
                    echo '</tr>';
                }

                echo '</tbody>';
                echo '<tfoot><tr class="table-success">';
                echo '<th>الإجمالي:</th>';
                echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                echo '<th colspan="3">' . $revenue_result->num_rows . ' من أصل ' . $total_records . ' معاملة</th>';
                echo '</tr></tfoot>';
                echo '</table></div>';

                // Pagination
                if ($total_pages > 1) {
                    echo '<nav aria-label="Page navigation">';
                    echo '<ul class="pagination justify-content-center">';

                    $base_url = "?filter_type=revenue&filter_user=$filter_user&date_from=$date_from&date_to=$date_to&page=";

                    if ($page > 1) {
                        echo '<li class="page-item"><a class="page-link" href="' . $base_url . ($page - 1) . '">السابق</a></li>';
                    }

                    for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
                        $active = ($i == $page) ? 'active' : '';
                        echo '<li class="page-item ' . $active . '"><a class="page-link" href="' . $base_url . $i . '">' . $i . '</a></li>';
                    }

                    if ($page < $total_pages) {
                        echo '<li class="page-item"><a class="page-link" href="' . $base_url . ($page + 1) . '">التالي</a></li>';
                    }

                    echo '</ul>';
                    echo '</nav>';
                }
            } else {
                echo '<div class="alert alert-warning text-center">';
                echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                echo '<h5>لا توجد إيرادات</h5>';
                echo '<p>لم يتم العثور على إيرادات للفترة المحددة</p>';
                echo '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'expenses'): ?>
        <!-- Expenses Section -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-arrow-down text-danger"></i> تفاصيل المصروفات</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('expenses')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('expenses')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="filter_type" value="expenses">
                    <div class="col-md-3">
                        <label class="form-label">المستخدم:</label>
                        <select name="filter_user" class="form-select">
                            <option value="0">جميع المستخدمين</option>
                            <?php
                            if ($users_result && $users_result->num_rows > 0) {
                                $users_result->data_seek(0);
                                while ($user = $users_result->fetch_assoc()) {
                                    $selected = ($filter_user == $user['id_user']) ? 'selected' : '';
                                    echo '<option value="' . $user['id_user'] . '" ' . $selected . '>' . htmlspecialchars($user['user_name']) . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>

            <?php
            // بناء الاستعلام للمصروفات
            $user_condition = ($filter_user > 0) ? " AND depit_tb.userID = " . intval($filter_user) : "";
            $date_condition = "";
            if ($date_from && $date_to) {
                $date_condition = " AND DATE(depit_tb.depit_date) BETWEEN '" . $conn->real_escape_string($date_from) . "' AND '" . $conn->real_escape_string($date_to) . "'";
            }

            $offset = ($page - 1) * $per_page;
            $expenses_query = "SELECT depit_tb.*, users_tb.user_name
                               FROM depit_tb
                               INNER JOIN users_tb ON depit_tb.userID = users_tb.id_user
                               WHERE depit_tb.depit_cash > 0
                               $user_condition $date_condition
                               ORDER BY depit_tb.depit_date DESC
                               LIMIT $per_page OFFSET $offset";

            $expenses_result = $conn->query($expenses_query);

            // حساب العدد الإجمالي
            $count_query = "SELECT COUNT(*) as total FROM depit_tb
                           INNER JOIN users_tb ON depit_tb.userID = users_tb.id_user
                           WHERE depit_tb.depit_cash > 0 $user_condition $date_condition";
            $count_result = $conn->query($count_query);
            $total_records = $count_result ? $count_result->fetch_assoc()['total'] : 0;
            $total_pages = ceil($total_records / $per_page);

            if ($expenses_result && $expenses_result->num_rows > 0) {
                $total = 0;
                echo '<div class="table-responsive">';
                echo '<table class="table table-striped table-hover" id="expensesTable">';
                echo '<thead>';
                echo '<tr>';
                echo '<th><i class="fas fa-sticky-note"></i> وصف المصروف</th>';
                echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                echo '<th><i class="fas fa-cogs"></i> الإجراءات</th>';
                echo '</tr>';
                echo '</thead><tbody>';

                while ($row = $expenses_result->fetch_assoc()) {
                    $total += $row['depit_cash'];
                    echo '<tr>';
                    echo '<td><strong>' . htmlspecialchars($row['depit_note']) . '</strong></td>';
                    echo '<td><span class="badge bg-danger fs-6">IQD ' . number_format($row['depit_cash']) . '</span></td>';
                    echo '<td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td>';
                    echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                    echo '<td>';
                    echo '<button class="btn btn-sm btn-outline-info" onclick="viewExpenseDetails(' . $row['id_depit'] . ')">';
                    echo '<i class="fas fa-eye"></i> عرض';
                    echo '</button>';
                    echo '</td>';
                    echo '</tr>';
                }

                echo '</tbody>';
                echo '<tfoot><tr class="table-danger">';
                echo '<th>الإجمالي:</th>';
                echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                echo '<th colspan="3">' . $expenses_result->num_rows . ' من أصل ' . $total_records . ' معاملة</th>';
                echo '</tr></tfoot>';
                echo '</table></div>';

                // Pagination
                if ($total_pages > 1) {
                    echo '<nav aria-label="Page navigation">';
                    echo '<ul class="pagination justify-content-center">';

                    $base_url = "?filter_type=expenses&filter_user=$filter_user&date_from=$date_from&date_to=$date_to&page=";

                    if ($page > 1) {
                        echo '<li class="page-item"><a class="page-link" href="' . $base_url . ($page - 1) . '">السابق</a></li>';
                    }

                    for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
                        $active = ($i == $page) ? 'active' : '';
                        echo '<li class="page-item ' . $active . '"><a class="page-link" href="' . $base_url . $i . '">' . $i . '</a></li>';
                    }

                    if ($page < $total_pages) {
                        echo '<li class="page-item"><a class="page-link" href="' . $base_url . ($page + 1) . '">التالي</a></li>';
                    }

                    echo '</ul>';
                    echo '</nav>';
                }
            } else {
                echo '<div class="alert alert-warning text-center">';
                echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                echo '<h5>لا توجد مصروفات</h5>';
                echo '<p>لم يتم العثور على مصروفات للفترة المحددة</p>';
                echo '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'salaries'): ?>
        <!-- Salaries Section -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-users text-warning"></i> تفاصيل الرواتب</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('salaries')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('salaries')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <?php
            $user_condition = ($filter_user > 0) ? " AND employ_tb.userID = " . intval($filter_user) : "";

            $offset = ($page - 1) * $per_page;
            $salaries_query = "SELECT employ_tb.*, users_tb.user_name
                               FROM employ_tb
                               INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user
                               WHERE employ_tb.salary > 0
                               $user_condition
                               ORDER BY employ_tb.date_start DESC
                               LIMIT $per_page OFFSET $offset";

            $salaries_result = $conn->query($salaries_query);

            if ($salaries_result && $salaries_result->num_rows > 0) {
                $total = 0;
                echo '<div class="table-responsive">';
                echo '<table class="table table-striped table-hover" id="salariesTable">';
                echo '<thead>';
                echo '<tr>';
                echo '<th><i class="fas fa-user"></i> اسم الموظف</th>';
                echo '<th><i class="fas fa-briefcase"></i> المنصب</th>';
                echo '<th><i class="fas fa-money-bill"></i> الراتب</th>';
                echo '<th><i class="fas fa-calendar"></i> تاريخ البداية</th>';
                echo '<th><i class="fas fa-user-tie"></i> المستخدم</th>';
                echo '<th><i class="fas fa-phone"></i> الهاتف</th>';
                echo '</tr>';
                echo '</thead><tbody>';

                while ($row = $salaries_result->fetch_assoc()) {
                    $total += $row['salary'];
                    echo '<tr>';
                    echo '<td><strong>' . htmlspecialchars($row['f_name']) . '</strong></td>';
                    echo '<td>' . htmlspecialchars($row['job']) . '</td>';
                    echo '<td><span class="badge bg-warning text-dark fs-6">IQD ' . number_format($row['salary']) . '</span></td>';
                    echo '<td>' . date('Y-m-d', strtotime($row['date_start'])) . '</td>';
                    echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                    echo '<td>' . htmlspecialchars($row['phone']) . '</td>';
                    echo '</tr>';
                }

                echo '</tbody>';
                echo '<tfoot><tr class="table-warning">';
                echo '<th colspan="2">الإجمالي:</th>';
                echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                echo '<th colspan="3">' . $salaries_result->num_rows . ' موظف</th>';
                echo '</tr></tfoot>';
                echo '</table></div>';
            } else {
                echo '<div class="alert alert-warning text-center">';
                echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                echo '<h5>لا توجد رواتب</h5>';
                echo '<p>لم يتم العثور على رواتب</p>';
                echo '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'profit_loss'): ?>
        <!-- Profit & Loss Statement -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-chart-line text-info"></i> قائمة الأرباح والخسائر</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('profit_loss')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('profit_loss')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-plus-circle"></i> الإيرادات</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>رسوم الطلاب</td>
                                    <td class="text-end"><strong><?= number_format($stats['revenue']['total']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إيرادات أخرى</td>
                                    <td class="text-end"><strong>0</strong></td>
                                </tr>
                                <tr class="table-success">
                                    <th>إجمالي الإيرادات</th>
                                    <th class="text-end"><?= number_format($stats['revenue']['total']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-minus-circle"></i> المصروفات</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>الرواتب</td>
                                    <td class="text-end"><strong><?= number_format($stats['salaries']['total']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>مصروفات تشغيلية</td>
                                    <td class="text-end"><strong><?= number_format($stats['expenses']['total']) ?></strong></td>
                                </tr>
                                <tr class="table-danger">
                                    <th>إجمالي المصروفات</th>
                                    <th class="text-end"><?= number_format($stats['expenses']['total'] + $stats['salaries']['total']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?>">
                        <div class="card-header bg-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?> text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator"></i>
                                <?= $stats['net_profit'] >= 0 ? 'صافي الربح' : 'صافي الخسارة' ?>
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?>">
                                <?= number_format($stats['net_profit']) ?> دينار عراقي
                            </h2>
                            <p class="text-muted">
                                نسبة الربح: <?= $stats['revenue']['total'] > 0 ? number_format(($stats['net_profit'] / $stats['revenue']['total']) * 100, 2) : 0 ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'balance_sheet'): ?>
        <!-- Balance Sheet -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-balance-scale text-primary"></i> الميزانية العمومية</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('balance_sheet')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('balance_sheet')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-coins"></i> الأصول</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>النقد في الصندوق</td>
                                    <td class="text-end"><strong><?= number_format($stats['cash_balance']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>المدينون (الطلاب)</td>
                                    <td class="text-end"><strong>0</strong></td>
                                </tr>
                                <tr>
                                    <td>الأصول الثابتة</td>
                                    <td class="text-end"><strong>0</strong></td>
                                </tr>
                                <tr class="table-primary">
                                    <th>إجمالي الأصول</th>
                                    <th class="text-end"><?= number_format($stats['cash_balance']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-file-invoice"></i> الخصوم وحقوق الملكية</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>الدائنون</td>
                                    <td class="text-end"><strong>0</strong></td>
                                </tr>
                                <tr>
                                    <td>رأس المال</td>
                                    <td class="text-end"><strong><?= number_format($stats['cash_balance'] - $stats['net_profit']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>الأرباح المحتجزة</td>
                                    <td class="text-end"><strong><?= number_format($stats['net_profit']) ?></strong></td>
                                </tr>
                                <tr class="table-secondary">
                                    <th>إجمالي الخصوم وحقوق الملكية</th>
                                    <th class="text-end"><?= number_format($stats['cash_balance']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'cash_flow'): ?>
        <!-- Cash Flow Statement -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-exchange-alt text-secondary"></i> قائمة التدفق النقدي</h4>
                <div>
                    <button class="btn btn-success btn-custom" onclick="exportData('cash_flow')">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-primary btn-custom" onclick="printSection('cash_flow')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-water"></i> التدفقات النقدية من الأنشطة التشغيلية</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <td>النقد المحصل من الطلاب</td>
                            <td class="text-end text-success"><strong>+ <?= number_format($stats['revenue']['total']) ?></strong></td>
                        </tr>
                        <tr>
                            <td>النقد المدفوع للموظفين</td>
                            <td class="text-end text-danger"><strong>- <?= number_format($stats['salaries']['total']) ?></strong></td>
                        </tr>
                        <tr>
                            <td>النقد المدفوع للمصروفات التشغيلية</td>
                            <td class="text-end text-danger"><strong>- <?= number_format($stats['expenses']['total']) ?></strong></td>
                        </tr>
                        <tr class="table-info">
                            <th>صافي التدفق النقدي من الأنشطة التشغيلية</th>
                            <th class="text-end <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                <?= $stats['net_profit'] >= 0 ? '+' : '' ?> <?= number_format($stats['net_profit']) ?>
                            </th>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-wallet"></i> الرصيد النقدي</h5>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-success"><?= number_format($stats['cash_balance']) ?> دينار عراقي</h3>
                    <p class="text-muted">الرصيد النقدي المتاح</p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'reports'): ?>
        <!-- Reports Section -->
        <div class="content-area">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-file-alt text-dark"></i> التقارير المالية</h4>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                            <h5>تقرير الإيرادات الشهرية</h5>
                            <p class="text-muted">تقرير مفصل بالإيرادات لكل شهر</p>
                            <button class="btn btn-primary" onclick="generateReport('monthly_revenue')">
                                <i class="fas fa-download"></i> تحميل التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-pie fa-3x text-danger mb-3"></i>
                            <h5>تقرير المصروفات التفصيلي</h5>
                            <p class="text-muted">تحليل المصروفات حسب النوع</p>
                            <button class="btn btn-danger" onclick="generateReport('detailed_expenses')">
                                <i class="fas fa-download"></i> تحميل التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-warning mb-3"></i>
                            <h5>تقرير الرواتب</h5>
                            <p class="text-muted">تقرير شامل برواتب الموظفين</p>
                            <button class="btn btn-warning" onclick="generateReport('salary_report')">
                                <i class="fas fa-download"></i> تحميل التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle Sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }

        // Export Data Functions
        function exportData(type) {
            let table;
            let filename;

            switch(type) {
                case 'revenue':
                    table = document.getElementById('revenueTable');
                    filename = 'revenue_report.csv';
                    break;
                case 'expenses':
                    table = document.getElementById('expensesTable');
                    filename = 'expenses_report.csv';
                    break;
                case 'salaries':
                    table = document.getElementById('salariesTable');
                    filename = 'salaries_report.csv';
                    break;
                default:
                    alert('نوع البيانات غير مدعوم');
                    return;
            }

            if (table) {
                const csv = tableToCSV(table);
                downloadCSV(csv, filename);
            } else {
                alert('لا توجد بيانات للتصدير');
            }
        }

        function tableToCSV(table) {
            let csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length - 1; j++) { // Skip last column (actions)
                    row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
                }

                csv.push(row.join(','));
            }

            return csv.join('\n');
        }

        function downloadCSV(csv, filename) {
            const csvFile = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const downloadLink = document.createElement('a');

            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';

            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // Print Functions
        function printSection(type) {
            window.print();
        }

        // View Expense Details
        function viewExpenseDetails(id) {
            alert('عرض تفاصيل المصروف رقم: ' + id);
            // يمكن إضافة modal هنا لعرض التفاصيل
        }

        // Generate Reports
        function generateReport(type) {
            switch(type) {
                case 'monthly_revenue':
                    window.open('reports/monthly_revenue.php', '_blank');
                    break;
                case 'detailed_expenses':
                    window.open('reports/detailed_expenses.php', '_blank');
                    break;
                case 'salary_report':
                    window.open('reports/salary_report.php', '_blank');
                    break;
                default:
                    alert('نوع التقرير غير مدعوم');
            }
        }

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            <?php if ($filter_type == 'dashboard'): ?>
            // Monthly Chart
            const monthlyCtx = document.getElementById('monthlyChart');
            if (monthlyCtx) {
                new Chart(monthlyCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'الإيرادات',
                            data: [<?= $stats['revenue']['monthly'] ?>, 0, 0, 0, 0, 0],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'المصروفات',
                            data: [<?= $stats['expenses']['monthly'] ?>, 0, 0, 0, 0, 0],
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'الإيرادات والمصروفات الشهرية'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // Expenses Pie Chart
            const expensesCtx = document.getElementById('expensesChart');
            if (expensesCtx) {
                new Chart(expensesCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['الرواتب', 'المصروفات التشغيلية'],
                        datasets: [{
                            data: [<?= $stats['salaries']['total'] ?>, <?= $stats['expenses']['total'] ?>],
                            backgroundColor: ['#ffc107', '#dc3545'],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'توزيع المصروفات'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
            <?php endif; ?>

            // Add loading animation to buttons
            const buttons = document.querySelectorAll('.btn-custom');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('no-loading')) {
                        const icon = this.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-spinner fa-spin';
                        }
                    }
                });
            });
        });

        // Responsive sidebar for mobile
        if (window.innerWidth <= 768) {
            document.getElementById('sidebar').classList.add('collapsed');
            document.getElementById('mainContent').classList.add('expanded');
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>
