/* Advanced Dashboard Styles */

/* Statistics Grid */
.stats-grid {
    margin-bottom: 50px;
}

.priority-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.main-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

/* Stat Cards */
.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius);
    padding: 30px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    color: #1e293b;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    transition: var(--transition);
}

.stat-card.urgent::before {
    background: var(--danger-gradient);
}

.stat-card.warning::before {
    background: var(--warning-gradient);
}

.stat-card.active::before {
    background: var(--success-gradient);
}

.stat-card.total::before {
    background: var(--info-gradient);
}

.stat-card.financial::before {
    background: var(--gold-gradient);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-light);
}

.card-icon.urgent {
    background: var(--danger-gradient);
}

.card-icon.warning {
    background: var(--warning-gradient);
}

.card-icon.active {
    background: var(--success-gradient);
}

.card-icon.total {
    background: var(--info-gradient);
}

.card-icon.financial {
    background: var(--gold-gradient);
}

.card-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    background: var(--primary-gradient);
}

.card-badge.urgent {
    background: var(--danger-gradient);
}

.card-badge.warning {
    background: var(--warning-gradient);
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.card-trend.up {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.card-trend.down {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.card-trend.stable {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

.card-content {
    margin-bottom: 20px;
}

.card-number {
    font-size: 3rem;
    font-weight: 900;
    color: #3b82f6;
    margin-bottom: 10px;
    line-height: 1;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.card-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.4;
}

.card-footer {
    margin-top: auto;
}

.card-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 10px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.card-link:hover {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transform: translateX(-5px);
}

/* Categories Section */
.categories-section {
    margin-bottom: 50px;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.category-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    color: #1e293b;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: var(--transition);
}

.category-card:hover::before {
    opacity: 1;
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-medium);
}

.category-card.kindergarten .category-icon {
    background: var(--orange-gradient);
}

.category-card.preschool .category-icon {
    background: var(--purple-gradient);
}

.category-card.preparatory .category-icon {
    background: var(--blue-gradient);
}

.category-card.advanced .category-icon {
    background: var(--green-gradient);
}

.category-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: #3b82f6;
    margin-bottom: 10px;
}

.category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
    line-height: 1.3;
}

.category-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 20px;
}

.category-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-bar {
    height: 100%;
    background: var(--success-gradient);
    border-radius: 3px;
    width: var(--progress);
    transition: width 2s ease;
}

.category-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 10px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.category-link:hover {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transform: translateX(-5px);
}

/* Management Section */
.management-section {
    margin-bottom: 50px;
}

.management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.management-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 30px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.management-card.needs {
    border-left: 4px solid #667eea;
}

.management-card.leaves {
    border-left: 4px solid #28a745;
}

.management-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    margin-bottom: 20px;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-light);
}

.management-card.needs .management-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.management-card.leaves .management-icon {
    background: var(--success-gradient);
}

.management-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: white;
    margin-bottom: 10px;
}

.management-subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    line-height: 1.5;
}

.management-stats {
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.management-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 15px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.management-link:hover {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transform: translateX(-5px);
}

/* Enhanced Footer */
.enhanced-footer {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    margin-top: 50px;
    padding: 40px;
    color: white;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
    text-decoration: none;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .main-container {
        padding: 15px;
    }
    
    .hero-section {
        padding: 25px;
    }
    
    .stat-card, .category-card, .management-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .card-number, .category-number {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }
}
