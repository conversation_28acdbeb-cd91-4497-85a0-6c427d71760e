<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/topbar.php";
include "addon/dbcon.php";

// معالجة إضافة الحقول الجديدة
$fix_applied = false;
$messages = [];

if (isset($_POST['fix_database'])) {
    try {
        // فحص وجود الحقول
        $check_health = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'health_status'");
        $check_registration = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'registration_status'");
        
        // إضافة حقل الحالة الصحية إذا لم يكن موجوداً
        if (mysqli_num_rows($check_health) == 0) {
            $add_health = mysqli_query($con, "ALTER TABLE `stud_tb` ADD `health_status` TEXT NULL DEFAULT NULL COMMENT 'الحالة الصحية للطالب'");
            if ($add_health) {
                $messages[] = "✅ تم إضافة حقل health_status بنجاح";
            } else {
                $messages[] = "❌ فشل في إضافة حقل health_status: " . mysqli_error($con);
            }
        } else {
            $messages[] = "ℹ️ حقل health_status موجود بالفعل";
        }
        
        // إضافة حقل حالة التسجيل إذا لم يكن موجوداً
        if (mysqli_num_rows($check_registration) == 0) {
            $add_registration = mysqli_query($con, "ALTER TABLE `stud_tb` ADD `registration_status` VARCHAR(50) NULL DEFAULT 'تسجيل جديد' COMMENT 'حالة التسجيل'");
            if ($add_registration) {
                $messages[] = "✅ تم إضافة حقل registration_status بنجاح";
            } else {
                $messages[] = "❌ فشل في إضافة حقل registration_status: " . mysqli_error($con);
            }
        } else {
            $messages[] = "ℹ️ حقل registration_status موجود بالفعل";
        }
        
        // تحديث البيانات الموجودة
        $update_health = mysqli_query($con, "UPDATE `stud_tb` SET `health_status` = 'لا توجد ملاحظات' WHERE `health_status` IS NULL OR `health_status` = ''");
        $update_registration = mysqli_query($con, "UPDATE `stud_tb` SET `registration_status` = 'تسجيل جديد' WHERE `registration_status` IS NULL OR `registration_status` = ''");
        
        if ($update_health && $update_registration) {
            $messages[] = "✅ تم تحديث البيانات الموجودة بنجاح";
        }
        
        $fix_applied = true;
        
    } catch (Exception $e) {
        $messages[] = "❌ خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة صفحة الطلاب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            padding: 20px;
        }
        
        .fix-container {
            max-width: 800px;
            margin: 50px auto;
        }
        
        .fix-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .fix-header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .success-box {
            background: #f0fdf4;
            border: 1px solid #10b981;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #065f46;
        }
        
        .error-box {
            background: #fef2f2;
            border: 1px solid #ef4444;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #991b1b;
        }
        
        .warning-box {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #92400e;
        }
        
        .fix-button {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }
        
        .fix-button:hover {
            background: linear-gradient(45deg, #dc2626, #b91c1c);
            transform: translateY(-2px);
        }
        
        .test-button {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: linear-gradient(45deg, #059669, #047857);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .message-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        
        .message-success {
            background: #f0fdf4;
            border-left-color: #10b981;
            color: #065f46;
        }
        
        .message-error {
            background: #fef2f2;
            border-left-color: #ef4444;
            color: #991b1b;
        }
        
        .message-info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1>🔧 إصلاح مشكلة صفحة الطلاب</h1>
            <p>حل مشكلة "حدث خطأ في تحميل البيانات" في صفحة infost.php</p>
        </div>

        <?php if ($fix_applied): ?>
        <div class="fix-card">
            <h3>📋 نتائج الإصلاح</h3>
            <?php foreach ($messages as $message): ?>
                <div class="message-item <?php 
                    if (strpos($message, '✅') !== false) echo 'message-success';
                    elseif (strpos($message, '❌') !== false) echo 'message-error';
                    else echo 'message-info';
                ?>">
                    <?php echo $message; ?>
                </div>
            <?php endforeach; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="infost.php" class="test-button" target="_blank">
                    <i class="fas fa-users"></i>
                    اختبار صفحة الطلاب الآن
                </a>
            </div>
        </div>
        <?php endif; ?>

        <div class="fix-card">
            <h3>🔍 تشخيص المشكلة</h3>
            <div class="warning-box">
                <h5><i class="fas fa-exclamation-triangle"></i> المشكلة المحتملة:</h5>
                <p>صفحة الطلاب تعرض خطأ "حدث خطأ في تحميل البيانات" عند اختيار مستخدم. هذا يحدث عادة بسبب:</p>
                <ul>
                    <li>عدم وجود الحقول الجديدة (health_status, registration_status) في قاعدة البيانات</li>
                    <li>خطأ في الاستعلام SQL</li>
                    <li>مشكلة في الاتصال بقاعدة البيانات</li>
                </ul>
            </div>
        </div>

        <div class="fix-card">
            <h3>🛠️ الحل السريع</h3>
            <div class="success-box">
                <h5><i class="fas fa-tools"></i> خطوات الإصلاح:</h5>
                <ol>
                    <li><strong>إضافة الحقول المفقودة:</strong> سيتم إضافة حقلي health_status و registration_status</li>
                    <li><strong>تحديث البيانات الموجودة:</strong> إعطاء قيم افتراضية للبيانات الحالية</li>
                    <li><strong>اختبار الاستعلام:</strong> التأكد من عمل الاستعلام بشكل صحيح</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <form method="POST" style="display: inline-block;">
                    <button type="submit" name="fix_database" class="fix-button" 
                            onclick="return confirm('هل أنت متأكد من تطبيق الإصلاح؟')">
                        <i class="fas fa-magic"></i>
                        تطبيق الإصلاح الآن
                    </button>
                </form>
            </div>
        </div>

        <div class="fix-card">
            <h3>📊 فحص الحالة الحالية</h3>
            <?php
            // فحص وجود الحقول
            $health_exists = mysqli_num_rows(mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'health_status'")) > 0;
            $registration_exists = mysqli_num_rows(mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'registration_status'")) > 0;
            
            // فحص عدد المستخدمين والطلاب
            $users_count = mysqli_num_rows(mysqli_query($con, "SELECT id_user FROM users_tb WHERE role = 'User'"));
            $students_count = mysqli_num_rows(mysqli_query($con, "SELECT id FROM stud_tb"));
            ?>
            
            <div class="message-item <?php echo $health_exists ? 'message-success' : 'message-error'; ?>">
                <strong>حقل health_status:</strong> <?php echo $health_exists ? '✅ موجود' : '❌ غير موجود'; ?>
            </div>
            
            <div class="message-item <?php echo $registration_exists ? 'message-success' : 'message-error'; ?>">
                <strong>حقل registration_status:</strong> <?php echo $registration_exists ? '✅ موجود' : '❌ غير موجود'; ?>
            </div>
            
            <div class="message-item message-info">
                <strong>عدد المستخدمين:</strong> <?php echo $users_count; ?> مستخدم
            </div>
            
            <div class="message-item message-info">
                <strong>عدد الطلاب:</strong> <?php echo $students_count; ?> طالب
            </div>
        </div>

        <div class="fix-card">
            <h3>🔗 روابط مفيدة</h3>
            <div style="text-align: center;">
                <a href="test_database_connection.php" class="test-button" target="_blank">
                    <i class="fas fa-database"></i>
                    اختبار قاعدة البيانات
                </a>
                <a href="infost.php" class="test-button" target="_blank">
                    <i class="fas fa-users"></i>
                    صفحة الطلاب
                </a>
                <a href="addstud.php" class="test-button" target="_blank">
                    <i class="fas fa-user-plus"></i>
                    إضافة طالب
                </a>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/all.min.js"></script>
</body>
</html>
