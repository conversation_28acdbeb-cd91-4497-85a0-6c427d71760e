<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول الإعدادات
$create_settings = "CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'color', 'image', 'number', 'boolean') DEFAULT 'text',
    setting_category VARCHAR(50) DEFAULT 'general',
    setting_description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_settings);

$message = '';
$messageType = 'success';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_settings'])) {
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'setting_') === 0) {
            $setting_key = substr($key, 8);
            $setting_value = mysqli_real_escape_string($con, $value);
            $sql = "INSERT INTO app_settings (setting_key, setting_value) VALUES ('$setting_key', '$setting_value') 
                    ON DUPLICATE KEY UPDATE setting_value = '$setting_value', updated_at = NOW()";
            @mysqli_query($con, $sql);
        }
    }
    $message = 'تم حفظ الإعدادات بنجاح!';
}

// جلب الإعدادات الحالية
$settings = [];
$settings_result = @mysqli_query($con, "SELECT * FROM app_settings ORDER BY setting_category, setting_key");
if ($settings_result) {
    while ($row = mysqli_fetch_assoc($settings_result)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إعدادات التطبيق - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container {
            max-width: 800px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .settings-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-save:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #667eea;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container">
        <!-- Header -->
        <div class="header-card">
            <h1>⚙️ إعدادات التطبيق</h1>
            <p style="color: #7f8c8d; margin: 0;">إدارة إعدادات التطبيق العامة والمعلومات الأساسية</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom alert-success-custom">
                <i class="fas fa-check-circle"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Settings Form -->
        <div class="settings-card">
            <form method="POST">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم التطبيق:</label>
                        <input type="text" name="setting_app_name" class="form-control" 
                               value="<?php echo $settings['app_name'] ?? 'أكاديمية كيدز'; ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">شعار التطبيق:</label>
                        <input type="text" name="setting_app_logo" class="form-control" 
                               value="<?php echo $settings['app_logo'] ?? '🎓'; ?>">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">وصف التطبيق:</label>
                    <textarea name="setting_app_description" class="form-control" rows="3"><?php echo $settings['app_description'] ?? 'تطبيق تعليمي متكامل للأطفال'; ?></textarea>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">رقم الإصدار:</label>
                        <input type="text" name="setting_app_version" class="form-control" 
                               value="<?php echo $settings['app_version'] ?? '1.0.0'; ?>">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">المطور:</label>
                        <input type="text" name="setting_developer" class="form-control" 
                               value="<?php echo $settings['developer'] ?? 'فريق التطوير'; ?>">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">البريد الإلكتروني:</label>
                        <input type="email" name="setting_support_email" class="form-control" 
                               value="<?php echo $settings['support_email'] ?? '<EMAIL>'; ?>">
                    </div>
                </div>

                <h3 class="section-title">
                    <i class="fas fa-palette"></i>
                    إعدادات التصميم
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اللون الأساسي:</label>
                        <input type="color" name="setting_primary_color" class="form-control" 
                               value="<?php echo $settings['primary_color'] ?? '#667eea'; ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اللون الثانوي:</label>
                        <input type="color" name="setting_secondary_color" class="form-control" 
                               value="<?php echo $settings['secondary_color'] ?? '#764ba2'; ?>">
                    </div>
                </div>

                <h3 class="section-title">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">المنطقة الزمنية:</label>
                        <select name="setting_timezone" class="form-select">
                            <option value="Asia/Riyadh" <?php echo ($settings['timezone'] ?? '') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                            <option value="Asia/Dubai" <?php echo ($settings['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                            <option value="Asia/Kuwait" <?php echo ($settings['timezone'] ?? '') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                            <option value="Asia/Qatar" <?php echo ($settings['timezone'] ?? '') == 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اللغة الافتراضية:</label>
                        <select name="setting_default_language" class="form-select">
                            <option value="ar" <?php echo ($settings['default_language'] ?? '') == 'ar' ? 'selected' : ''; ?>>العربية</option>
                            <option value="en" <?php echo ($settings['default_language'] ?? '') == 'en' ? 'selected' : ''; ?>>English</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" name="save_settings" class="btn-save">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // Form submission loading state
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.btn-save');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
