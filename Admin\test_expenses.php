<?php
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

include "addon/dbcon.php";

echo "<h2>اختبار المصاريف</h2>";

// اختبار المستخدم رقم 1
$user_id = 1;
echo "<h3>اختبار المستخدم رقم: $user_id</h3>";

// محاكاة POST request
$_POST['id'] = $user_id;

echo "<h4>نتيجة ملف info_depitF.php:</h4>";
echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
echo "<thead style='background: #f8f9fa;'>
    <tr>
        <th>العمليات</th>
        <th>المستخدم</th>
        <th>تاريخ المصروف</th>
        <th>قيمة المصروف</th>
        <th>وصف المصروف</th>
    </tr>
</thead>";
echo "<tbody>";

// تضمين ملف المعالجة
ob_start();
include "addon/info_depitF.php";
$output = ob_get_clean();

echo $output;
echo "</tbody></table>";

echo "<h4>اختبار مع مستخدم آخر:</h4>";
$_POST['id'] = 6; // مستخدم A2

echo "<table border='1' style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
echo "<thead style='background: #f8f9fa;'>
    <tr>
        <th>العمليات</th>
        <th>المستخدم</th>
        <th>تاريخ المصروف</th>
        <th>قيمة المصروف</th>
        <th>وصف المصروف</th>
    </tr>
</thead>";
echo "<tbody>";

ob_start();
include "addon/info_depitF.php";
$output2 = ob_get_clean();

echo $output2;
echo "</tbody></table>";

echo "<h4>اختبار بدون اختيار مستخدم:</h4>";
unset($_POST['id']);

echo "<table border='1' style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
echo "<thead style='background: #f8f9fa;'>
    <tr>
        <th>العمليات</th>
        <th>المستخدم</th>
        <th>تاريخ المصروف</th>
        <th>قيمة المصروف</th>
        <th>وصف المصروف</th>
    </tr>
</thead>";
echo "<tbody>";

ob_start();
include "addon/info_depitF.php";
$output3 = ob_get_clean();

echo $output3;
echo "</tbody></table>";

echo "<h4>اختبار مباشر للمجموع:</h4>";
$sql = "SELECT SUM(depit_cash) as total FROM depit_tb WHERE userID = 1";
$result = mysqli_query($con, $sql);
if ($result) {
    $total = mysqli_fetch_assoc($result)['total'];
    echo "<p style='font-size: 18px; color: green;'>المجموع الكلي للمستخدم رقم 1: IQD " . number_format($total) . "</p>";
}

echo "<h4>روابط مفيدة:</h4>";
echo "<ul>";
echo "<li><a href='info_depit.php' target='_blank'>صفحة المصاريف الرئيسية</a></li>";
echo "<li><a href='allDepit.php' target='_blank'>جميع المصاريف</a></li>";
echo "</ul>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
    th { background-color: #f2f2f2; }
    h2, h3, h4 { color: #333; }
</style>
