<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
try {
    include "db_connection.php";
    if (!$conn) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
} catch (Exception $e) {
    die("خطأ في الاتصال: " . $e->getMessage());
}

// معالجة الفلاتر مع التحقق من صحة البيانات
$filter_type = isset($_GET['filter_type']) ? trim($_GET['filter_type']) : 'dashboard';
$filter_user = isset($_GET['filter_user']) ? intval($_GET['filter_user']) : 0;
$date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = 20;

// التحقق من صحة نوع الفلتر
$allowed_filters = ['dashboard', 'revenue', 'expenses', 'salaries', 'profit_loss', 'balance_sheet', 'cash_flow', 'reports'];
if (!in_array($filter_type, $allowed_filters)) {
    $filter_type = 'dashboard';
}

// التحقق من صحة التواريخ
if ($date_from && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_from)) {
    $date_from = '';
}
if ($date_to && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_to)) {
    $date_to = '';
}

// حساب الإحصائيات الشاملة
$stats = [
    'revenue' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'expenses' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'salaries' => ['total' => 0, 'count' => 0, 'monthly' => 0],
    'net_profit' => 0,
    'cash_balance' => 0
];

// إجمالي الإيرادات مع معالجة الأخطاء
try {
    $revenue_query = "SELECT COUNT(*) as count, COALESCE(SUM(cash_stud), 0) as total FROM stud_pay WHERE cash_stud > 0";
    $revenue_result = $conn->query($revenue_query);
    if ($revenue_result && $row = $revenue_result->fetch_assoc()) {
        $stats['revenue']['total'] = intval($row['total']);
        $stats['revenue']['count'] = intval($row['count']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام الإيرادات: " . $e->getMessage());
}

// إيرادات الشهر الحالي
try {
    $current_month = date('Y-m');
    $monthly_revenue_query = "SELECT COALESCE(SUM(cash_stud), 0) as total FROM stud_pay WHERE cash_stud > 0 AND DATE_FORMAT(datein, '%Y-%m') = '$current_month'";
    $monthly_revenue_result = $conn->query($monthly_revenue_query);
    if ($monthly_revenue_result && $row = $monthly_revenue_result->fetch_assoc()) {
        $stats['revenue']['monthly'] = intval($row['total']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام الإيرادات الشهرية: " . $e->getMessage());
}

// إجمالي المصروفات
try {
    $expenses_query = "SELECT COUNT(*) as count, COALESCE(SUM(depit_cash), 0) as total FROM depit_tb WHERE depit_cash > 0";
    $expenses_result = $conn->query($expenses_query);
    if ($expenses_result && $row = $expenses_result->fetch_assoc()) {
        $stats['expenses']['total'] = intval($row['total']);
        $stats['expenses']['count'] = intval($row['count']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام المصروفات: " . $e->getMessage());
}

// مصروفات الشهر الحالي
try {
    $monthly_expenses_query = "SELECT COALESCE(SUM(depit_cash), 0) as total FROM depit_tb WHERE depit_cash > 0 AND DATE_FORMAT(depit_date, '%Y-%m') = '$current_month'";
    $monthly_expenses_result = $conn->query($monthly_expenses_query);
    if ($monthly_expenses_result && $row = $monthly_expenses_result->fetch_assoc()) {
        $stats['expenses']['monthly'] = intval($row['total']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام المصروفات الشهرية: " . $e->getMessage());
}

// إجمالي الرواتب
try {
    $salaries_query = "SELECT COUNT(*) as count, COALESCE(SUM(salary), 0) as total FROM employ_tb WHERE salary > 0";
    $salaries_result = $conn->query($salaries_query);
    if ($salaries_result && $row = $salaries_result->fetch_assoc()) {
        $stats['salaries']['total'] = intval($row['total']);
        $stats['salaries']['count'] = intval($row['count']);
    }
} catch (Exception $e) {
    error_log("خطأ في استعلام الرواتب: " . $e->getMessage());
}

// حساب صافي الربح
$stats['net_profit'] = $stats['revenue']['total'] - $stats['expenses']['total'] - $stats['salaries']['total'];
$stats['cash_balance'] = $stats['revenue']['total'] - $stats['expenses']['total'];

// جلب المستخدمين
$users_result = null;
try {
    $users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
    $users_result = $conn->query($users_query);
} catch (Exception $e) {
    error_log("خطأ في جلب المستخدمين: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المحاسبي المتكامل - روضة الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            padding: 0;
            max-width: 1600px;
            overflow: hidden;
        }

        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 100vh;
            padding: 20px 0;
            position: fixed;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 5px 0;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 15px 25px;
            display: block;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin-right: 10px;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }

        .main-content {
            margin-right: 280px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .header-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-card.revenue { border-left-color: #28a745; }
        .stat-card.expenses { border-left-color: #dc3545; }
        .stat-card.salaries { border-left-color: #ffc107; }
        .stat-card.profit { border-left-color: #007bff; }
        .stat-card.balance { border-left-color: #17a2b8; }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-title {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .stat-subtitle {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .content-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .table th {
            background: #343a40;
            color: white;
            border: none;
            font-weight: 600;
        }

        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
            }
        }

        .toggle-sidebar {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.2rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-calculator"></i> النظام المحاسبي</h4>
            <small>روضة الأطفال</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="?filter_type=dashboard" class="<?= $filter_type == 'dashboard' ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a></li>
            <li><a href="?filter_type=revenue" class="<?= $filter_type == 'revenue' ? 'active' : '' ?>">
                <i class="fas fa-arrow-up text-success"></i> الإيرادات
            </a></li>
            <li><a href="?filter_type=expenses" class="<?= $filter_type == 'expenses' ? 'active' : '' ?>">
                <i class="fas fa-arrow-down text-danger"></i> المصروفات
            </a></li>
            <li><a href="?filter_type=salaries" class="<?= $filter_type == 'salaries' ? 'active' : '' ?>">
                <i class="fas fa-users text-warning"></i> الرواتب
            </a></li>
            <li><a href="?filter_type=profit_loss" class="<?= $filter_type == 'profit_loss' ? 'active' : '' ?>">
                <i class="fas fa-chart-line text-info"></i> الأرباح والخسائر
            </a></li>
            <li><a href="acounting.php">
                <i class="fas fa-chart-bar text-secondary"></i> النظام القديم
            </a></li>
            <li><a href="home.php">
                <i class="fas fa-home text-primary"></i> الصفحة الرئيسية
            </a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header Bar -->
        <div class="header-bar">
            <div class="d-flex align-items-center">
                <button class="toggle-sidebar me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">النظام المحاسبي المتكامل</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <i class="fas fa-user text-primary"></i>
                    <?= $_SESSION['user']->user_name ?? 'المدير' ?>
                </span>
                <span class="me-3">
                    <i class="fas fa-clock text-muted"></i>
                    <?= date('Y-m-d H:i') ?>
                </span>
                <a href="../logout.php" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> خروج
                </a>
            </div>
        </div>

        <?php if ($filter_type == 'dashboard'): ?>
        <!-- Dashboard Statistics -->
        <div class="stats-grid">
            <div class="stat-card revenue" onclick="location.href='?filter_type=revenue'">
                <i class="fas fa-arrow-up stat-icon text-success"></i>
                <div class="stat-value text-success"><?= number_format($stats['revenue']['total']) ?></div>
                <div class="stat-title">إجمالي الإيرادات</div>
                <div class="stat-subtitle"><?= $stats['revenue']['count'] ?> معاملة</div>
                <small class="text-muted">هذا الشهر: <?= number_format($stats['revenue']['monthly']) ?></small>
            </div>

            <div class="stat-card expenses" onclick="location.href='?filter_type=expenses'">
                <i class="fas fa-arrow-down stat-icon text-danger"></i>
                <div class="stat-value text-danger"><?= number_format($stats['expenses']['total']) ?></div>
                <div class="stat-title">إجمالي المصروفات</div>
                <div class="stat-subtitle"><?= $stats['expenses']['count'] ?> معاملة</div>
                <small class="text-muted">هذا الشهر: <?= number_format($stats['expenses']['monthly']) ?></small>
            </div>

            <div class="stat-card salaries" onclick="location.href='?filter_type=salaries'">
                <i class="fas fa-users stat-icon text-warning"></i>
                <div class="stat-value text-warning"><?= number_format($stats['salaries']['total']) ?></div>
                <div class="stat-title">إجمالي الرواتب</div>
                <div class="stat-subtitle"><?= $stats['salaries']['count'] ?> موظف</div>
                <small class="text-muted">متوسط الراتب: <?= $stats['salaries']['count'] > 0 ? number_format($stats['salaries']['total'] / $stats['salaries']['count']) : 0 ?></small>
            </div>

            <div class="stat-card profit" onclick="location.href='?filter_type=profit_loss'">
                <i class="fas fa-chart-line stat-icon <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"></i>
                <div class="stat-value <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($stats['net_profit']) ?></div>
                <div class="stat-title">صافي الربح</div>
                <div class="stat-subtitle"><?= $stats['net_profit'] >= 0 ? 'ربح' : 'خسارة' ?></div>
                <small class="text-muted">نسبة الربح: <?= $stats['revenue']['total'] > 0 ? number_format(($stats['net_profit'] / $stats['revenue']['total']) * 100, 2) : 0 ?>%</small>
            </div>

            <div class="stat-card balance">
                <i class="fas fa-wallet stat-icon text-info"></i>
                <div class="stat-value text-info"><?= number_format($stats['cash_balance']) ?></div>
                <div class="stat-title">الرصيد النقدي</div>
                <div class="stat-subtitle">دينار عراقي</div>
                <small class="text-muted">بعد خصم المصروفات</small>
            </div>
        </div>

        <!-- Quick Info -->
        <div class="content-area">
            <h5><i class="fas fa-info-circle"></i> معلومات سريعة</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-chart-line"></i> الأداء المالي</h6>
                        <p class="mb-1">إجمالي الإيرادات: <strong><?= number_format($stats['revenue']['total']) ?></strong> دينار</p>
                        <p class="mb-1">إجمالي المصروفات: <strong><?= number_format($stats['expenses']['total'] + $stats['salaries']['total']) ?></strong> دينار</p>
                        <p class="mb-0">صافي الربح: <strong class="<?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($stats['net_profit']) ?></strong> دينار</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-calendar"></i> هذا الشهر</h6>
                        <p class="mb-1">إيرادات الشهر: <strong><?= number_format($stats['revenue']['monthly']) ?></strong> دينار</p>
                        <p class="mb-1">مصروفات الشهر: <strong><?= number_format($stats['expenses']['monthly']) ?></strong> دينار</p>
                        <p class="mb-0">صافي الشهر: <strong class="<?= ($stats['revenue']['monthly'] - $stats['expenses']['monthly']) >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($stats['revenue']['monthly'] - $stats['expenses']['monthly']) ?></strong> دينار</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'revenue'): ?>
        <!-- Revenue Section -->
        <div class="content-area">
            <h4><i class="fas fa-arrow-up text-success"></i> تفاصيل الإيرادات</h4>

            <?php
            try {
                $user_condition = ($filter_user > 0) ? " AND stud_tb.userID = " . intval($filter_user) : "";
                $date_condition = "";
                if ($date_from && $date_to) {
                    $date_condition = " AND DATE(stud_pay.datein) BETWEEN '" . $conn->real_escape_string($date_from) . "' AND '" . $conn->real_escape_string($date_to) . "'";
                }

                $offset = ($page - 1) * $per_page;
                $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name, stud_pay.id_pay
                                  FROM stud_tb
                                  INNER JOIN stud_pay ON stud_pay.id_stud = stud_tb.id
                                  INNER JOIN users_tb ON stud_tb.userID = users_tb.id_user
                                  WHERE stud_pay.cash_stud > 0
                                  $user_condition $date_condition
                                  ORDER BY stud_pay.datein DESC
                                  LIMIT $per_page OFFSET $offset";

                $revenue_result = $conn->query($revenue_query);

                if ($revenue_result && $revenue_result->num_rows > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-child"></i> اسم الطالب</th>';
                    echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                    echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                    echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                    echo '<th><i class="fas fa-receipt"></i> رقم الوصل</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = $revenue_result->fetch_assoc()) {
                        $total += $row['cash_stud'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['name']) . '</strong></td>';
                        echo '<td><span class="badge bg-success fs-6">IQD ' . number_format($row['cash_stud']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '<td><span class="badge bg-info">' . $row['id_pay'] . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-success">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="3">' . $revenue_result->num_rows . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد إيرادات</h5>';
                    echo '<p>لم يتم العثور على إيرادات للفترة المحددة</p>';
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض الإيرادات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'expenses'): ?>
        <!-- Expenses Section -->
        <div class="content-area">
            <h4><i class="fas fa-arrow-down text-danger"></i> تفاصيل المصروفات</h4>

            <?php
            try {
                $user_condition = ($filter_user > 0) ? " AND depit_tb.userID = " . intval($filter_user) : "";
                $date_condition = "";
                if ($date_from && $date_to) {
                    $date_condition = " AND DATE(depit_tb.depit_date) BETWEEN '" . $conn->real_escape_string($date_from) . "' AND '" . $conn->real_escape_string($date_to) . "'";
                }

                $offset = ($page - 1) * $per_page;
                $expenses_query = "SELECT depit_tb.*, users_tb.user_name
                                   FROM depit_tb
                                   INNER JOIN users_tb ON depit_tb.userID = users_tb.id_user
                                   WHERE depit_tb.depit_cash > 0
                                   $user_condition $date_condition
                                   ORDER BY depit_tb.depit_date DESC
                                   LIMIT $per_page OFFSET $offset";

                $expenses_result = $conn->query($expenses_query);

                if ($expenses_result && $expenses_result->num_rows > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-sticky-note"></i> وصف المصروف</th>';
                    echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
                    echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
                    echo '<th><i class="fas fa-user"></i> المستخدم</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = $expenses_result->fetch_assoc()) {
                        $total += $row['depit_cash'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['depit_note']) . '</strong></td>';
                        echo '<td><span class="badge bg-danger fs-6">IQD ' . number_format($row['depit_cash']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-danger">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="2">' . $expenses_result->num_rows . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد مصروفات</h5>';
                    echo '<p>لم يتم العثور على مصروفات للفترة المحددة</p>';
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض المصروفات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'salaries'): ?>
        <!-- Salaries Section -->
        <div class="content-area">
            <h4><i class="fas fa-users text-warning"></i> تفاصيل الرواتب</h4>

            <?php
            try {
                $user_condition = ($filter_user > 0) ? " AND employ_tb.userID = " . intval($filter_user) : "";

                $offset = ($page - 1) * $per_page;
                $salaries_query = "SELECT employ_tb.*, users_tb.user_name
                                   FROM employ_tb
                                   INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user
                                   WHERE employ_tb.salary > 0
                                   $user_condition
                                   ORDER BY employ_tb.date_start DESC
                                   LIMIT $per_page OFFSET $offset";

                $salaries_result = $conn->query($salaries_query);

                if ($salaries_result && $salaries_result->num_rows > 0) {
                    $total = 0;
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th><i class="fas fa-user"></i> اسم الموظف</th>';
                    echo '<th><i class="fas fa-briefcase"></i> المنصب</th>';
                    echo '<th><i class="fas fa-money-bill"></i> الراتب</th>';
                    echo '<th><i class="fas fa-calendar"></i> تاريخ البداية</th>';
                    echo '<th><i class="fas fa-user-tie"></i> المستخدم</th>';
                    echo '<th><i class="fas fa-phone"></i> الهاتف</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    while ($row = $salaries_result->fetch_assoc()) {
                        $total += $row['salary'];
                        echo '<tr>';
                        echo '<td><strong>' . htmlspecialchars($row['f_name']) . '</strong></td>';
                        echo '<td>' . htmlspecialchars($row['job']) . '</td>';
                        echo '<td><span class="badge bg-warning text-dark fs-6">IQD ' . number_format($row['salary']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['date_start'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '<td>' . htmlspecialchars($row['phone']) . '</td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-warning">';
                    echo '<th colspan="2">الإجمالي:</th>';
                    echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
                    echo '<th colspan="3">' . $salaries_result->num_rows . ' موظف</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning text-center">';
                    echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
                    echo '<h5>لا توجد رواتب</h5>';
                    echo '<p>لم يتم العثور على رواتب</p>';
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض الرواتب: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'profit_loss'): ?>
        <!-- Profit & Loss Statement -->
        <div class="content-area">
            <h4><i class="fas fa-chart-line text-info"></i> قائمة الأرباح والخسائر</h4>

            <div class="row">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-plus-circle"></i> الإيرادات</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>رسوم الطلاب</td>
                                    <td class="text-end"><strong><?= number_format($stats['revenue']['total']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إيرادات أخرى</td>
                                    <td class="text-end"><strong>0</strong></td>
                                </tr>
                                <tr class="table-success">
                                    <th>إجمالي الإيرادات</th>
                                    <th class="text-end"><?= number_format($stats['revenue']['total']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-minus-circle"></i> المصروفات</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>الرواتب</td>
                                    <td class="text-end"><strong><?= number_format($stats['salaries']['total']) ?></strong></td>
                                </tr>
                                <tr>
                                    <td>مصروفات تشغيلية</td>
                                    <td class="text-end"><strong><?= number_format($stats['expenses']['total']) ?></strong></td>
                                </tr>
                                <tr class="table-danger">
                                    <th>إجمالي المصروفات</th>
                                    <th class="text-end"><?= number_format($stats['expenses']['total'] + $stats['salaries']['total']) ?></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?>">
                        <div class="card-header bg-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?> text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator"></i>
                                <?= $stats['net_profit'] >= 0 ? 'صافي الربح' : 'صافي الخسارة' ?>
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-<?= $stats['net_profit'] >= 0 ? 'success' : 'danger' ?>">
                                <?= number_format($stats['net_profit']) ?> دينار عراقي
                            </h2>
                            <p class="text-muted">
                                نسبة الربح: <?= $stats['revenue']['total'] > 0 ? number_format(($stats['net_profit'] / $stats['revenue']['total']) * 100, 2) : 0 ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle Sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }

        // Responsive sidebar for mobile
        if (window.innerWidth <= 768) {
            document.getElementById('sidebar').classList.add('collapsed');
            document.getElementById('mainContent').classList.add('expanded');
        }
    </script>
</body>
</html>

<?php
if ($conn) {
    $conn->close();
}
?>
