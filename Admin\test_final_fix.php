<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/topbar.php";
include "addon/dbcon.php";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح النهائي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 50px auto;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .success-box {
            background: #f0fdf4;
            border: 1px solid #10b981;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #065f46;
        }
        
        .test-button {
            background: linear-gradient(45deg, #059669, #10b981);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: linear-gradient(45deg, #047857, #059669);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px;
            margin: 5px 0;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 8px;
            border-right: 4px solid #10b981;
        }
        
        .feature-list li i {
            color: #10b981;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>✅ تم إصلاح صفحة الطلاب بنجاح!</h1>
            <p>جميع المشاكل تم حلها والميزات الجديدة تم إضافتها</p>
        </div>

        <div class="test-card">
            <h3>🎉 الإصلاحات المكتملة</h3>
            <div class="success-box">
                <h5><i class="fas fa-check-circle"></i> تم إنجاز جميع المطلوب:</h5>
                <ul class="feature-list">
                    <li><i class="fas fa-database"></i> <strong>ربط الملف بقاعدة البيانات:</strong> تم إصلاح الاستعلامات وربطها بقاعدة البيانات</li>
                    <li><i class="fas fa-users"></i> <strong>عرض البيانات حسب المستخدم:</strong> عند اختيار مستخدم تظهر بياناته فقط</li>
                    <li><i class="fas fa-sort"></i> <strong>أزرار الترتيب:</strong> زر تصاعدي (↑) وتنازلي (↓) لترتيب البيانات</li>
                    <li><i class="fas fa-table"></i> <strong>دعم الحقول الجديدة:</strong> الحالة الصحية وحالة التسجيل</li>
                    <li><i class="fas fa-shield-alt"></i> <strong>معالجة الأخطاء:</strong> رسائل خطأ واضحة ومفيدة</li>
                    <li><i class="fas fa-mobile-alt"></i> <strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>
                </ul>
            </div>
        </div>

        <div class="test-card">
            <h3>🔧 التحديثات المطبقة</h3>
            <div class="success-box">
                <h5>الملفات المحدثة:</h5>
                <ul>
                    <li><strong>Admin/addon/infostudF.php:</strong> إصلاح الاستعلامات ودعم الترتيب</li>
                    <li><strong>Admin/infost.php:</strong> إضافة أزرار الترتيب ووظائف JavaScript</li>
                    <li><strong>fix_database_simple.sql:</strong> ملف SQL لإضافة الحقول المطلوبة</li>
                </ul>
            </div>
        </div>

        <div class="test-card">
            <h3>📊 فحص قاعدة البيانات</h3>
            <?php
            // فحص وجود الحقول
            $health_exists = false;
            $registration_exists = false;
            
            $check_health = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'health_status'");
            $check_registration = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'registration_status'");
            
            if ($check_health && mysqli_num_rows($check_health) > 0) {
                $health_exists = true;
            }
            
            if ($check_registration && mysqli_num_rows($check_registration) > 0) {
                $registration_exists = true;
            }
            
            // إحصائيات
            $total_users = mysqli_num_rows(mysqli_query($con, "SELECT id_user FROM users_tb WHERE role = 'User'"));
            $total_students = mysqli_num_rows(mysqli_query($con, "SELECT id FROM stud_tb"));
            ?>
            
            <div class="success-box">
                <h5>حالة قاعدة البيانات:</h5>
                <p><strong>حقل health_status:</strong> 
                    <span style="color: <?php echo $health_exists ? '#10b981' : '#ef4444'; ?>">
                        <?php echo $health_exists ? '✅ موجود' : '❌ غير موجود'; ?>
                    </span>
                </p>
                <p><strong>حقل registration_status:</strong> 
                    <span style="color: <?php echo $registration_exists ? '#10b981' : '#ef4444'; ?>">
                        <?php echo $registration_exists ? '✅ موجود' : '❌ غير موجود'; ?>
                    </span>
                </p>
                <p><strong>عدد المستخدمين:</strong> <?php echo $total_users; ?></p>
                <p><strong>عدد الطلاب:</strong> <?php echo $total_students; ?></p>
                
                <?php if (!$health_exists || !$registration_exists): ?>
                <div style="background: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; padding: 15px; margin-top: 15px; color: #991b1b;">
                    <strong>تحذير:</strong> بعض الحقول غير موجودة. يرجى تشغيل ملف fix_database_simple.sql
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="test-card">
            <h3>🚀 كيفية الاستخدام</h3>
            <div class="success-box">
                <h5>خطوات الاستخدام:</h5>
                <ol>
                    <li><strong>إذا كانت الحقول غير موجودة:</strong> شغل ملف fix_database_simple.sql في phpMyAdmin</li>
                    <li><strong>اذهب إلى صفحة الطلاب:</strong> Admin/infost.php</li>
                    <li><strong>اختر مستخدم:</strong> من القائمة المنسدلة</li>
                    <li><strong>استخدم أزرار الترتيب:</strong> ↑ للترتيب التصاعدي، ↓ للترتيب التنازلي</li>
                    <li><strong>تصفح البيانات:</strong> استخدم البحث والفلترة</li>
                </ol>
            </div>
        </div>

        <div class="test-card">
            <h3>🔗 اختبار الصفحات</h3>
            <div style="text-align: center;">
                <a href="infost.php" class="test-button" target="_blank">
                    <i class="fas fa-users"></i>
                    اختبار صفحة الطلاب
                </a>
                <a href="addstud.php" class="test-button" target="_blank">
                    <i class="fas fa-user-plus"></i>
                    إضافة طالب جديد
                </a>
                <a href="../fix_database_simple.sql" class="test-button" target="_blank">
                    <i class="fas fa-database"></i>
                    ملف SQL
                </a>
            </div>
        </div>

        <div class="test-card">
            <h3>📝 ملاحظات مهمة</h3>
            <div class="success-box">
                <ul>
                    <li><strong>الترتيب الافتراضي:</strong> تنازلي (من الأحدث للأقدم)</li>
                    <li><strong>أزرار الترتيب:</strong> في رأس الجدول بجانب كلمة "الترتيب"</li>
                    <li><strong>البيانات:</strong> تظهر حسب المستخدم المختار فقط</li>
                    <li><strong>الحقول الجديدة:</strong> تظهر في الجدول مع شارات ملونة</li>
                    <li><strong>معالجة الأخطاء:</strong> رسائل واضحة في حالة وجود مشاكل</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/all.min.js"></script>
</body>
</html>
