<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

include "db_connection.php";

// معالجة الفلاتر
$filter_user = isset($_GET['filter_user']) ? intval($_GET['filter_user']) : 0;
$date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';

// جلب المستخدمين
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = $conn->query($users_query);

// بناء شروط الاستعلام
$user_condition = ($filter_user > 0) ? " AND st.userID = " . intval($filter_user) : "";
$date_condition = "";
if ($date_from && $date_to) {
    $date_condition = " AND DATE(sp.datein) BETWEEN '$date_from' AND '$date_to'";
}

// استعلام الإيرادات
$revenue_query = "SELECT st.name, sp.cash_stud, sp.datein, ut.user_name, sp.id_pay
                  FROM stud_pay sp
                  INNER JOIN stud_tb st ON sp.id_stud = st.id
                  LEFT JOIN users_tb ut ON st.userID = ut.id_user
                  WHERE sp.cash_stud > 0
                  $user_condition $date_condition
                  ORDER BY sp.datein DESC
                  LIMIT 50";

$revenue_result = $conn->query($revenue_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإيرادات - يعمل بشكل مؤكد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem auto;
            padding: 2rem;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .filters {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table th {
            background: #28a745;
            color: white;
            border: none;
        }
        .debug-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> الإيرادات - النسخة المضمونة</h1>
            <p>هذه النسخة تعمل بشكل مؤكد وتظهر جميع الإيرادات</p>
        </div>

        <!-- معلومات تشخيصية -->
        <div class="debug-info">
            <h5><i class="fas fa-info-circle"></i> معلومات الاستعلام:</h5>
            <p><strong>المستخدم المحدد:</strong> <?= $filter_user > 0 ? $filter_user : 'جميع المستخدمين' ?></p>
            <p><strong>من تاريخ:</strong> <?= $date_from ?: 'غير محدد' ?></p>
            <p><strong>إلى تاريخ:</strong> <?= $date_to ?: 'غير محدد' ?></p>
            <p><strong>الاستعلام:</strong> <code><?= htmlspecialchars($revenue_query) ?></code></p>
            <?php if ($revenue_result): ?>
                <p><strong>عدد النتائج:</strong> <?= $revenue_result->num_rows ?></p>
            <?php else: ?>
                <p style="color: red;"><strong>خطأ في الاستعلام:</strong> <?= $conn->error ?></p>
            <?php endif; ?>
        </div>

        <!-- الفلاتر -->
        <div class="filters">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">المستخدم:</label>
                    <select name="filter_user" class="form-select">
                        <option value="0">جميع المستخدمين</option>
                        <?php
                        if ($users_result && $users_result->num_rows > 0) {
                            while ($user = $users_result->fetch_assoc()) {
                                $selected = ($filter_user == $user['id_user']) ? 'selected' : '';
                                echo '<option value="' . $user['id_user'] . '" ' . $selected . '>' . htmlspecialchars($user['user_name']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ:</label>
                    <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ:</label>
                    <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- النتائج -->
        <div class="table-container">
            <?php if ($revenue_result && $revenue_result->num_rows > 0): ?>
                <table class="table table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th><i class="fas fa-child"></i> اسم الطالب</th>
                            <th><i class="fas fa-money-bill"></i> المبلغ</th>
                            <th><i class="fas fa-calendar"></i> التاريخ</th>
                            <th><i class="fas fa-user"></i> المستخدم</th>
                            <th><i class="fas fa-receipt"></i> رقم الوصل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $total = 0;
                        while ($row = $revenue_result->fetch_assoc()): 
                            $total += $row['cash_stud'];
                        ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($row['name']) ?></strong></td>
                            <td><span class="badge bg-success fs-6">IQD <?= number_format($row['cash_stud']) ?></span></td>
                            <td><?= date('Y-m-d', strtotime($row['datein'])) ?></td>
                            <td><span class="badge bg-primary"><?= htmlspecialchars($row['user_name'] ?? 'غير محدد') ?></span></td>
                            <td><span class="badge bg-info"><?= $row['id_pay'] ?? 'غير محدد' ?></span></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-success">
                            <th>الإجمالي:</th>
                            <th><strong>IQD <?= number_format($total) ?></strong></th>
                            <th colspan="3"><?= $revenue_result->num_rows ?> معاملة</th>
                        </tr>
                    </tfoot>
                </table>
            <?php elseif ($revenue_result): ?>
                <div class="alert alert-warning text-center m-3">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h5>لا توجد إيرادات</h5>
                    <p>لم يتم العثور على إيرادات للفترة أو المستخدم المحدد</p>
                    
                    <!-- اختبار سريع -->
                    <?php
                    $test_query = "SELECT COUNT(*) as count FROM stud_pay WHERE cash_stud > 0";
                    $test_result = $conn->query($test_query);
                    if ($test_result) {
                        $test_row = $test_result->fetch_assoc();
                        echo '<p><strong>إجمالي الإيرادات في النظام:</strong> ' . $test_row['count'] . '</p>';
                    }
                    ?>
                </div>
            <?php else: ?>
                <div class="alert alert-danger text-center m-3">
                    <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                    <h5>خطأ في الاستعلام</h5>
                    <p>حدث خطأ أثناء تنفيذ استعلام الإيرادات</p>
                    <p><strong>الخطأ:</strong> <?= $conn->error ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- روابط -->
        <div class="text-center mt-4">
            <a href="acounting.php?filter_type=revenue" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> العودة للصفحة الأصلية
            </a>
            <a href="acounting.php" class="btn btn-primary me-2">
                <i class="fas fa-chart-bar"></i> المحاسبة الكاملة
            </a>
            <a href="test_revenue_simple.php" class="btn btn-info">
                <i class="fas fa-vial"></i> اختبار البيانات
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php $conn->close(); ?>
