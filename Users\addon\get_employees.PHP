<?php
// addon/get_attendance.php
session_start();
include "dbcon.php";

if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

if (isset($_POST['date'])) {
    $user_id = $_SESSION['user']->id_user;
    $date = $_POST['date'];
    
    if ($date === 'all') {
        // Get all attendance records
        $sql = "SELECT stat2.id, stat2.data_stat, stat2.stat_employee, 
                       employ_tb.f_name, users_tb.user_name, stat2.id_employee 
                FROM stat2 
                INNER JOIN employ_tb ON stat2.id_employee = employ_tb.id_employ 
                INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user 
                WHERE users_tb.id_user = ? 
                ORDER BY stat2.data_stat DESC";
        
        $stmt = mysqli_prepare($con, $sql);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
    } else {
        // Get attendance records for specific date
        $sql = "SELECT stat2.id, stat2.data_stat, stat2.stat_employee, 
                       employ_tb.f_name, users_tb.user_name, stat2.id_employee 
                FROM stat2 
                INNER JOIN employ_tb ON stat2.id_employee = employ_tb.id_employ 
                INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user 
                WHERE users_tb.id_user = ? AND DATE(stat2.data_stat) = ? 
                ORDER BY stat2.data_stat DESC";
        
        $stmt = mysqli_prepare($con, $sql);
        mysqli_stmt_bind_param($stmt, "is", $user_id, $date);
    }
    
    if ($stmt) {
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $attendance = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $attendance[] = $row;
        }
        
        mysqli_stmt_close($stmt);
        
        header('Content-Type: application/json');
        echo json_encode($attendance);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Database error']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request']);
}

mysqli_close($con);
?>