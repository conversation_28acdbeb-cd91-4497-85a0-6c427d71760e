<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول طلبات الإجازة
$create_leave_requests = "CREATE TABLE IF NOT EXISTS leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    leave_type VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_leave_requests);

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'approve_leave':
            $leave_id = (int)($_POST['leave_id'] ?? 0);
            $admin_notes = mysqli_real_escape_string($con, $_POST['admin_notes'] ?? '');
            
            if ($leave_id > 0) {
                $sql = "UPDATE leave_requests SET status = 'approved', admin_notes = '$admin_notes', updated_at = NOW() WHERE id = $leave_id";
                if (@mysqli_query($con, $sql)) {
                    $message = 'تم الموافقة على طلب الإجازة بنجاح!';
                } else {
                    $message = 'حدث خطأ أثناء الموافقة على الطلب!';
                    $messageType = 'error';
                }
            }
            break;
            
        case 'reject_leave':
            $leave_id = (int)($_POST['leave_id'] ?? 0);
            $admin_notes = mysqli_real_escape_string($con, $_POST['admin_notes'] ?? '');
            
            if ($leave_id > 0) {
                $sql = "UPDATE leave_requests SET status = 'rejected', admin_notes = '$admin_notes', updated_at = NOW() WHERE id = $leave_id";
                if (@mysqli_query($con, $sql)) {
                    $message = 'تم رفض طلب الإجازة!';
                } else {
                    $message = 'حدث خطأ أثناء رفض الطلب!';
                    $messageType = 'error';
                }
            }
            break;
    }
}

// جلب طلبات الإجازة
$leave_requests = @mysqli_query($con, "SELECT lr.*, s.name as student_name FROM leave_requests lr LEFT JOIN stud_tb s ON lr.student_id = s.id ORDER BY lr.created_at DESC");
if (!$leave_requests) {
    $leave_requests = false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📅 طلبات الإجازة - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container-fluid {
            max-width: 1400px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .content-card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .leave-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .leave-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .leave-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .leave-info h5 {
            color: #2c3e50;
            margin: 0;
            font-weight: bold;
        }

        .leave-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .badge-custom {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending { background: #ffc107; color: #212529; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }

        .type-sick { background: #e74c3c; color: white; }
        .type-emergency { background: #f39c12; color: white; }
        .type-personal { background: #3498db; color: white; }
        .type-family { background: #9b59b6; color: white; }
        .type-other { background: #95a5a6; color: white; }

        .leave-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .leave-dates {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .date-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #2c3e50;
        }

        .leave-reason {
            background: #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            line-height: 1.6;
            color: #2c3e50;
        }

        .admin-notes {
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            border-right: 4px solid #667eea;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        .btn-approve {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-approve:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .btn-reject {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
            color: white;
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px 20px 0 0;
            border: none;
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .alert-error-custom {
            background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border-right: 5px solid #e74c3c;
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .leave-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .leave-dates {
                flex-direction: column;
                gap: 1rem;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container-fluid">
        <!-- Header -->
        <div class="header-card">
            <h1>📅 طلبات الإجازة</h1>
            <p style="color: #7f8c8d; margin: 0;">مراجعة والموافقة على طلبات الإجازة من الطلاب</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom <?php echo $messageType == 'success' ? 'alert-success-custom' : 'alert-error-custom'; ?>">
                <i class="fas <?php echo $messageType == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Leave Requests List -->
        <div class="content-card">
            <div class="content-card-header">
                <i class="fas fa-calendar-minus"></i> جميع طلبات الإجازة
            </div>
            <div class="content-card-body">
                <?php if ($leave_requests && mysqli_num_rows($leave_requests) > 0): ?>
                    <?php while ($row = mysqli_fetch_assoc($leave_requests)): 
                        $status_classes = [
                            'pending' => 'status-pending',
                            'approved' => 'status-approved',
                            'rejected' => 'status-rejected'
                        ];
                        $type_classes = [
                            'sick' => 'type-sick',
                            'emergency' => 'type-emergency',
                            'personal' => 'type-personal',
                            'family' => 'type-family',
                            'other' => 'type-other'
                        ];
                        $status_names = [
                            'pending' => 'في الانتظار',
                            'approved' => 'موافق عليها',
                            'rejected' => 'مرفوضة'
                        ];
                        $type_names = [
                            'sick' => 'إجازة مرضية',
                            'emergency' => 'إجازة طارئة',
                            'personal' => 'إجازة شخصية',
                            'family' => 'إجازة عائلية',
                            'other' => 'أخرى'
                        ];
                    ?>
                        <div class="leave-item">
                            <div class="leave-header">
                                <div class="leave-info">
                                    <h5><?php echo htmlspecialchars($row['student_name'] ?? 'غير محدد'); ?></h5>
                                    <small class="text-muted">
                                        تم التقديم في: <?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="leave-meta">
                                    <span class="badge-custom <?php echo $type_classes[$row['leave_type']] ?? 'type-other'; ?>">
                                        <?php echo $type_names[$row['leave_type']] ?? $row['leave_type']; ?>
                                    </span>
                                    <span class="badge-custom <?php echo $status_classes[$row['status']] ?? 'status-pending'; ?>">
                                        <?php echo $status_names[$row['status']] ?? $row['status']; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="leave-details">
                                <div class="leave-dates">
                                    <div class="date-info">
                                        <i class="fas fa-calendar-plus" style="color: #28a745;"></i>
                                        <strong>من:</strong> <?php echo $row['start_date']; ?>
                                    </div>
                                    <div class="date-info">
                                        <i class="fas fa-calendar-minus" style="color: #dc3545;"></i>
                                        <strong>إلى:</strong> <?php echo $row['end_date']; ?>
                                    </div>
                                    <div class="date-info">
                                        <i class="fas fa-clock" style="color: #ffc107;"></i>
                                        <strong>المدة:</strong> 
                                        <?php 
                                        $start = new DateTime($row['start_date']);
                                        $end = new DateTime($row['end_date']);
                                        $diff = $start->diff($end);
                                        echo ($diff->days + 1) . ' يوم';
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="leave-reason">
                                <h6 style="color: #2c3e50; margin-bottom: 0.5rem;">
                                    <i class="fas fa-comment-alt"></i> سبب الإجازة:
                                </h6>
                                <?php echo nl2br(htmlspecialchars($row['reason'])); ?>
                            </div>
                            
                            <?php if (!empty($row['admin_notes'])): ?>
                                <div class="admin-notes">
                                    <h6 style="color: #667eea; margin-bottom: 0.5rem;">
                                        <i class="fas fa-user-tie"></i> ملاحظات الإدارة:
                                    </h6>
                                    <p style="margin: 0; line-height: 1.6;">
                                        <?php echo nl2br(htmlspecialchars($row['admin_notes'])); ?>
                                    </p>
                                    <small class="text-muted">
                                        تم التحديث في: <?php echo date('Y-m-d H:i', strtotime($row['updated_at'])); ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($row['status'] == 'pending'): ?>
                                <div class="action-buttons">
                                    <button type="button" class="btn-approve" onclick="approveLeave(<?php echo $row['id']; ?>)">
                                        <i class="fas fa-check"></i> موافقة
                                    </button>
                                    <button type="button" class="btn-reject" onclick="rejectLeave(<?php echo $row['id']; ?>)">
                                        <i class="fas fa-times"></i> رفض
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="text-center" style="padding: 3rem;">
                        <i class="fas fa-calendar-minus" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                        <h4 style="color: #7f8c8d;">لا توجد طلبات إجازة حتى الآن</h4>
                        <p style="color: #95a5a6;">عندما يقدم الطلاب طلبات إجازة، ستظهر هنا</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الموافقة على طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="approve_leave">
                        <input type="hidden" name="leave_id" id="approveLeaveId">
                        <div class="mb-3">
                            <label class="form-label">ملاحظات الموافقة (اختياري):</label>
                            <textarea name="admin_notes" class="form-control" rows="4" placeholder="يمكنك إضافة ملاحظات للطالب..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn-approve">
                            <i class="fas fa-check"></i> الموافقة على الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفض طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reject_leave">
                        <input type="hidden" name="leave_id" id="rejectLeaveId">
                        <div class="mb-3">
                            <label class="form-label">سبب الرفض:</label>
                            <textarea name="admin_notes" class="form-control" rows="4" required placeholder="يرجى توضيح سبب رفض الطلب..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn-reject">
                            <i class="fas fa-times"></i> رفض الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function approveLeave(leaveId) {
            document.getElementById('approveLeaveId').value = leaveId;
            const modal = new bootstrap.Modal(document.getElementById('approveModal'));
            modal.show();
        }

        function rejectLeave(leaveId) {
            document.getElementById('rejectLeaveId').value = leaveId;
            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
