<?php
include "addon/dbcon.php";

echo "<h2>إعداد قاعدة البيانات لنظام الطلاب</h2>";

// إنشاء جدول إجازات الطلاب
$sql1 = "CREATE TABLE IF NOT EXISTS `student_leaves` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `student_name` varchar(255) NOT NULL,
  `leave_type` varchar(100) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reason` text NOT NULL,
  `request_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) NOT NULL DEFAULT 'قيد المراجعة',
  `admin_response` text,
  `response_by` int(11),
  `response_date` datetime,
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if (mysqli_query($con, $sql1)) {
    echo "<p style='color: green;'>✅ تم إنشاء جدول student_leaves بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول student_leaves: " . mysqli_error($con) . "</p>";
}

// إنشاء جدول العطل الرسمية
$sql2 = "CREATE TABLE IF NOT EXISTS `official_holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `holiday_name` varchar(255) NOT NULL,
  `holiday_date` date NOT NULL,
  `description` text,
  `duration_days` int(11) DEFAULT 1,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if (mysqli_query($con, $sql2)) {
    echo "<p style='color: green;'>✅ تم إنشاء جدول official_holidays بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول official_holidays: " . mysqli_error($con) . "</p>";
}

// إنشاء جدول الأخبار والإعلانات
$sql3 = "CREATE TABLE IF NOT EXISTS `news_announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(100) DEFAULT 'إعلان',
  `priority` varchar(50) DEFAULT 'متوسطة',
  `status` varchar(50) DEFAULT 'نشط',
  `created_by` int(11),
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if (mysqli_query($con, $sql3)) {
    echo "<p style='color: green;'>✅ تم إنشاء جدول news_announcements بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول news_announcements: " . mysqli_error($con) . "</p>";
}

// إنشاء جدول الإشعارات
$sql4 = "CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_role` varchar(50) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `message` text NOT NULL,
  `type` varchar(100) DEFAULT 'general',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT 0,
  `related_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_role` (`user_role`),
  KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if (mysqli_query($con, $sql4)) {
    echo "<p style='color: green;'>✅ تم إنشاء جدول notifications بنجاح</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول notifications: " . mysqli_error($con) . "</p>";
}

// التحقق من وجود عمود student_request في جدول leave_requests
$check_column = "SHOW COLUMNS FROM leave_requests LIKE 'student_request'";
$column_result = mysqli_query($con, $check_column);

if ($column_result && mysqli_num_rows($column_result) == 0) {
    // إضافة العمود إذا لم يكن موجوداً
    $add_column = "ALTER TABLE leave_requests ADD COLUMN student_request tinyint(1) DEFAULT 0";
    if (mysqli_query($con, $add_column)) {
        echo "<p style='color: green;'>✅ تم إضافة عمود student_request إلى جدول leave_requests</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ تعذر إضافة عمود student_request: " . mysqli_error($con) . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ عمود student_request موجود بالفعل في جدول leave_requests</p>";
}

// إدراج بيانات تجريبية للعطل الرسمية
$holidays_data = [
    ['رأس السنة الميلادية', '2024-01-01', 'عطلة رأس السنة الميلادية', 1],
    ['عيد الفطر المبارك', '2024-04-10', 'عطلة عيد الفطر المبارك', 3],
    ['عيد الأضحى المبارك', '2024-06-16', 'عطلة عيد الأضحى المبارك', 4],
    ['رأس السنة الهجرية', '2024-07-07', 'عطلة رأس السنة الهجرية', 1],
    ['المولد النبوي الشريف', '2024-09-15', 'عطلة المولد النبوي الشريف', 1],
    ['عطلة نصف السنة', '2024-12-25', 'عطلة نهاية العام الدراسي', 7]
];

$holidays_inserted = 0;
foreach ($holidays_data as $holiday) {
    $check_holiday = "SELECT id FROM official_holidays WHERE holiday_name = '" . $holiday[0] . "' AND holiday_date = '" . $holiday[1] . "'";
    $holiday_result = mysqli_query($con, $check_holiday);
    
    if (mysqli_num_rows($holiday_result) == 0) {
        $insert_holiday = "INSERT INTO official_holidays (holiday_name, holiday_date, description, duration_days) 
                          VALUES ('" . $holiday[0] . "', '" . $holiday[1] . "', '" . $holiday[2] . "', " . $holiday[3] . ")";
        if (mysqli_query($con, $insert_holiday)) {
            $holidays_inserted++;
        }
    }
}

if ($holidays_inserted > 0) {
    echo "<p style='color: green;'>✅ تم إدراج $holidays_inserted عطلة رسمية</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ العطل الرسمية موجودة بالفعل</p>";
}

// إدراج بيانات تجريبية للأخبار
$news_data = [
    ['بداية العام الدراسي الجديد', 'نرحب بجميع الطلاب في بداية العام الدراسي الجديد. نتمنى لكم عاماً مليئاً بالنجاح والتفوق.', 'إعلان', 'عالية'],
    ['تحديث في نظام الحضور', 'تم تحديث نظام الحضور الإلكتروني. يرجى من جميع الطلاب التأكد من تسجيل الحضور يومياً.', 'تحديث', 'متوسطة'],
    ['فعالية ترفيهية قادمة', 'ستقام فعالية ترفيهية للطلاب يوم الجمعة القادم. سيتم الإعلان عن التفاصيل قريباً.', 'فعالية', 'منخفضة']
];

$news_inserted = 0;
foreach ($news_data as $news) {
    $check_news = "SELECT id FROM news_announcements WHERE title = '" . $news[0] . "'";
    $news_result = mysqli_query($con, $check_news);
    
    if (mysqli_num_rows($news_result) == 0) {
        $insert_news = "INSERT INTO news_announcements (title, content, type, priority) 
                       VALUES ('" . $news[0] . "', '" . $news[1] . "', '" . $news[2] . "', '" . $news[3] . "')";
        if (mysqli_query($con, $insert_news)) {
            $news_inserted++;
        }
    }
}

if ($news_inserted > 0) {
    echo "<p style='color: green;'>✅ تم إدراج $news_inserted خبر/إعلان</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ الأخبار والإعلانات موجودة بالفعل</p>";
}

echo "<br><h3>✅ تم إعداد قاعدة البيانات بنجاح!</h3>";
echo "<p><a href='simple_login.php' style='color: #007bff;'>الذهاب إلى صفحة تسجيل الدخول</a></p>";
echo "<p><a href='test_login.php' style='color: #007bff;'>اختبار بيانات تسجيل الدخول</a></p>";

// اختبار إدراج طلب إجازة تجريبي
echo "<br><h4>اختبار إدراج طلب إجازة:</h4>";
$test_sql = "INSERT INTO student_leaves (student_id, student_name, leave_type, start_date, end_date, reason, status) 
             VALUES (1, 'طالب تجريبي', 'مرضية', '2024-01-15', '2024-01-16', 'اختبار النظام', 'قيد المراجعة')";

if (mysqli_query($con, $test_sql)) {
    echo "<p style='color: green;'>✅ تم إدراج طلب إجازة تجريبي بنجاح</p>";
    
    // حذف الطلب التجريبي
    $delete_test = "DELETE FROM student_leaves WHERE student_name = 'طالب تجريبي'";
    mysqli_query($con, $delete_test);
    echo "<p style='color: blue;'>ℹ️ تم حذف الطلب التجريبي</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إدراج طلب إجازة تجريبي: " . mysqli_error($con) . "</p>";
}

mysqli_close($con);
?>

<style>
    body {
        font-family: Arial, sans-serif;
        direction: rtl;
        padding: 20px;
        background: #f5f5f5;
    }
    
    h2, h3, h4 {
        color: #2c3e50;
    }
    
    p {
        margin: 10px 0;
        padding: 8px;
        border-radius: 5px;
    }
    
    a {
        text-decoration: none;
        font-weight: bold;
    }
    
    a:hover {
        text-decoration: underline;
    }
</style>
