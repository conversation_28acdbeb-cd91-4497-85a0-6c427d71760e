<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

include "dbcon.php";
    
 if(isset($_GET['input']))
    {
      $Filter=$_GET['input'];
      $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND users_tb.id_user={$_SESSION['user']->id_user} AND CONCAT(id_note,userID,name,age,sex,catg,datein,date_exp,p_name,p_phone,loc,id_pay,user_name) LIKE '%$Filter%'";
      $query_run=mysqli_query($con,$query);
      ?>
      <thead>
    <tr>
    <th scope="col">  العمليات  </th>
    <th scope="col"> مستخدم الحضانة  </th>
    <th scope="col"> الايام المتبقية </th>
      <th scope="col">حالة الاشتراك</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ النفاذ </th>
      <th scope="col">تاريخ الاشتراك</th>
      <th scope="col">رقم ولي الامر</th>
      <th scope="col">اسم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">السكن</th>
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
      <th scope="col">اسم الطالب  </th>
      <th scope="col">رقم  الوصل </th>
      
    </tr>
  </thead>
  <?php
      if(mysqli_num_rows($query_run)>0){
        foreach($query_run as $items){
          $id=$items['id'];
          $date_in=strtotime(date('y-m-d'));
          $date_out=strtotime($items['date_exp']);
          $stat=$date_out-$date_in;
          $cek=floor($stat/(60*60*24));
          if($cek<=0){
            $mes='<h3 class=exp>منتهي</h3>';
            $btndelet='<button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove" onclick="deletdata('.$id.')">حذف </button>';
            $btnv='<button type="button" class="btn btn-secondary mb-2" id="renew_btn" name="remove"><a href="renew.php?renewId='.$id.'" class="text-light">تجديد </a></button>';
        }elseif($cek<=10 & $cek>0){
          $btndelet="";
          $btnv="";
          $mes='<h3 class=soon >قريبا</h3>';
         }else{
            $mes='<h3 class=still >فعال</h3>';
            $btnv="";
            $btndelet="";
        }
          ?>
            <tr id="tr_<?php echo $id?>">
          <td><button type="button" class="btn btn-secondary mb-1" id="edit_bnt" name="update"> <a href=editstud.php?id=<?=$id?> class="text-light">تعديل </a></button>
          <?php echo  $btndelet?>
          <?php echo  $btnv?>
          </td>
          <td><?= $items['user_name'];?></td>
          <td><?= $cek;?></td>
          <td><?= $mes;?></td>
          <td><?= $items['date_exp'];?></td>
          <td><?= number_format($items['cash_stud']);?></td>
          <td><?= $items['datein'];?></td>
          <td><?= $items['loc'];?></td>
          <td><?= $items['p_phone'];?></td>
          <td><?= $items['p_name'];?></td>
          <td><?= $items['catg'];?></td>
          <td><?= $items['sex'];?></td>
          <td><?= $items['age'];?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_pay'];?></td>
          </tr>
        <?php
 

        }
    }else{
        echo "<td colspan=14 style='font-size: 25px;'>لاتوجد بيانات بهذا الوصف</td>";
    }
    }



    ?>
   