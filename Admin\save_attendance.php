<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// جلب البيانات من الطلب
$employee_id = $_POST['employee_id'] ?? '';
$employee_name = $_POST['employee_name'] ?? '';
$attendance_date = $_POST['attendance_date'] ?? '';
$status = $_POST['status'] ?? '';
$check_in_time = $_POST['check_in_time'] ?? null;
$check_out_time = $_POST['check_out_time'] ?? null;
$notes = $_POST['notes'] ?? '';

// التحقق من البيانات المطلوبة
if (empty($employee_id) || empty($employee_name) || empty($attendance_date) || empty($status)) {
    echo json_encode([
        'success' => false,
        'error' => 'البيانات المطلوبة مفقودة'
    ]);
    exit();
}

try {
    // التحقق من وجود جدول الحضور
    $check_table = $con->query("SHOW TABLES LIKE 'employee_attendance'");
    if (!$check_table || $check_table->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'error' => 'جدول الحضور غير موجود',
            'action' => 'create_table'
        ]);
        exit();
    }
    
    // التحقق من وجود سجل سابق لنفس الموظف والتاريخ
    $check_existing = $con->prepare("SELECT id FROM employee_attendance WHERE employee_id = ? AND attendance_date = ?");
    $check_existing->bind_param("ss", $employee_id, $attendance_date);
    $check_existing->execute();
    $existing_result = $check_existing->get_result();
    
    if ($existing_result->num_rows > 0) {
        // تحديث السجل الموجود
        $update_query = "UPDATE employee_attendance SET 
                        employee_name = ?, 
                        status = ?, 
                        check_in_time = ?, 
                        check_out_time = ?, 
                        notes = ?, 
                        updated_at = CURRENT_TIMESTAMP 
                        WHERE employee_id = ? AND attendance_date = ?";
        
        $stmt = $con->prepare($update_query);
        $stmt->bind_param("sssssss", $employee_name, $status, $check_in_time, $check_out_time, $notes, $employee_id, $attendance_date);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث بيانات الحضور بنجاح',
                'action' => 'updated'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في تحديث البيانات: ' . $con->error
            ]);
        }
    } else {
        // إدراج سجل جديد
        $insert_query = "INSERT INTO employee_attendance 
                        (employee_id, employee_name, attendance_date, status, check_in_time, check_out_time, notes) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $con->prepare($insert_query);
        $stmt->bind_param("sssssss", $employee_id, $employee_name, $attendance_date, $status, $check_in_time, $check_out_time, $notes);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حفظ بيانات الحضور بنجاح',
                'action' => 'inserted'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في حفظ البيانات: ' . $con->error
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في العملية: ' . $e->getMessage()
    ]);
}

// إغلاق الاتصال
$con->close();
?>
