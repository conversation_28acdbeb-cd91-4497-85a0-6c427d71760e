<?php
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

include "db_connection.php";

echo "<h1>🧪 اختبار الإيرادات البسيط</h1>";

if ($conn) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // اختبار 1: فحص جدول stud_pay
    echo "<h2>📊 فحص جدول stud_pay:</h2>";
    $test1 = "SELECT COUNT(*) as total FROM stud_pay";
    $result1 = $conn->query($test1);
    if ($result1) {
        $row1 = $result1->fetch_assoc();
        echo "<p>إجمالي السجلات في stud_pay: <strong>" . $row1['total'] . "</strong></p>";
    }
    
    // اختبار 2: فحص السجلات التي تحتوي على مبالغ
    $test2 = "SELECT COUNT(*) as count, SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
    $result2 = $conn->query($test2);
    if ($result2) {
        $row2 = $result2->fetch_assoc();
        echo "<p>السجلات مع مبالغ > 0: <strong>" . $row2['count'] . "</strong></p>";
        echo "<p>إجمالي المبلغ: <strong>" . number_format($row2['total']) . " دينار</strong></p>";
    }
    
    // اختبار 3: فحص الربط مع جدول الطلاب
    echo "<h2>🔗 فحص الربط مع جدول الطلاب:</h2>";
    $test3 = "SELECT COUNT(*) as count FROM stud_pay sp INNER JOIN stud_tb st ON sp.id_stud = st.id WHERE sp.cash_stud > 0";
    $result3 = $conn->query($test3);
    if ($result3) {
        $row3 = $result3->fetch_assoc();
        echo "<p>السجلات المربوطة مع الطلاب: <strong>" . $row3['count'] . "</strong></p>";
    }
    
    // اختبار 4: فحص الربط مع جدول المستخدمين
    echo "<h2>👥 فحص الربط مع جدول المستخدمين:</h2>";
    $test4 = "SELECT COUNT(*) as count FROM stud_pay sp 
              INNER JOIN stud_tb st ON sp.id_stud = st.id 
              LEFT JOIN users_tb ut ON st.userID = ut.id_user 
              WHERE sp.cash_stud > 0";
    $result4 = $conn->query($test4);
    if ($result4) {
        $row4 = $result4->fetch_assoc();
        echo "<p>السجلات مع LEFT JOIN للمستخدمين: <strong>" . $row4['count'] . "</strong></p>";
    }
    
    // اختبار 5: عرض عينة من البيانات
    echo "<h2>📋 عينة من البيانات:</h2>";
    $test5 = "SELECT st.name, sp.cash_stud, sp.datein, ut.user_name, sp.id_pay
              FROM stud_pay sp
              INNER JOIN stud_tb st ON sp.id_stud = st.id
              LEFT JOIN users_tb ut ON st.userID = ut.id_user
              WHERE sp.cash_stud > 0
              ORDER BY sp.datein DESC
              LIMIT 5";
    $result5 = $conn->query($test5);
    
    if ($result5 && $result5->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>اسم الطالب</th><th>المبلغ</th><th>التاريخ</th><th>المستخدم</th><th>رقم الوصل</th>";
        echo "</tr>";
        
        while ($row = $result5->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . number_format($row['cash_stud']) . "</td>";
            echo "<td>" . $row['datein'] . "</td>";
            echo "<td>" . htmlspecialchars($row['user_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($row['id_pay'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد بيانات إيرادات</p>";
        
        if ($result5 === false) {
            echo "<p style='color: red;'>خطأ في الاستعلام: " . $conn->error . "</p>";
        }
    }
    
    // اختبار 6: فحص المستخدمين
    echo "<h2>👤 فحص المستخدمين:</h2>";
    $test6 = "SELECT COUNT(*) as count FROM users_tb WHERE role = 'User'";
    $result6 = $conn->query($test6);
    if ($result6) {
        $row6 = $result6->fetch_assoc();
        echo "<p>عدد المستخدمين: <strong>" . $row6['count'] . "</strong></p>";
        
        if ($row6['count'] > 0) {
            $users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' LIMIT 5";
            $users_result = $conn->query($users_query);
            if ($users_result && $users_result->num_rows > 0) {
                echo "<p><strong>عينة من المستخدمين:</strong></p>";
                echo "<ul>";
                while ($user = $users_result->fetch_assoc()) {
                    echo "<li>ID: " . $user['id_user'] . " - " . htmlspecialchars($user['user_name']) . "</li>";
                }
                echo "</ul>";
            }
        }
    }
    
    $conn->close();
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

echo "<br><br>";
echo "<a href='acounting.php?filter_type=revenue' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔙 العودة لصفحة الإيرادات</a>";
echo " ";
echo "<a href='acounting.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        direction: rtl;
        margin: 20px;
        background: #f8f9fa;
    }
    
    h1, h2 {
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
    }
    
    p {
        background: white;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    table {
        background: white;
        margin: 10px 0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    th, td {
        padding: 10px;
        text-align: center;
        border: 1px solid #ddd;
    }
    
    th {
        background: #3498db;
        color: white;
    }
    
    ul {
        background: white;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
</style>
