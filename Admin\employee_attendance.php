<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// معالجة الفلاتر
$selected_date = isset($_GET['attendance_date']) ? $_GET['attendance_date'] : date('Y-m-d');
$selected_employee = isset($_GET['employee_id']) ? $_GET['employee_id'] : '0';

// جلب الموظفين
try {
    // التحقق من هيكل الجدول أولاً
    $columns_query = "SHOW COLUMNS FROM employ_tb";
    $columns_result = $con->query($columns_query);
    $columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    // تحديد العمود المناسب للمعرف في جدول employ_tb
    $id_column = 'id_employ'; // العمود الصحيح في جدول employ_tb
    if (in_array('id_employ', $columns)) {
        $id_column = 'id_employ';
    } elseif (in_array('id', $columns)) {
        $id_column = 'id';
    } elseif (in_array('emp_id', $columns)) {
        $id_column = 'emp_id';
    }
    
    // جلب الموظفين
    $query = "SELECT $id_column as id, f_name, job FROM employ_tb ORDER BY f_name";
    $result = $con->query($query);
    $employees = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $employees[] = $row;
        }
    }
    
    // جلب إجمالي عدد الموظفين للتشخيص
    $count_query = "SELECT COUNT(*) as total FROM employ_tb";
    $count_result = $con->query($count_query);
    $total_employees_db = $count_result->fetch_assoc()['total'];
    
} catch(Exception $e) {
    $employees = [];
    $total_employees_db = 0;
    $db_error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض حضور الموظفين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <?php include "addon/topbar.php"; ?>
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin: 20px auto;
            padding: 30px;
            max-width: 1400px;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
        }

        .page-header h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
        }

        .search-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .filters-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .stats-section {
            margin-bottom: 25px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid;
            margin-bottom: 15px;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .stats-card.primary { border-left-color: #007bff; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #17a2b8; }
        .stats-card.secondary { border-left-color: #6c757d; }

        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .stats-icon.primary { color: #007bff; }
        .stats-icon.success { color: #28a745; }
        .stats-icon.warning { color: #ffc107; }
        .stats-icon.danger { color: #dc3545; }
        .stats-icon.info { color: #17a2b8; }
        .stats-icon.secondary { color: #6c757d; }

        .stats-number {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }

        .table-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .table {
            margin: 0;
        }

        .table thead th {
            background: #f8f9fa;
            border: none;
            padding: 15px;
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            font-size: 0.85rem;
        }

        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .btn-group .btn {
            border-radius: 6px;
            margin: 0 1px;
            padding: 6px 12px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .btn-group .btn:hover {
            transform: translateY(-1px);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 10px 15px;
        }

        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .table-header {
                flex-direction: column;
                text-align: center;
            }
            
            .table-header .btn-group {
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-users-cog"></i> عرض حضور الموظفين</h1>
            <p class="mb-0">نظام متكامل لمراجعة وإدارة حضور الموظفين</p>
        </div>

        <!-- قسم البحث -->
        <div class="search-section">
            <h5 class="mb-3"><i class="fas fa-search"></i> البحث والفلترة</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">اختيار الموظف:</label>
                    <select name="employee_id" class="form-select">
                        <option value="0">جميع الموظفين</option>
                        <?php foreach ($employees as $employee): ?>
                            <option value="<?= htmlspecialchars($employee['id']) ?>" <?= $selected_employee == $employee['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($employee['f_name']) ?> - <?= htmlspecialchars($employee['job']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">التاريخ:</label>
                    <input type="date" name="attendance_date" class="form-control" value="<?= $selected_date ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> عرض الحضور
                    </button>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <a href="?attendance_date=<?= date('Y-m-d') ?>" class="btn btn-success flex-fill">
                            <i class="fas fa-calendar-day"></i> اليوم
                        </a>
                        <a href="?attendance_date=<?= date('Y-m-d', strtotime('-1 day')) ?>" class="btn btn-warning flex-fill">
                            <i class="fas fa-calendar-minus"></i> أمس
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- فلاتر إضافية -->
        <div class="filters-section">
            <!-- الصف الأول: الفلاتر الرئيسية -->
            <div class="row g-3 align-items-end mb-3">
                <div class="col-lg-3 col-md-6">
                    <label class="form-label">فلتر المستخدم:</label>
                    <select class="form-select" id="user_filter" onchange="applyFilters()">
                        <option value="all">جميع المستخدمين</option>
                        <?php
                        // جلب قائمة المستخدمين
                        $users_query = "SELECT DISTINCT u.id_user, u.user_name
                                       FROM users_tb u
                                       INNER JOIN employ_tb e ON u.id_user = e.userID
                                       WHERE u.role = 'User'
                                       ORDER BY u.user_name";
                        $users_result = $con->query($users_query);
                        while ($user = $users_result->fetch_assoc()): ?>
                            <option value="<?= htmlspecialchars($user['user_name']) ?>">
                                <?= htmlspecialchars($user['user_name']) ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-lg-3 col-md-6">
                    <label class="form-label">فلتر الحالة:</label>
                    <select class="form-select" id="status_filter" onchange="applyFilters()">
                        <option value="all">جميع الحالات</option>
                        <option value="حاضر">حاضر</option>
                        <option value="متأخر">متأخر</option>
                        <option value="غائب">غائب</option>
                        <option value="إجازة">إجازة</option>
                        <option value="لم يتم تسجيله">لم يتم تسجيله</option>
                    </select>
                </div>

                <div class="col-lg-3 col-md-6">
                    <label class="form-label">البحث في الأسماء:</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن موظف..." onkeyup="searchEmployees()">
                </div>

                <div class="col-lg-3 col-md-6">
                    <label class="form-label">الترتيب:</label>
                    <button type="button" class="btn btn-outline-primary w-100" id="sortButton" onclick="toggleSort()">
                        <i class="fas fa-sort-amount-down" id="sortIcon"></i> <span id="sortText">تصاعدي</span>
                    </button>
                </div>
            </div>

            <!-- الصف الثاني: أزرار الإجراءات -->
            <div class="row g-2">
                <div class="col-md-6">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-eraser"></i> مسح جميع الفلاتر
                    </button>
                </div>

                <div class="col-md-6">
                    <button type="button" class="btn btn-info w-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> تحديث البيانات
                    </button>
                </div>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="stats-section">
            <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card primary">
                        <div class="stats-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number" id="totalEmployees">0</div>
                        <div class="stats-label">إجمالي المعروض</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-number" id="presentCount">0</div>
                        <div class="stats-label">حاضر</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card warning">
                        <div class="stats-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stats-number" id="lateCount">0</div>
                        <div class="stats-label">متأخر</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card danger">
                        <div class="stats-icon danger">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stats-number" id="absentCount">0</div>
                        <div class="stats-label">غائب</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stats-number" id="leaveCount">0</div>
                        <div class="stats-label">إجازة</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="stats-card secondary">
                        <div class="stats-icon secondary">
                            <i class="fas fa-minus-circle"></i>
                        </div>
                        <div class="stats-number" id="notRegisteredCount">0</div>
                        <div class="stats-label">لم يسجل</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجدول -->
        <div class="table-section">
            <div class="table-header">
                <h4><i class="fas fa-table"></i> سجلات حضور الموظفين - <?= date('d/m/Y', strtotime($selected_date)) ?></h4>
                <div class="btn-group">
                    <button onclick="printAttendance()" class="btn btn-light btn-sm">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button onclick="exportToExcel()" class="btn btn-light btn-sm">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0" id="attendanceTable">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 60px;">#</th>
                            <th>اسم الموظف</th>
                            <th>المنصب الوظيفي</th>
                            <th class="text-center">التاريخ</th>
                            <th class="text-center">وقت الحضور</th>
                            <th class="text-center">حالة الحضور</th>
                            <th class="text-center" style="width: 150px;">المستخدم</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <tr>
                            <td colspan="7" class="text-center p-4">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                    <p>جاري تحميل البيانات...</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- النافذة المنبثقة -->
    <div class="modal fade" id="customModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">عنوان النافذة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    محتوى النافذة
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentSort = 'asc';
        let allEmployeesData = [];

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAttendanceData();
            setTimeout(() => {
                updateStats();
            }, 1000);
        });

        // تحميل بيانات الحضور
        function loadAttendanceData() {
            displayEmployeesDirectly();
        }

        // عرض البيانات مباشرة
        function displayEmployeesDirectly() {
            const tbody = document.getElementById('attendanceTableBody');

            fetch(`employee_attendance_data.php?date=<?= $selected_date ?>&employee=<?= $selected_employee ?>`)
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    let counter = 1;
                    let totalEmployees = 0;

                    if (data.success && data.employees && data.employees.length > 0) {
                        allEmployeesData = data.employees;

                        data.employees.forEach(employee => {
                            let status, statusClass, statusIcon, checkInTime;

                            if (employee.attendance) {
                                status = employee.attendance.status;
                                checkInTime = employee.attendance.check_in_time || 'لم يسجل';

                                switch (employee.attendance.status) {
                                    case 'حاضر':
                                        statusClass = 'bg-success text-white';
                                        statusIcon = 'fa-check-circle';
                                        break;
                                    case 'متأخر':
                                        statusClass = 'bg-warning text-dark';
                                        statusIcon = 'fa-exclamation-triangle';
                                        break;
                                    case 'غائب':
                                        statusClass = 'bg-danger text-white';
                                        statusIcon = 'fa-times-circle';
                                        break;
                                    case 'إجازة':
                                        statusClass = 'bg-info text-white';
                                        statusIcon = 'fa-calendar-check';
                                        break;
                                    default:
                                        statusClass = 'bg-secondary text-white';
                                        statusIcon = 'fa-question-circle';
                                }
                            } else {
                                status = 'لم يتم تسجيله';
                                statusClass = 'bg-light text-dark';
                                statusIcon = 'fa-minus-circle';
                                checkInTime = 'لم يسجل';
                            }

                            html += `
                                <tr data-status="${employee.attendance ? employee.attendance.status : 'لم يتم تسجيله'}" data-name="${employee.f_name.toLowerCase()}" data-user="${employee.user_name}">
                                    <td class="text-center"><span class="badge bg-primary">${counter}</span></td>
                                    <td><strong>${employee.f_name}</strong></td>
                                    <td class="text-muted">${employee.job}</td>
                                    <td class="text-center"><?= date('d/m/Y', strtotime($selected_date)) ?></td>
                                    <td class="text-center"><span class="badge bg-info">${checkInTime}</span></td>
                                    <td class="text-center">
                                        <span class="status-badge ${statusClass}">
                                            <i class="fas ${statusIcon}"></i> ${status}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-user"></i> ${employee.user_name}
                                        </span>
                                    </td>
                                </tr>
                            `;
                            counter++;
                            totalEmployees++;
                        });
                    } else if (data.success && data.total_count > 0) {
                        const selectedEmployeeOption = document.querySelector('select[name="employee_id"] option:checked');
                        const selectedEmployeeName = selectedEmployeeOption ? selectedEmployeeOption.textContent : 'غير محدد';
                        const selectedEmployeeId = selectedEmployeeOption ? selectedEmployeeOption.value : '';

                        html = `<tr><td colspan="7" class="text-center p-5">
                            <div class="alert alert-info">
                                <i class="fas fa-user-clock fa-3x mb-3"></i>
                                <h5>لا توجد سجلات حضور للموظف المحدد</h5>
                                <p><strong>الموظف:</strong> ${selectedEmployeeName}</p>
                                <p><strong>التاريخ:</strong> <?= date('d/m/Y', strtotime($selected_date)) ?></p>
                                <div class="mt-3">
                                    <button class="btn btn-primary me-2" onclick="editAttendance('${selectedEmployeeId}', '${selectedEmployeeName}', 'لم يتم تسجيله')">
                                        <i class="fas fa-plus"></i> تسجيل حضور جديد
                                    </button>
                                    <button class="btn btn-info" onclick="viewEmployeeDetails('${selectedEmployeeId}', '${selectedEmployeeName}', 'غير محدد')">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </button>
                                </div>
                            </div>
                        </td></tr>`;
                    }

                    if (html === '') {
                        html = `<tr><td colspan="7" class="text-center p-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <h5>لا توجد سجلات حضور للتاريخ المحدد</h5>
                                <p>عدد الموظفين في قاعدة البيانات: ${data.total_count || 0}</p>
                            </div>
                        </td></tr>`;
                    }

                    tbody.innerHTML = html;
                    document.getElementById('totalEmployees').textContent = totalEmployees;

                    setTimeout(() => {
                        updateStats();
                    }, 100);
                })
                .catch(error => {
                    console.error('خطأ في جلب البيانات:', error);
                    tbody.innerHTML = `<tr><td colspan="7" class="text-center p-4">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                            <h5>خطأ في تحميل البيانات</h5>
                            <p>يرجى المحاولة مرة أخرى</p>
                        </div>
                    </td></tr>`;
                });
        }

        // تحديث الإحصائيات
        function updateStats() {
            const rows = document.querySelectorAll('#attendanceTableBody tr:not([style*="display: none"])');
            let presentCount = 0, absentCount = 0, lateCount = 0, leaveCount = 0, notRegisteredCount = 0;

            rows.forEach(row => {
                const statusCell = row.querySelector('td:nth-child(6)');
                if (statusCell) {
                    const statusText = statusCell.textContent.toLowerCase();
                    if (statusText.includes('حاضر')) presentCount++;
                    else if (statusText.includes('غائب')) absentCount++;
                    else if (statusText.includes('متأخر')) lateCount++;
                    else if (statusText.includes('إجازة')) leaveCount++;
                    else if (statusText.includes('لم يتم تسجيله')) notRegisteredCount++;
                }
            });

            document.getElementById('totalEmployees').textContent = rows.length;
            document.getElementById('presentCount').textContent = presentCount;
            document.getElementById('absentCount').textContent = absentCount;
            document.getElementById('lateCount').textContent = lateCount;
            document.getElementById('leaveCount').textContent = leaveCount;
            document.getElementById('notRegisteredCount').textContent = notRegisteredCount;
        }

        // فلترة البيانات حسب الحالة والمستخدم
        function applyFilters() {
            const statusFilter = document.getElementById('status_filter').value;
            const userFilter = document.getElementById('user_filter').value;
            const rows = document.querySelectorAll('#attendanceTableBody tr');

            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                const userName = row.getAttribute('data-user');

                const statusMatch = statusFilter === 'all' || status === statusFilter;
                const userMatch = userFilter === 'all' || userName === userFilter;

                if (statusMatch && userMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            updateStats();
            updateRowNumbers();
        }

        // البحث في أسماء الموظفين
        function searchEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#attendanceTableBody tr');

            rows.forEach(row => {
                const name = row.getAttribute('data-name');

                if (name && name.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            updateStats();
            updateRowNumbers();
        }

        // ترتيب النتائج
        function toggleSort() {
            const tbody = document.getElementById('attendanceTableBody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const sortIcon = document.getElementById('sortIcon');
            const sortText = document.getElementById('sortText');

            if (currentSort === 'asc') {
                rows.sort((a, b) => {
                    const nameA = a.getAttribute('data-name');
                    const nameB = b.getAttribute('data-name');
                    return nameB.localeCompare(nameA, 'ar');
                });
                currentSort = 'desc';
                sortIcon.className = 'fas fa-sort-amount-up';
                sortText.textContent = 'تنازلي';
            } else {
                rows.sort((a, b) => {
                    const nameA = a.getAttribute('data-name');
                    const nameB = b.getAttribute('data-name');
                    return nameA.localeCompare(nameB, 'ar');
                });
                currentSort = 'asc';
                sortIcon.className = 'fas fa-sort-amount-down';
                sortText.textContent = 'تصاعدي';
            }

            rows.forEach(row => tbody.appendChild(row));
            updateRowNumbers();
        }

        // مسح جميع الفلاتر
        function clearFilters() {
            document.getElementById('status_filter').value = 'all';
            document.getElementById('user_filter').value = 'all';
            document.getElementById('searchInput').value = '';

            const rows = document.querySelectorAll('#attendanceTableBody tr');
            rows.forEach(row => {
                row.style.display = '';
            });

            updateStats();
            updateRowNumbers();
        }

        // تحديث أرقام الصفوف
        function updateRowNumbers() {
            const visibleRows = document.querySelectorAll('#attendanceTableBody tr:not([style*="display: none"])');
            visibleRows.forEach((row, index) => {
                const firstCell = row.querySelector('td:first-child');
                if (firstCell) {
                    firstCell.innerHTML = `<span class="badge bg-primary">${index + 1}</span>`;
                }
            });
        }

        // تحديث البيانات
        function refreshData() {
            showNotification('جاري تحديث البيانات...', 'info');
            loadAttendanceData();
            setTimeout(() => {
                showNotification('تم تحديث البيانات بنجاح', 'success');
                updateStats();
            }, 1000);
        }

        // عرض تفاصيل الموظف
        function viewEmployeeDetails(employeeId, employeeName, employeeJob) {
            const modalContent = `
                <div class="text-center mb-4">
                    <i class="fas fa-user-circle fa-5x text-primary mb-3"></i>
                    <h4 class="text-primary">${employeeName}</h4>
                    <p class="text-muted">${employeeJob}</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title text-primary"><i class="fas fa-id-card"></i> معلومات أساسية</h6>
                                <p><strong>رقم الموظف:</strong> ${employeeId}</p>
                                <p><strong>المنصب:</strong> ${employeeJob}</p>
                                <p><strong>القسم:</strong> إدارة الروضة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body">
                                <h6 class="card-title text-info"><i class="fas fa-calendar-alt"></i> معلومات الجلسة</h6>
                                <p><strong>التاريخ المحدد:</strong> <?= date('d/m/Y', strtotime($selected_date)) ?></p>
                                <p><strong>الوقت الحالي:</strong> ${new Date().toLocaleTimeString('ar-EG')}</p>
                                <p><strong>اليوم:</strong> ${new Date().toLocaleDateString('ar-EG', {weekday: 'long'})}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 justify-content-center mt-4">
                    <button class="btn btn-warning" onclick="closeModal(); editAttendance('${employeeId}', '${employeeName}', 'لم يتم تسجيله')">
                        <i class="fas fa-edit"></i> تسجيل/تعديل الحضور
                    </button>
                    <button class="btn btn-info" onclick="closeModal(); viewHistory('${employeeId}', '${employeeName}')">
                        <i class="fas fa-history"></i> سجل الحضور
                    </button>
                </div>
            `;

            showModal('تفاصيل الموظف', modalContent);
        }

        // تعديل الحضور
        function editAttendance(employeeId, employeeName, currentStatus) {
            const modalContent = `
                <div class="alert alert-primary">
                    <strong><i class="fas fa-user"></i> الموظف:</strong> ${employeeName}<br>
                    <strong><i class="fas fa-calendar"></i> التاريخ:</strong> <?= date('d/m/Y', strtotime($selected_date)) ?><br>
                    <strong><i class="fas fa-info-circle"></i> الحالة الحالية:</strong> ${currentStatus}
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-clipboard-check"></i> حالة الحضور الجديدة:</label>
                            <select class="form-select" id="attendanceStatus" onchange="toggleLeaveFields()">
                                <option value="حاضر">حاضر</option>
                                <option value="متأخر">متأخر</option>
                                <option value="غائب">غائب</option>
                                <option value="إجازة">إجازة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-clock"></i> وقت الحضور:</label>
                            <input type="time" class="form-control" id="checkInTime" value="08:00">
                        </div>
                    </div>
                </div>

                <div class="row" id="leaveFields" style="display: none;">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-calendar-alt"></i> تاريخ بداية الإجازة:</label>
                            <input type="date" class="form-control" id="leaveStartDate">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-calendar-alt"></i> تاريخ نهاية الإجازة:</label>
                            <input type="date" class="form-control" id="leaveEndDate">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label"><i class="fas fa-sticky-note"></i> ملاحظات:</label>
                    <textarea class="form-control" id="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية حول حضور الموظف"></textarea>
                </div>

                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="saveAttendance('${employeeId}', '${employeeName}')">
                        <i class="fas fa-save"></i> حفظ التعديلات
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            `;

            showModal('تعديل حضور الموظف', modalContent);
        }

        // إظهار/إخفاء حقول الإجازة
        function toggleLeaveFields() {
            const status = document.getElementById('attendanceStatus').value;
            const leaveFields = document.getElementById('leaveFields');

            if (status === 'إجازة') {
                leaveFields.style.display = 'block';
            } else {
                leaveFields.style.display = 'none';
            }
        }

        // حفظ الحضور
        function saveAttendance(employeeId, employeeName) {
            const status = document.getElementById('attendanceStatus').value;
            const checkInTime = document.getElementById('checkInTime').value;
            const notes = document.getElementById('notes').value;
            const attendanceDate = '<?= $selected_date ?>';
            const leaveStartDate = document.getElementById('leaveStartDate') ? document.getElementById('leaveStartDate').value : '';
            const leaveEndDate = document.getElementById('leaveEndDate') ? document.getElementById('leaveEndDate').value : '';

            const formData = new FormData();
            formData.append('employee_id', employeeId);
            formData.append('employee_name', employeeName);
            formData.append('attendance_date', attendanceDate);
            formData.append('status', status);
            formData.append('check_in_time', checkInTime);
            formData.append('notes', notes);
            formData.append('leave_start_date', leaveStartDate);
            formData.append('leave_end_date', leaveEndDate);

            fetch('save_attendance_stat2.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`تم حفظ بيانات الحضور بنجاح للموظف: ${employeeName}`, 'success');
                    closeModal();
                    setTimeout(() => {
                        loadAttendanceData();
                    }, 500);
                } else {
                    showNotification('خطأ في حفظ البيانات: ' + (data.error || 'خطأ غير معروف'), 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ في الإرسال:', error);
                showNotification('خطأ في الاتصال بالخادم', 'danger');
            });
        }

        // عرض سجل الحضور
        function viewHistory(employeeId, employeeName) {
            const modalContent = `
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>جاري تحميل سجل الحضور...</p>
                </div>
            `;

            showModal('سجل حضور الموظف', modalContent);

            // محاكاة بيانات السجل
            setTimeout(() => {
                const historyContent = `
                    <div class="alert alert-primary">
                        <strong><i class="fas fa-user"></i> الموظف:</strong> ${employeeName}<br>
                        <strong><i class="fas fa-calendar-week"></i> الفترة:</strong> آخر 14 يوم
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-primary">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>وقت الدخول</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>اليوم</td>
                                    <td><span class="badge bg-success">حاضر</span></td>
                                    <td>08:30</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>أمس</td>
                                    <td><span class="badge bg-warning">متأخر</span></td>
                                    <td>09:15</td>
                                    <td>تأخر بسبب الزحام</td>
                                </tr>
                                <tr>
                                    <td>قبل يومين</td>
                                    <td><span class="badge bg-success">حاضر</span></td>
                                    <td>08:00</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;

                const modalBody = document.querySelector('#customModal .modal-body');
                if (modalBody) {
                    modalBody.innerHTML = historyContent;
                }
            }, 1000);
        }

        // عرض النافذة المنبثقة
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            const modal = new bootstrap.Modal(document.getElementById('customModal'));
            modal.show();
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('customModal'));
            if (modal) {
                modal.hide();
            }
        }

        // عرض الإشعارات
        function showNotification(message, type) {
            const notification = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', notification);
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) alert.remove();
            }, 5000);
        }

        // طباعة التقرير
        function printAttendance() {
            const table = document.getElementById('attendanceTable').cloneNode(true);

            // الجدول جاهز للطباعة مع عمود المستخدم

            const totalEmployees = document.getElementById('totalEmployees').textContent;
            const selectedDate = '<?= date("d/m/Y", strtotime($selected_date)) ?>';
            const currentTime = new Date().toLocaleString('ar-EG');

            const printWindow = window.open('', '_blank');
            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير حضور الموظفين - ${selectedDate}</title>
                    <style>
                        body {
                            font-family: 'Arial', sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            background: white;
                            color: #333;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 3px solid #667eea;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #667eea;
                            margin-bottom: 10px;
                            font-size: 28px;
                        }
                        .header p {
                            margin: 5px 0;
                            font-size: 16px;
                            color: #666;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 12px 8px;
                            text-align: center;
                            font-size: 14px;
                        }
                        th {
                            background: #667eea;
                            color: white;
                            font-weight: bold;
                        }
                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                        .footer {
                            margin-top: 40px;
                            text-align: center;
                            font-size: 12px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير حضور الموظفين</h1>
                        <p><strong>التاريخ:</strong> ${selectedDate}</p>
                        <p><strong>وقت الطباعة:</strong> ${currentTime}</p>
                        <p><strong>إجمالي الموظفين:</strong> ${totalEmployees}</p>
                    </div>

                    ${table.outerHTML}

                    <div class="footer">
                        <p>تم الطباعة في: ${currentTime} | نظام إدارة الروضة</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();

            setTimeout(() => {
                printWindow.print();
                setTimeout(() => {
                    printWindow.close();
                }, 1000);
            }, 500);

            showNotification('تم إعداد التقرير للطباعة', 'success');
        }

        // تصدير إلى Excel
        function exportToExcel() {
            const table = document.getElementById('attendanceTable');
            const rows = table.querySelectorAll('tr');

            if (rows.length <= 1) {
                showNotification('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            let csv = '\uFEFF'; // BOM for UTF-8
            csv += 'الرقم,اسم الموظف,المنصب الوظيفي,التاريخ,وقت الحضور,حالة الحضور\n';

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].querySelectorAll('td');
                if (cells.length > 1 && !cells[0].textContent.includes('لا توجد')) {
                    let rowData = [];
                    for (let j = 0; j < cells.length - 1; j++) {
                        let cellText = cells[j].innerText.replace(/,/g, ';').replace(/\n/g, ' ').trim();
                        rowData.push(`"${cellText}"`);
                    }
                    csv += rowData.join(',') + '\n';
                }
            }

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `تقرير_حضور_الموظفين_<?= $selected_date ?>.csv`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification('تم تصدير البيانات بنجاح إلى ملف Excel', 'success');
        }
    </script>
</body>
</html>
