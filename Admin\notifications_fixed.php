<link rel="stylesheet" href="css/notifications_fixed.css">
<!-- مكون الإشعارات المحسن -->
<div class="notifications-fixed-container">
    <!-- زر الإشعارات -->
    <div class="notification-bell-fixed" onclick="toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge-fixed" id="notificationBadge" style="display: none;">0</span>
    </div>
    
    <!-- قائمة الإشعارات -->
    <div class="notifications-dropdown-fixed" id="notificationsDropdown">
        <div class="notifications-header-fixed">
            <h3>الإشعارات</h3>
            <button class="mark-all-read-btn-fixed" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i>
                تحديد الكل كمقروء
            </button>
        </div>
        
        <div class="notifications-list-fixed" id="notificationsList">
            <div class="loading-notifications-fixed">
                <i class="fas fa-spinner fa-spin"></i>
                جاري التحميل...
            </div>
        </div>
        
        <div class="notifications-footer-fixed">
            <button class="load-more-btn-fixed" onclick="loadMoreNotifications()" id="loadMoreBtn" style="display: none;">
                تحميل المزيد
            </button>
        </div>
    </div>
</div>

<style>
.notifications-fixed-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 999999;
}

.notification-bell-fixed {
    position: relative;
    cursor: pointer;
    padding: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    transition: all 0.3s ease;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    border: 3px solid white;
}

.notification-bell-fixed:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.notification-badge-fixed {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 4px 8px;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
    animation: pulse 2s infinite;
    border: 2px solid white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.notifications-dropdown-fixed {
    position: absolute;
    top: 70px;
    left: 0;
    width: 380px;
    max-height: 550px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    z-index: 999999;
    display: none;
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

.notifications-dropdown-fixed.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.notifications-header-fixed {
    padding: 15px 20px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header-fixed h3 {
    margin: 0;
    font-size: 1.1rem;
}

.mark-all-read-btn-fixed {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mark-all-read-btn-fixed:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.notifications-list-fixed {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item-fixed {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item-fixed:hover {
    background: #f8f9fa;
}

.notification-item-fixed.unread {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-item-fixed.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 8px;
    height: 8px;
    background: #2196f3;
    border-radius: 50%;
    transform: translateY(-50%);
}

.notification-title-fixed {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-message-fixed {
    color: #666;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 5px;
}

.notification-time-fixed {
    color: #999;
    font-size: 0.7rem;
}

.notification-type-icon-fixed {
    display: inline-block;
    margin-left: 5px;
    width: 20px;
    text-align: center;
}

.type-leave_request { color: #ff9800; }
.type-need_request { color: #4caf50; }
.type-leave_response { color: #2196f3; }
.type-need_response { color: #9c27b0; }
.type-general { color: #607d8b; }

.loading-notifications-fixed {
    text-align: center;
    padding: 30px;
    color: #666;
}

.empty-notifications-fixed {
    text-align: center;
    padding: 30px;
    color: #999;
}

.empty-notifications-fixed i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.notifications-footer-fixed {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.load-more-btn-fixed {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn-fixed:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .notifications-fixed-container {
        top: 10px;
        left: 10px;
    }
    
    .notifications-dropdown-fixed {
        width: 320px;
    }
    
    .notification-item-fixed {
        padding: 12px 15px;
    }
    
    .notifications-header-fixed {
        padding: 12px 15px;
    }
}

/* التأكد من عدم التداخل مع العناصر الأخرى */
.notifications-dropdown-fixed {
    box-shadow: 0 15px 35px rgba(0,0,0,0.25) !important;
    border: 2px solid #007bff !important;
}
</style>

<script>
let notificationsOpen = false;
let notificationsOffset = 0;
const notificationsLimit = 10;

// تحميل عدد الإشعارات غير المقروءة
function loadNotificationCount() {
    fetch('notifications_api.php?action=get_count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => console.error('Error:', error));
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadge');
    if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'block';
    } else {
        badge.style.display = 'none';
    }
}

// تبديل عرض الإشعارات
function toggleNotifications() {
    const dropdown = document.getElementById('notificationsDropdown');
    
    if (!notificationsOpen) {
        dropdown.classList.add('show');
        notificationsOpen = true;
        loadNotifications(true);
    } else {
        dropdown.classList.remove('show');
        notificationsOpen = false;
    }
}

// تحميل الإشعارات
function loadNotifications(reset = false) {
    if (reset) {
        notificationsOffset = 0;
        document.getElementById('notificationsList').innerHTML = '<div class="loading-notifications-fixed"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    }
    
    fetch(`notifications_api.php?action=get_notifications&limit=${notificationsLimit}&offset=${notificationsOffset}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications, reset);
                notificationsOffset += notificationsLimit;
                
                // إظهار/إخفاء زر تحميل المزيد
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (data.notifications.length === notificationsLimit) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error:', error));
}

// عرض الإشعارات
function displayNotifications(notifications, reset = false) {
    const container = document.getElementById('notificationsList');
    
    if (reset) {
        container.innerHTML = '';
    }
    
    if (notifications.length === 0 && reset) {
        container.innerHTML = `
            <div class="empty-notifications-fixed">
                <i class="fas fa-bell-slash"></i>
                <div>لا توجد إشعارات</div>
            </div>
        `;
        return;
    }
    
    notifications.forEach(notification => {
        const item = createNotificationItem(notification);
        container.appendChild(item);
    });
}

// إنشاء عنصر إشعار
function createNotificationItem(notification) {
    const item = document.createElement('div');
    item.className = `notification-item-fixed ${notification.is_read == 0 ? 'unread' : ''}`;
    item.onclick = () => markNotificationAsRead(notification.id);
    
    const typeIcons = {
        'leave_request': 'fa-calendar-alt',
        'need_request': 'fa-clipboard-list',
        'leave_response': 'fa-reply',
        'need_response': 'fa-check-circle',
        'general': 'fa-info-circle'
    };
    
    item.innerHTML = `
        <div class="notification-title-fixed">
            <i class="fas ${typeIcons[notification.type]} notification-type-icon-fixed type-${notification.type}"></i>
            ${notification.title}
        </div>
        <div class="notification-message-fixed">${notification.message}</div>
        <div class="notification-time-fixed">${notification.time_ago}</div>
    `;
    
    return item;
}

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId) {
    const formData = new FormData();
    formData.append('notification_id', notificationId);
    
    fetch('notifications_api.php?action=mark_read', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            // تحديث العنصر في الواجهة
            const items = document.querySelectorAll('.notification-item-fixed');
            items.forEach(item => {
                if (item.onclick.toString().includes(notificationId)) {
                    item.classList.remove('unread');
                }
            });
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('notifications_api.php?action=mark_all_read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            loadNotifications(true);
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحميل المزيد من الإشعارات
function loadMoreNotifications() {
    loadNotifications(false);
}

// إغلاق الإشعارات عند النقر خارجها
document.addEventListener('click', function(event) {
    const container = document.querySelector('.notifications-fixed-container');
    if (!container.contains(event.target) && notificationsOpen) {
        document.getElementById('notificationsDropdown').classList.remove('show');
        notificationsOpen = false;
    }
});

// تحميل عدد الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadNotificationCount();
    
    // تحديث العدد كل 30 ثانية
    setInterval(loadNotificationCount, 30000);
});
</script>
