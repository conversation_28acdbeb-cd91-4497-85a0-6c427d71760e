<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// معالجة الفلاتر
$selected_date = isset($_GET['attendance_date']) ? $_GET['attendance_date'] : date('Y-m-d');
$selected_student = isset($_GET['student_id']) ? $_GET['student_id'] : '0';
$selected_category = isset($_GET['category']) ? $_GET['category'] : 'all';

// جلب الطلاب للقائمة المنسدلة
$students = [];
try {
    $query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name 
              FROM stud_tb s
              LEFT JOIN users_tb u ON s.userID = u.id_user
              ORDER BY s.name";
    $result = $con->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
    }
} catch(Exception $e) {
    $students = [];
}

// جلب بيانات الحضور عند الطلب
$attendance_data = [];
$show_results = false;

if (isset($_GET['show_attendance']) || !empty($_GET['attendance_date'])) {
    $show_results = true;
    
    try {
        // بناء الاستعلام
        $where_conditions = ["DATE(stat.data_stat) = ?"];
        $params = [$selected_date];
        $param_types = "s";
        
        if ($selected_student != '0') {
            $where_conditions[] = "s.id = ?";
            $params[] = $selected_student;
            $param_types .= "i";
        }
        
        if ($selected_category != 'all') {
            $where_conditions[] = "s.catg = ?";
            $params[] = $selected_category;
            $param_types .= "s";
        }
        
        $where_clause = "WHERE " . implode(" AND ", $where_conditions);
        
        $attendance_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name, 
                                   stat.stat_stud, stat.data_stat
                            FROM stud_tb s
                            LEFT JOIN users_tb u ON s.userID = u.id_user
                            INNER JOIN stat ON s.id = stat.id_stud
                            $where_clause
                            ORDER BY s.name";
        
        $stmt = $con->prepare($attendance_query);
        if ($stmt) {
            $stmt->bind_param($param_types, ...$params);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $attendance_data[] = $row;
            }
        }
    } catch(Exception $e) {
        $error_message = "خطأ في جلب البيانات: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حضور الطلاب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php"; ?>
    
    <style>
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin: 20px auto;
            padding: 30px;
            max-width: 1200px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .search-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .table-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            text-align: center;
        }
        
        .table thead th {
            background: #f8f9fa;
            border: none;
            padding: 15px;
            font-weight: 600;
            color: #495057;
        }
        
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-present { background: #28a745; color: white; }
        .status-absent { background: #dc3545; color: white; }
        .status-late { background: #ffc107; color: black; }
        .status-leave { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-graduation-cap"></i> حضور الطلاب</h1>
            <p class="mb-0">عرض وإدارة حضور الطلاب</p>
        </div>

        <!-- قسم البحث -->
        <div class="search-section">
            <h5 class="mb-3"><i class="fas fa-search"></i> البحث والفلترة</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">اختيار الطالب:</label>
                    <select name="student_id" class="form-select">
                        <option value="0">جميع الطلاب</option>
                        <?php foreach ($students as $student): ?>
                            <option value="<?= htmlspecialchars($student['id']) ?>" <?= $selected_student == $student['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($student['name']) ?> - <?= htmlspecialchars($student['catg']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الصف الدراسي:</label>
                    <select name="category" class="form-select">
                        <option value="all">جميع الصفوف</option>
                        <option value="صف التمهيدي" <?= $selected_category == 'صف التمهيدي' ? 'selected' : '' ?>>صف التمهيدي</option>
                        <option value="صف الروضة" <?= $selected_category == 'صف الروضة' ? 'selected' : '' ?>>صف الروضة</option>
                        <option value="صف الحضانة" <?= $selected_category == 'صف الحضانة' ? 'selected' : '' ?>>صف الحضانة</option>
                        <option value="صف التحضيري" <?= $selected_category == 'صف التحضيري' ? 'selected' : '' ?>>صف التحضيري</option>
                        <option value="روضة" <?= $selected_category == 'روضة' ? 'selected' : '' ?>>روضة</option>
                        <option value="حضانة" <?= $selected_category == 'حضانة' ? 'selected' : '' ?>>حضانة</option>
                        <option value="تمهيدي" <?= $selected_category == 'تمهيدي' ? 'selected' : '' ?>>تمهيدي</option>
                        <option value="تحضيري" <?= $selected_category == 'تحضيري' ? 'selected' : '' ?>>تحضيري</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">التاريخ:</label>
                    <input type="date" name="attendance_date" class="form-control" value="<?= $selected_date ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" name="show_attendance" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> عرض الحضور
                    </button>
                </div>
            </form>
        </div>

        <!-- النتائج -->
        <?php if ($show_results): ?>
        <div class="table-section">
            <div class="table-header">
                <h4><i class="fas fa-table"></i> سجلات حضور الطلاب - <?= date('d/m/Y', strtotime($selected_date)) ?></h4>
            </div>

            <div class="table-responsive">
                <?php if (!empty($attendance_data)): ?>
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="text-center">#</th>
                            <th>اسم الطالب</th>
                            <th>الصف الدراسي</th>
                            <th>اسم ولي الأمر</th>
                            <th class="text-center">التاريخ</th>
                            <th class="text-center">حالة الحضور</th>
                            <th class="text-center">المستخدم</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $counter = 1;
                        foreach ($attendance_data as $record): 
                            $status_class = '';
                            switch($record['stat_stud']) {
                                case 'حاضر': $status_class = 'status-present'; break;
                                case 'غائب': $status_class = 'status-absent'; break;
                                case 'متأخر': $status_class = 'status-late'; break;
                                case 'إجازة': $status_class = 'status-leave'; break;
                                default: $status_class = 'bg-secondary text-white';
                            }
                        ?>
                        <tr>
                            <td class="text-center"><span class="badge bg-primary"><?= $counter ?></span></td>
                            <td><strong><?= htmlspecialchars($record['name']) ?></strong></td>
                            <td><?= htmlspecialchars($record['catg']) ?></td>
                            <td><?= htmlspecialchars($record['p_name']) ?></td>
                            <td class="text-center"><?= date('d/m/Y', strtotime($record['data_stat'])) ?></td>
                            <td class="text-center">
                                <span class="status-badge <?= $status_class ?>">
                                    <?= htmlspecialchars($record['stat_stud']) ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">
                                    <?= htmlspecialchars($record['user_name'] ?? 'غير محدد') ?>
                                </span>
                            </td>
                        </tr>
                        <?php 
                        $counter++;
                        endforeach; 
                        ?>
                    </tbody>
                </table>
                
                <div class="p-3 bg-light">
                    <strong>إجمالي السجلات: <?= count($attendance_data) ?></strong>
                </div>
                
                <?php else: ?>
                <div class="p-5 text-center">
                    <i class="fas fa-info-circle fa-3x mb-3 text-info"></i>
                    <h5>لا توجد سجلات حضور للتاريخ المحدد</h5>
                    <p class="text-muted">يرجى اختيار تاريخ آخر أو تعديل الفلاتر</p>
                    <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger mt-3">
                        <?= htmlspecialchars($error_message) ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
</body>
</html>
