<?php
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>تشخيص مشكلة المصاريف</h2>";

include "addon/dbcon.php";

// اختبار الاتصال
if ($con->connect_error) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $con->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
}

// اختبار وجود المستخدمين
$sql = "SELECT id_user, user_name FROM users_tb LIMIT 5";
$result = mysqli_query($con, $sql);

echo "<h3>المستخدمين المتاحين:</h3>";
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>اختبار</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$row['id_user']}</td>";
        echo "<td>{$row['user_name']}</td>";
        echo "<td><button onclick='testUser({$row['id_user']})'>اختبار</button></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد مستخدمين</p>";
}

// اختبار المصاريف للمستخدم رقم 1
echo "<h3>اختبار المصاريف للمستخدم رقم 1:</h3>";
$sql = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE userID = 1";
$result = mysqli_query($con, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "<p>عدد المصاريف: {$row['count']}</p>";
    echo "<p>المجموع: IQD " . number_format($row['total']) . "</p>";
}

// اختبار AJAX
echo "<h3>اختبار AJAX:</h3>";
echo "<div id='ajax-result'>اضغط على زر الاختبار أدناه</div>";
echo "<button onclick='testAjax()'>اختبار AJAX</button>";

?>

<script src="js/jquery.min.js"></script>
<script>
function testUser(userId) {
    console.log('اختبار المستخدم:', userId);
    
    $.ajax({
        method: "POST",
        url: "addon/info_depitF.php",
        data: {id: userId},
        success: function (data) {
            console.log('نجح AJAX:', data);
            alert('نجح! البيانات: ' + data.substring(0, 100) + '...');
        },
        error: function(xhr, status, error) {
            console.error('فشل AJAX:', error);
            alert('فشل: ' + error);
        }
    });
}

function testAjax() {
    $('#ajax-result').html('جاري الاختبار...');
    
    $.ajax({
        method: "POST",
        url: "addon/info_depitF.php",
        data: {id: 1},
        success: function (data) {
            console.log('نتيجة AJAX:', data);
            $('#ajax-result').html('<h4>نجح AJAX:</h4><div style="border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow: auto;">' + data + '</div>');
        },
        error: function(xhr, status, error) {
            console.error('خطأ AJAX:', error);
            $('#ajax-result').html('<p style="color: red;">خطأ: ' + error + '</p>');
        }
    });
}

// اختبار تلقائي
$(document).ready(function() {
    console.log('الصفحة جاهزة - اختبار تلقائي');
    setTimeout(function() {
        testAjax();
    }, 1000);
});
</script>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
    table { border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
    th { background-color: #f2f2f2; }
    button { padding: 5px 10px; margin: 2px; }
</style>
