<?php
// الإصلاح النهائي لمشاكل حضور الطلاب
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h1>الإصلاح النهائي لحضور الطلاب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .fix-box { border: 2px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 10px; background: #f8f9fa; }
    .solution-box { border: 2px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 10px; background: #d4edda; }
    button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    button:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .btn-success:hover { background: #1e7e34; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background: #f2f2f2; }
</style>";

// 1. فحص المشاكل الحالية
echo "<div class='fix-box'>";
echo "<h2>🔍 فحص المشاكل الحالية</h2>";

$issues = [];
$solutions = [];

// فحص ملف API
if (!file_exists('student_attendance_data.php')) {
    $issues[] = "ملف API غير موجود";
    $solutions[] = "إنشاء ملف API جديد";
} else {
    echo "<p class='success'>✅ ملف API موجود</p>";
}

// فحص قاعدة البيانات
include "addon/dbcon.php";
if (!$con) {
    $issues[] = "مشكلة في الاتصال بقاعدة البيانات";
} else {
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    $tables = ['stud_tb', 'stat', 'users_tb'];
    foreach ($tables as $table) {
        $result = $con->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            $issues[] = "جدول $table غير موجود";
        } else {
            $count = $con->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p class='success'>✅ جدول $table: $count سجل</p>";
        }
    }
}

if (empty($issues)) {
    echo "<p class='success'><strong>✅ لا توجد مشاكل أساسية!</strong></p>";
} else {
    echo "<p class='error'><strong>❌ المشاكل المكتشفة:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>$issue</li>";
    }
    echo "</ul>";
}
echo "</div>";

// 2. إنشاء ملف API محسن
echo "<div class='solution-box'>";
echo "<h2>🛠️ إنشاء ملف API محسن</h2>";

$api_content = '<?php
session_start();
header("Content-Type: application/json; charset=utf-8");

// التحقق من صلاحية المدير
if (!isset($_SESSION["user"]) || $_SESSION["user"]->role !== "Admin") {
    echo json_encode(["error" => "غير مصرح", "success" => false], JSON_UNESCAPED_UNICODE);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

if (!$con) {
    echo json_encode(["error" => "فشل في الاتصال بقاعدة البيانات", "success" => false], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $selected_date = $_GET["date"] ?? date("Y-m-d");
    $selected_student = $_GET["student"] ?? "0";
    $selected_category = $_GET["category"] ?? "all";
    
    // جلب عدد الطلاب
    $total_result = $con->query("SELECT COUNT(*) as total FROM stud_tb");
    $total_count = $total_result ? $total_result->fetch_assoc()["total"] : 0;
    
    // بناء الاستعلام
    $where_conditions = [];
    $params = [];
    $param_types = "";
    
    if ($selected_student != "0" && is_numeric($selected_student)) {
        $where_conditions[] = "s.id = ?";
        $params[] = intval($selected_student);
        $param_types .= "i";
    }
    
    if ($selected_category != "all" && !empty($selected_category)) {
        $where_conditions[] = "(s.catg = ? OR s.catg LIKE ? OR s.catg LIKE ?)";
        $params[] = $selected_category;
        $params[] = "%" . str_replace("صف ", "", $selected_category) . "%";
        $params[] = "%" . $selected_category . "%";
        $param_types .= "sss";
    }
    
    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    // جلب الطلاب
    $students_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name 
                      FROM stud_tb s
                      LEFT JOIN users_tb u ON s.userID = u.id_user
                      $where_clause
                      ORDER BY s.name
                      LIMIT 100";
    
    $students_result = null;
    if (!empty($params)) {
        $stmt = $con->prepare($students_query);
        if ($stmt) {
            $stmt->bind_param($param_types, ...$params);
            $stmt->execute();
            $students_result = $stmt->get_result();
        }
    } else {
        $students_result = $con->query($students_query);
    }
    
    $students = [];
    if ($students_result && $students_result->num_rows > 0) {
        while ($student = $students_result->fetch_assoc()) {
            // جلب الحضور
            $att_query = "SELECT stat_stud, data_stat FROM stat WHERE id_stud = ? AND DATE(data_stat) = ? ORDER BY data_stat DESC LIMIT 1";
            $att_stmt = $con->prepare($att_query);
            $attendance = null;
            
            if ($att_stmt) {
                $att_stmt->bind_param("is", $student["id"], $selected_date);
                $att_stmt->execute();
                $att_result = $att_stmt->get_result();
                $attendance = $att_result->fetch_assoc();
            }
            
            $check_in_time = "لم يسجل";
            if ($attendance) {
                $datetime = $attendance["data_stat"];
                if (strpos($datetime, " ") !== false) {
                    $check_in_time = date("H:i", strtotime($datetime));
                } else {
                    $check_in_time = "08:00";
                }
            }
            
            $students[] = [
                "id" => $student["id"],
                "name" => $student["name"] ?? "غير محدد",
                "catg" => $student["catg"] ?? "غير محدد",
                "p_name" => $student["p_name"] ?? "غير محدد",
                "user_name" => $student["user_name"] ?? "غير محدد",
                "attendance" => $attendance ? [
                    "status" => $attendance["stat_stud"],
                    "check_in_time" => $check_in_time,
                    "data_stat" => $attendance["data_stat"]
                ] : null
            ];
        }
    }
    
    echo json_encode([
        "students" => $students,
        "total_count" => $total_count,
        "selected_date" => $selected_date,
        "selected_student" => $selected_student,
        "selected_category" => $selected_category,
        "success" => true
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        "error" => "خطأ: " . $e->getMessage(),
        "success" => false
    ], JSON_UNESCAPED_UNICODE);
}

if ($con) {
    $con->close();
}
?>';

if (file_put_contents('student_attendance_data_fixed.php', $api_content)) {
    echo "<p class='success'>✅ تم إنشاء ملف API محسن: student_attendance_data_fixed.php</p>";
} else {
    echo "<p class='error'>❌ فشل في إنشاء ملف API</p>";
}
echo "</div>";

// 3. اختبار API الجديد
echo "<div class='fix-box'>";
echo "<h2>🧪 اختبار API الجديد</h2>";

echo "<button onclick='testNewAPI()'>اختبار API الجديد</button>";
echo "<button onclick='testWithCategories()'>اختبار مع الصفوف</button>";
echo "<div id='test-result'></div>";

echo "<script>
function testNewAPI() {
    const resultDiv = document.getElementById('test-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    fetch('student_attendance_data_fixed.php?date=" . date('Y-m-d') . "')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    resultDiv.innerHTML = '<p style=\"color: green;\">✅ API الجديد يعمل بنجاح!</p>' +
                        '<p><strong>عدد الطلاب:</strong> ' + data.students.length + '</p>' +
                        '<p><strong>إجمالي قاعدة البيانات:</strong> ' + data.total_count + '</p>';
                } else {
                    resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ: ' + data.error + '</p>';
                }
            } catch (e) {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في JSON: ' + e.message + '</p><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ خطأ في الشبكة: ' + error.message + '</p>';
        });
}

function testWithCategories() {
    const categories = ['صف الروضة', 'صف الحضانة', 'صف التمهيدي', 'صف التحضيري'];
    const resultDiv = document.getElementById('test-result');
    resultDiv.innerHTML = '<p>جاري اختبار الصفوف...</p>';
    
    let results = '<h4>نتائج اختبار الصفوف:</h4>';
    let completed = 0;
    
    categories.forEach(category => {
        fetch('student_attendance_data_fixed.php?date=" . date('Y-m-d') . "&category=' + encodeURIComponent(category))
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        results += '<p style=\"color: green;\">✅ ' + category + ': ' + data.students.length + ' طالب</p>';
                    } else {
                        results += '<p style=\"color: red;\">❌ ' + category + ': خطأ</p>';
                    }
                } catch (e) {
                    results += '<p style=\"color: red;\">❌ ' + category + ': خطأ في JSON</p>';
                }
                
                completed++;
                if (completed === categories.length) {
                    resultDiv.innerHTML = results;
                }
            })
            .catch(error => {
                results += '<p style=\"color: red;\">❌ ' + category + ': خطأ في الشبكة</p>';
                completed++;
                if (completed === categories.length) {
                    resultDiv.innerHTML = results;
                }
            });
    });
}
</script>";
echo "</div>";

// 4. عرض الصفوف الموجودة
if ($con) {
    echo "<div class='fix-box'>";
    echo "<h2>📚 الصفوف الموجودة في قاعدة البيانات</h2>";
    
    $categories_query = "SELECT catg, COUNT(*) as count FROM stud_tb WHERE catg IS NOT NULL AND catg != '' GROUP BY catg ORDER BY catg";
    $categories_result = $con->query($categories_query);
    
    if ($categories_result && $categories_result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>اسم الصف</th><th>عدد الطلاب</th></tr>";
        while ($row = $categories_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['catg']) . "</td>";
            echo "<td>" . $row['count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا توجد صفوف في قاعدة البيانات</p>";
    }
    echo "</div>";
}

// 5. الحلول النهائية
echo "<div class='solution-box'>";
echo "<h2>🎯 الحلول النهائية</h2>";
echo "<h3>الصفحات الجاهزة للاستخدام:</h3>";
echo "<ol>";
echo "<li><strong><a href='attandec.php' target='_blank'>الصفحة الرئيسية المحدثة</a></strong> - مع API محسن</li>";
echo "<li><strong><a href='attandec_simple.php' target='_blank'>الصفحة المبسطة</a></strong> - تصميم بسيط وسريع</li>";
echo "<li><strong><a href='student_attendance_data_fixed.php?date=" . date('Y-m-d') . "' target='_blank'>API الجديد</a></strong> - للاختبار المباشر</li>";
echo "</ol>";

echo "<h3>ملفات الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='test_api_quick.php' target='_blank'>اختبار سريع للـ API</a></li>";
echo "<li><a href='fix_attendance.php' target='_blank'>ملف الإصلاح الشامل</a></li>";
echo "</ul>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 التوصية النهائية:</h4>";
echo "<p><strong>استخدم الصفحة المبسطة</strong> (<a href='attandec_simple.php'>attandec_simple.php</a>) لأنها:</p>";
echo "<ul>";
echo "<li>✅ تعمل بدون مشاكل</li>";
echo "<li>✅ تدعم جميع الصفوف</li>";
echo "<li>✅ تصميم جميل ومتجاوب</li>";
echo "<li>✅ سريعة وموثوقة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

if ($con) {
    $con->close();
}
?>
