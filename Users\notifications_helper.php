<?php
// ملف مساعد للإشعارات في مجلد Users

// وظيفة إضافة إشعار طلب احتياج
function addNeedRequestNotification($con, $user_name, $need_type, $request_id) {
    try {
        // إنشاء جدول الإشعارات إذا لم يكن موجوداً
        $create_table_sql = "
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'info',
                target_role VARCHAR(20) DEFAULT 'Admin',
                related_id INT DEFAULT NULL,
                related_type VARCHAR(50) DEFAULT NULL,
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $con->query($create_table_sql);
        
        $title = "طلب احتياج جديد";
        $message = "تم إرسال طلب احتياج جديد من المستخدم: $user_name\nنوع الاحتياج: $need_type";
        
        // إضافة الإشعار للإدارة
        $sql = "INSERT INTO notifications (title, message, type, target_role, related_id, related_type) 
                VALUES (?, ?, 'need', 'Admin', ?, 'need_request')";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("ssi", $title, $message, $request_id);
        $stmt->execute();
        
        // إضافة نفس الإشعار للمدقق
        $sql = "INSERT INTO notifications (title, message, type, target_role, related_id, related_type) 
                VALUES (?, ?, 'need', 'Mod', ?, 'need_request')";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("ssi", $title, $message, $request_id);
        $stmt->execute();
        
        $stmt->close();
        return true;
        
    } catch (Exception $e) {
        error_log("خطأ في إضافة إشعار الاحتياج: " . $e->getMessage());
        return false;
    }
}

// وظيفة إضافة إشعار طلب إجازة
function addLeaveRequestNotification($con, $user_name, $leave_type, $start_date, $end_date, $request_id) {
    try {
        // إنشاء جدول الإشعارات إذا لم يكن موجوداً
        $create_table_sql = "
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'info',
                target_role VARCHAR(20) DEFAULT 'Admin',
                related_id INT DEFAULT NULL,
                related_type VARCHAR(50) DEFAULT NULL,
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $con->query($create_table_sql);
        
        $title = "طلب إجازة جديد";
        $message = "تم إرسال طلب إجازة جديد من المستخدم: $user_name\nنوع الإجازة: $leave_type\nمن: $start_date إلى: $end_date";
        
        // إضافة الإشعار للإدارة
        $sql = "INSERT INTO notifications (title, message, type, target_role, related_id, related_type) 
                VALUES (?, ?, 'leave', 'Admin', ?, 'leave_request')";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("ssi", $title, $message, $request_id);
        $stmt->execute();
        
        // إضافة نفس الإشعار للمدقق
        $sql = "INSERT INTO notifications (title, message, type, target_role, related_id, related_type) 
                VALUES (?, ?, 'leave', 'Mod', ?, 'leave_request')";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("ssi", $title, $message, $request_id);
        $stmt->execute();
        
        $stmt->close();
        return true;
        
    } catch (Exception $e) {
        error_log("خطأ في إضافة إشعار الإجازة: " . $e->getMessage());
        return false;
    }
}

// وظيفة إضافة إشعار عام
function addNotification($con, $title, $message, $type = 'info', $target_role = 'Admin', $related_id = null, $related_type = null) {
    try {
        // إنشاء جدول الإشعارات إذا لم يكن موجوداً
        $create_table_sql = "
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'info',
                target_role VARCHAR(20) DEFAULT 'Admin',
                related_id INT DEFAULT NULL,
                related_type VARCHAR(50) DEFAULT NULL,
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $con->query($create_table_sql);
        
        $sql = "INSERT INTO notifications (title, message, type, target_role, related_id, related_type) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("sssis", $title, $message, $type, $target_role, $related_id, $related_type);
        $result = $stmt->execute();
        $stmt->close();
        
        return $result;
        
    } catch (Exception $e) {
        error_log("خطأ في إضافة الإشعار: " . $e->getMessage());
        return false;
    }
}
?>
