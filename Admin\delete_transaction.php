<?php
session_start();
header('Content-Type: application/json');

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

include "addon/dbcon.php";

$type = isset($_GET['type']) ? $_GET['type'] : '';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($type) || $id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معاملات غير صحيحة']);
    exit();
}

$success = false;
$message = '';

switch ($type) {
    case 'ايرادات':
        // حذف من جدول المدفوعات
        $query = "DELETE FROM stud_pay WHERE id = ?";
        $stmt = mysqli_prepare($con, $query);
        mysqli_stmt_bind_param($stmt, "i", $id);
        $success = mysqli_stmt_execute($stmt);
        $message = $success ? 'تم حذف الإيراد بنجاح' : 'فشل في حذف الإيراد';
        mysqli_stmt_close($stmt);
        break;
        
    case 'مصروفات':
        // حذف من جدول المصروفات
        $query = "DELETE FROM depit_tb WHERE id = ?";
        $stmt = mysqli_prepare($con, $query);
        mysqli_stmt_bind_param($stmt, "i", $id);
        $success = mysqli_stmt_execute($stmt);
        $message = $success ? 'تم حذف المصروف بنجاح' : 'فشل في حذف المصروف';
        mysqli_stmt_close($stmt);
        break;
        
    case 'رواتب':
        // حذف من جدول الموظفين
        $query = "DELETE FROM employ_tb WHERE id = ?";
        $stmt = mysqli_prepare($con, $query);
        mysqli_stmt_bind_param($stmt, "i", $id);
        $success = mysqli_stmt_execute($stmt);
        $message = $success ? 'تم حذف الراتب بنجاح' : 'فشل في حذف الراتب';
        mysqli_stmt_close($stmt);
        break;
        
    default:
        $message = 'نوع المعاملة غير صحيح';
}

mysqli_close($con);

echo json_encode(['success' => $success, 'message' => $message]);
?>
