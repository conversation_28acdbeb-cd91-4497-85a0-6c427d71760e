<?php
session_start();
if(!isset($_SESSION['user']) || ($_SESSION['user']->role !== "Admin" && $_SESSION['user']->role !== "Mod")){
    header("location:../login.php");
    exit();
}

include "addon/dbcon.php";

$need_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if($need_id <= 0) {
    die("معرف الطلب غير صحيح");
}

// جلب بيانات الطلب
$query = "SELECT nr.*, u.user_name, ur.user_name as response_by_name 
          FROM needs_requests nr 
          JOIN users_tb u ON nr.user_id = u.id_user 
          LEFT JOIN users_tb ur ON nr.response_by = ur.id_user 
          WHERE nr.id_need = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $need_id);
$stmt->execute();
$result = $stmt->get_result();

if($result->num_rows == 0) {
    die("الطلب غير موجود");
}

$need = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>طباعة طلب احتياج</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .print-header {
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .print-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
        }
        .print-info {
            margin: 10px 0;
            color: #666;
        }
        .need-container {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 25px;
            background: #f9f9f9;
        }
        .need-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
        }
        .info-section {
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            margin: 12px 0;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #2c3e50;
            width: 150px;
            flex-shrink: 0;
        }
        .info-value {
            color: #495057;
            flex: 1;
        }
        .details-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #667eea;
        }
        .details-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin: 10px 0;
        }
        .status-pending { background: #ffc107; }
        .status-approved { background: #28a745; }
        .status-rejected { background: #dc3545; }
        .response-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none; }
        }
        .print-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="print-header">
        <h1>طلب احتياج</h1>
        <div class="print-info">تاريخ الطباعة: <?php echo date('Y/m/d H:i'); ?></div>
        <div class="print-info">طبع بواسطة: <?php echo $_SESSION['user']->user_name; ?></div>
    </div>

    <div class="need-container">
        <div class="need-title"><?php echo htmlspecialchars($need['need_name']); ?></div>
        
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">المستخدم:</div>
                <div class="info-value"><?php echo htmlspecialchars($need['user_name']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">تاريخ الطلب:</div>
                <div class="info-value"><?php echo date('Y/m/d', strtotime($need['request_date'])); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">نوع الاحتياج:</div>
                <div class="info-value"><?php echo $need['need_type']; ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">الحالة:</div>
                <div class="info-value">
                    <span class="status-badge status-<?php
                        echo $need['status'] == 'قيد المراجعة' ? 'pending' :
                            ($need['status'] == 'تم التوفير' ? 'approved' : 'rejected');
                    ?>">
                        <?php echo $need['status']; ?>
                    </span>
                </div>
            </div>
            <?php if($need['response_date']): ?>
            <div class="info-row">
                <div class="info-label">تاريخ الرد:</div>
                <div class="info-value"><?php echo date('Y/m/d H:i', strtotime($need['response_date'])); ?></div>
            </div>
            <?php endif; ?>
            <?php if($need['response_by_name']): ?>
            <div class="info-row">
                <div class="info-label">رد بواسطة:</div>
                <div class="info-value"><?php echo htmlspecialchars($need['response_by_name']); ?></div>
            </div>
            <?php endif; ?>
        </div>

        <div class="details-section">
            <div class="details-title">تفاصيل الاحتياج:</div>
            <div><?php echo nl2br(htmlspecialchars($need['need_details'])); ?></div>
        </div>

        <?php if($need['admin_response']): ?>
        <div class="response-section">
            <div class="details-title">رد الإدارة:</div>
            <div><?php echo nl2br(htmlspecialchars($need['admin_response'])); ?></div>
        </div>
        <?php endif; ?>
    </div>

    <div class="no-print">
        <button onclick="window.print()" class="print-btn">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button onclick="window.close()" class="print-btn" style="background: #6c757d;">
            إغلاق
        </button>
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
