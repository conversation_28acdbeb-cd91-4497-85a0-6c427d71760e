
/* CSS إضافي للإشعارات المحسنة */
.notifications-fixed-container {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    z-index: 999999 !important;
}

/* التأكد من عدم تداخل الإشعارات مع العناصر الأخرى */
nav, .navbar, header {
    z-index: 1000 !important;
}

body {
    position: relative !important;
}

/* تحسين عرض الإشعارات على الشاشات الصغيرة */
@media (max-width: 480px) {
    .notifications-fixed-container {
        top: 10px !important;
        left: 10px !important;
    }
    
    .notifications-dropdown-fixed {
        width: calc(100vw - 40px) !important;
        max-width: 300px !important;
    }
}

/* إخفاء الإشعارات القديمة إذا كانت موجودة */
.notifications-container:not(.notifications-fixed-container) {
    display: none !important;
}
