<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

$student = $_SESSION['student'];

// جلب طلبات الإجازة للطالب
$sql = "SELECT * FROM student_leaves WHERE student_id = ? ORDER BY request_date DESC";
$stmt = $con->prepare($sql);
$stmt->bind_param("i", $student->id);
$stmt->execute();
$result = $stmt->get_result();
$leaves = $result->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجازاتي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/student-style.css">
    <link rel="icon" href="css/icon.ico">
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container-fluid">
        <div class="leaves-section">
            <div class="leaves-header">
                <div class="header-icon">
                    <i class="fas fa-list-alt"></i>
                </div>
                <h1>إجازاتي</h1>
                <p>عرض جميع طلبات الإجازة المقدمة</p>
                <a href="request_leave.php" class="btn-new-request">
                    <i class="fas fa-plus"></i>
                    طلب إجازة جديد
                </a>
            </div>
            
            <div class="leaves-container">
                <?php if (empty($leaves)): ?>
                    <div class="no-leaves">
                        <div class="no-leaves-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <h3>لا توجد طلبات إجازة</h3>
                        <p>لم تقم بتقديم أي طلبات إجازة حتى الآن</p>
                        <a href="request_leave.php" class="btn-primary-custom">
                            <i class="fas fa-plus"></i>
                            تقديم طلب إجازة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="leaves-grid">
                        <?php foreach ($leaves as $leave): 
                            // حساب عدد الأيام
                            $start_date = new DateTime($leave['start_date']);
                            $end_date = new DateTime($leave['end_date']);
                            $days_count = $start_date->diff($end_date)->days + 1;
                            
                            // تحديد لون الحالة
                            $status_class = '';
                            switch($leave['status']) {
                                case 'قيد المراجعة':
                                    $status_class = 'status-pending';
                                    break;
                                case 'موافق':
                                    $status_class = 'status-approved';
                                    break;
                                case 'مرفوض':
                                    $status_class = 'status-rejected';
                                    break;
                                default:
                                    $status_class = 'status-pending';
                            }
                        ?>
                            <div class="leave-card">
                                <div class="leave-header">
                                    <div class="leave-type">
                                        <i class="fas fa-tag"></i>
                                        <?php echo htmlspecialchars($leave['leave_type']); ?>
                                    </div>
                                    <div class="leave-status <?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars($leave['status']); ?>
                                    </div>
                                </div>
                                
                                <div class="leave-details">
                                    <div class="detail-item">
                                        <i class="fas fa-calendar-alt"></i>
                                        <span class="label">من:</span>
                                        <span class="value"><?php echo date('Y/m/d', strtotime($leave['start_date'])); ?></span>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <i class="fas fa-calendar-check"></i>
                                        <span class="label">إلى:</span>
                                        <span class="value"><?php echo date('Y/m/d', strtotime($leave['end_date'])); ?></span>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <i class="fas fa-clock"></i>
                                        <span class="label">المدة:</span>
                                        <span class="value"><?php echo $days_count; ?> يوم</span>
                                    </div>
                                    
                                    <div class="detail-item">
                                        <i class="fas fa-calendar-plus"></i>
                                        <span class="label">تاريخ الطلب:</span>
                                        <span class="value"><?php echo date('Y/m/d H:i', strtotime($leave['request_date'])); ?></span>
                                    </div>
                                </div>
                                
                                <div class="leave-reason">
                                    <h4><i class="fas fa-comment-alt"></i> السبب:</h4>
                                    <p><?php echo nl2br(htmlspecialchars($leave['reason'])); ?></p>
                                </div>
                                
                                <?php if (!empty($leave['admin_response'])): ?>
                                    <div class="admin-response">
                                        <h4><i class="fas fa-reply"></i> رد الإدارة:</h4>
                                        <p><?php echo nl2br(htmlspecialchars($leave['admin_response'])); ?></p>
                                        <?php if (!empty($leave['response_date'])): ?>
                                            <small class="response-date">
                                                <i class="fas fa-clock"></i>
                                                <?php echo date('Y/m/d H:i', strtotime($leave['response_date'])); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.leave-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('animate-in');
            });
        });
    </script>
    
    <style>
        .leaves-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .leaves-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .leaves-header h1 {
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .leaves-header p {
            color: rgba(255,255,255,0.9);
            margin-bottom: 1.5rem;
        }
        
        .btn-new-request {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-new-request:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .leaves-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .no-leaves {
            text-align: center;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .no-leaves-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
        }
        
        .no-leaves h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .no-leaves p {
            color: #7f8c8d;
            margin-bottom: 2rem;
        }
        
        .leaves-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .leave-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .leave-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .leave-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .leave-type {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-weight: bold;
        }
        
        .leave-status {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #dc3545;
        }
        
        .leave-details {
            margin-bottom: 1.5rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .detail-item i {
            color: #3498db;
            margin-left: 0.5rem;
            width: 20px;
        }
        
        .detail-item .label {
            font-weight: bold;
            color: #2c3e50;
            margin-left: 0.5rem;
        }
        
        .detail-item .value {
            color: #34495e;
        }
        
        .leave-reason, .admin-response {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .leave-reason h4, .admin-response h4 {
            color: #2c3e50;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }
        
        .leave-reason p, .admin-response p {
            color: #34495e;
            line-height: 1.6;
            margin-bottom: 0;
        }
        
        .admin-response {
            background: #e8f5e8;
            border-right: 4px solid #28a745;
        }
        
        .response-date {
            color: #6c757d;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            display: block;
        }
        
        @media (max-width: 768px) {
            .leaves-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .leave-card {
                padding: 1rem;
            }
            
            .leave-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .detail-item {
                flex-direction: column;
                text-align: center;
                gap: 0.3rem;
            }
        }
    </style>
</body>
</html>
