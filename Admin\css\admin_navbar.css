/* تصميم شريط التنقل للأدمن مثل المستخدمين */
nav {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  height: 90px;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 0 55px;
  flex-wrap: wrap;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* إضافة مساحة للمحتوى تحت الشريط الثابت */
body {
  margin-top: 90px;
  padding-top: 20px;
}

/* تصميم الشعار */
.logo2 {
  order: 0;
  margin-left: 10px;
}

.logo2 img {
  width: 60px;
  height: 60px;
}

/* تصميم منطقة المستخدم */
.logo {
  order: 2;
  display: flex;
  align-items: center;
  gap: 15px;
}

/* تصميم الأزرار السريعة */
.quick-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  margin: 0 10px;
  flex-wrap: wrap;
  max-width: 70%;
  overflow-x: auto;
  padding: 5px 0;
  order: 1;
}

.quick-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  text-decoration: none;
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  min-width: 60px;
  position: relative;
  overflow: hidden;
  font-size: 0.8rem;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.quick-btn:hover:before {
  left: 100%;
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.3);
  color: white;
  text-decoration: none;
  border-color: rgba(255, 255, 255, 0.5);
}

.quick-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

.quick-btn i {
  font-size: 14px;
  color: white;
  margin-left: 2px;
}

.quick-btn span {
  font-size: 11px;
  font-weight: 500;
  color: white;
  text-align: center;
  line-height: 1.2;
}

/* أزرار خاصة بألوان مختلفة */
.quick-btn.home-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.quick-btn.home-btn:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.quick-btn.notifications-btn {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
  position: relative;
}

.quick-btn.notifications-btn:hover {
  box-shadow: 0 6px 20px rgba(253, 126, 20, 0.4);
}

/* شارة الإشعارات */
.notification-badge-nav {
  position: absolute !important;
  top: -8px !important;
  right: -5px !important;
  background: #ff4757 !important;
  color: white !important;
  border-radius: 50% !important;
  padding: 3px 7px !important;
  font-size: 0.7rem !important;
  font-weight: bold !important;
  min-width: 20px !important;
  text-align: center !important;
  animation: pulse 2s infinite !important;
  border: 2px solid white !important;
  z-index: 10 !important;
  box-shadow: 0 2px 5px rgba(0,0,0,0.3) !important;
}

@keyframes pulse {
  0% { 
    transform: scale(1);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 4px 10px rgba(255, 71, 87, 0.5);
  }
  100% { 
    transform: scale(1);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
  }
}

/* تصميم منطقة المستخدم */
.user-form {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
}

.welcome-label {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  white-space: nowrap;
}

#exit_btn {
  padding: 8px 15px;
  font-size: 0.9rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#exit_btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

/* تصميم متجاوب */
@media (max-width: 1000px) {
  nav {
    padding: 0 40px 0 50px;
  }

  .quick-actions {
    gap: 10px;
    margin: 0 10px;
  }

  .quick-btn {
    padding: 6px 8px;
    font-size: 0.75rem;
    min-width: 55px;
  }

  .quick-btn i {
    font-size: 12px;
  }

  .quick-btn span {
    font-size: 10px;
  }
}

@media (max-width: 768px) {
  /* تحسين الشريط للهواتف */
  nav {
    padding: 5px 10px;
    height: 70px;
  }
  
  body {
    margin-top: 70px;
  }
  
  /* تحسين التمرير الأفقي */
  .quick-actions {
    overflow-x: auto;
    overflow-y: visible;
    gap: 4px;
    margin: 0 5px;
    padding: 2px 0;
  }

  .quick-btn {
    padding: 5px 7px;
    font-size: 0.7rem;
    min-width: 50px;
    gap: 3px;
  }

  .quick-btn i {
    font-size: 11px;
  }

  .quick-btn span {
    font-size: 9px;
  }

  .user-form {
    flex-direction: column;
    gap: 8px;
  }

  .welcome-label {
    font-size: 0.8rem;
  }

  #exit_btn {
    font-size: 0.7rem;
    padding: 5px 10px;
  }

  .logo2 img {
    width: 50px;
    height: 50px;
  }

  .notification-badge-nav {
    top: -5px !important;
    right: -3px !important;
    padding: 2px 5px !important;
    font-size: 0.6rem !important;
    min-width: 16px !important;
  }
}

@media (max-width: 480px) {
  .quick-actions {
    gap: 3px;
    padding: 1px 0;
  }

  .quick-btn {
    padding: 4px 6px;
    font-size: 0.65rem;
    min-width: 45px;
    gap: 2px;
  }

  .quick-btn i {
    font-size: 10px;
  }

  .quick-btn span {
    font-size: 8px;
  }

  .logo2 img {
    width: 40px;
    height: 40px;
  }
}

/* إخفاء الشارة عند عدم وجود إشعارات */
.notification-badge-nav[style*='display: none'] {
  display: none !important;
}

/* إخفاء عناصر الشريط الجانبي القديم */
#click, .menu-btn, #capss {
  display: none !important;
}

/* تحسين الشبكة الرئيسية */
.grid {
  padding: 45px;
  margin-top: 20px;
  display: grid;
  grid-gap: 16px;
}

/* تصميم خاص لزر تطبيق الطالب */
.student-app-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
  border: 2px solid rgba(255,255,255,0.3) !important;
  animation: pulse-student-app 2s infinite;
  position: relative;
  overflow: hidden;
}

.student-app-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.student-app-btn:hover::before {
  left: 100%;
}

.student-app-btn:hover {
  background: linear-gradient(45deg, #ff5252, #d63031) !important;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

@keyframes pulse-student-app {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

/* تأثير خاص لأيقونة الهاتف */
.student-app-btn i {
  animation: phone-shake 3s infinite;
}

@keyframes phone-shake {
  0%, 100% { transform: rotate(0deg); }
  10%, 30%, 50%, 70%, 90% { transform: rotate(-5deg); }
  20%, 40%, 60%, 80% { transform: rotate(5deg); }
}
