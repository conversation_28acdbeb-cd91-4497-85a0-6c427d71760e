<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// جلب المعاملات
$employee_id = $_GET['employee_id'] ?? '';
$days = intval($_GET['days'] ?? 7); // عدد الأيام (افتراضي 7)

if (empty($employee_id)) {
    echo json_encode([
        'success' => false,
        'error' => 'معرف الموظف مطلوب'
    ]);
    exit();
}

try {
    // التحقق من وجود جدول stat2
    $check_table = $con->query("SHOW TABLES LIKE 'stat2'");
    if (!$check_table || $check_table->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'error' => 'جدول stat2 غير موجود',
            'history' => []
        ]);
        exit();
    }

    // جلب معلومات الموظف
    $emp_query = "SELECT f_name, job FROM employ_tb WHERE id_employ = ? OR id = ? OR f_name = ?";
    $emp_stmt = $con->prepare($emp_query);
    $emp_stmt->bind_param("sss", $employee_id, $employee_id, $employee_id);
    $emp_stmt->execute();
    $emp_result = $emp_stmt->get_result();
    $employee_info = $emp_result->fetch_assoc();

    if (!$employee_info) {
        echo json_encode([
            'success' => false,
            'error' => 'الموظف غير موجود'
        ]);
        exit();
    }

    // جلب سجل الحضور من جدول stat2 للأيام المحددة
    $history_query = "SELECT stat_employee, data_stat, leave_start_date, leave_end_date
                      FROM stat2
                      WHERE id_employee = ?
                      AND data_stat >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                      ORDER BY data_stat DESC";

    $history_stmt = $con->prepare($history_query);
    $history_stmt->bind_param("si", $employee_id, $days);
    $history_stmt->execute();
    $history_result = $history_stmt->get_result();

    $history = [];
    while ($record = $history_result->fetch_assoc()) {
        $date_only = date('Y-m-d', strtotime($record['data_stat']));
        $time_only = date('H:i', strtotime($record['data_stat']));

        $history[] = [
            'date' => $date_only,
            'date_formatted' => date('d/m/Y', strtotime($date_only)),
            'day_name' => date('l', strtotime($date_only)),
            'status' => $record['stat_employee'],
            'check_in_time' => $time_only,
            'check_out_time' => null, // غير متوفر في جدول stat2
            'leave_start_date' => $record['leave_start_date'],
            'leave_end_date' => $record['leave_end_date'],
            'data_stat' => $record['data_stat']
        ];
    }

    // إحصائيات سريعة من جدول stat2
    $stats_query = "SELECT
                    COUNT(*) as total_days,
                    SUM(CASE WHEN stat_employee = 'حاضر' THEN 1 ELSE 0 END) as present_days,
                    SUM(CASE WHEN stat_employee = 'متأخر' THEN 1 ELSE 0 END) as late_days,
                    SUM(CASE WHEN stat_employee = 'غائب' THEN 1 ELSE 0 END) as absent_days,
                    SUM(CASE WHEN stat_employee = 'إجازة' THEN 1 ELSE 0 END) as leave_days
                    FROM stat2
                    WHERE id_employee = ?
                    AND data_stat >= DATE_SUB(CURDATE(), INTERVAL ? DAY)";

    $stats_stmt = $con->prepare($stats_query);
    $stats_stmt->bind_param("si", $employee_id, $days);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    // حساب معدل الحضور
    $attendance_rate = 0;
    if ($stats['total_days'] > 0) {
        $attendance_rate = round((($stats['present_days'] + $stats['late_days']) / $stats['total_days']) * 100, 2);
    }
    
    echo json_encode([
        'success' => true,
        'employee' => [
            'id' => $employee_id,
            'name' => $employee_info['f_name'],
            'job' => $employee_info['job']
        ],
        'history' => $history,
        'stats' => [
            'total_days' => $stats['total_days'],
            'present_days' => $stats['present_days'],
            'late_days' => $stats['late_days'],
            'absent_days' => $stats['absent_days'],
            'leave_days' => $stats['leave_days'],
            'attendance_rate' => $attendance_rate
        ],
        'period' => $days
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في جلب البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// إغلاق الاتصال
$con->close();
?>
