<?php
session_start();
include "addon/dbcon.php";

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

// التحقق من وجود معرف الطلب
if (!isset($_POST['leave_id']) || empty($_POST['leave_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الطلب مطلوب']);
    exit();
}

$leave_id = intval($_POST['leave_id']);

try {
    // حذف الطلب من قاعدة البيانات
    $sql = "DELETE FROM leave_requests WHERE id_leave = ?";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("i", $leave_id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'تم حذف الطلب بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'الطلب غير موجود']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}

$con->close();
?>
