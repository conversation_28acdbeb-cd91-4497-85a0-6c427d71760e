<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

echo "<h2>تطبيق نظام الإشعارات الجديد في شريط التنقل</h2>";

// إزالة الإشعارات القديمة من جميع الصفحات
$admin_pages = [
    'home.php',
    'statistics.php',
    'allstud.php',
    'new_stud.php',
    'addstud.php',
    'manage_needs.php',
    'manage_leaves.php',
    'allDepit.php',
    'info_depit.php',
    'expired_stud.php',
    'expried_soon.php',
    'hadanaStud.php',
    'rodaStud.php',
    'tamhediStud.php',
    'tadeery.php',
    'info_employ.php',
    'info_user.php',
    'acounting.php',
    'infost.php',
    'reports.php',
    'attandec.php',
    'about_us.php'
];

$updated_count = 0;
$errors = [];

echo "<h3>إزالة الإشعارات القديمة:</h3>";

foreach ($admin_pages as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        $original_content = $content;
        
        // إزالة جميع أشكال الإشعارات القديمة
        $patterns_to_remove = [
            '/<!-- إضافة الإشعارات المحسنة -->.*?<?php include [\'"]notifications_fixed\.php[\'"];.*?\?>/s',
            '/<?php include [\'"]notifications_fixed\.php[\'"];.*?\?>/s',
            '/<?php include [\'"]notifications_component\.php[\'"];.*?\?>/s',
            '/<div class="notifications-wrapper".*?<\/div>/s',
            '/<div.*?notifications.*?<\/div>/s'
        ];
        
        foreach ($patterns_to_remove as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        // تنظيف الأسطر الفارغة الزائدة
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($page, $content)) {
                echo "<p style='color: green;'>✅ تم تنظيف $page</p>";
                $updated_count++;
            } else {
                $errors[] = "فشل في تحديث $page";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ $page لا يحتاج تنظيف</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ $page غير موجود</p>";
    }
}

echo "<h3>إنشاء ملف CSS محسن للشريط الجانبي:</h3>";

$navbar_css = "
/* تحسينات شريط التنقل مع الإشعارات */
nav ul {
    position: relative;
}

/* تحسين زر الإشعارات في الشريط الجانبي */
.notifications-nav-item a {
    position: relative !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.notifications-nav-item a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px) !important;
}

.notifications-nav-item a i {
    margin-left: 8px !important;
    font-size: 1.1rem !important;
}

/* تحسين الشارة */
.notification-badge-nav {
    position: absolute !important;
    top: -8px !important;
    right: 10px !important;
    background: #ff4757 !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 3px 7px !important;
    font-size: 0.7rem !important;
    font-weight: bold !important;
    min-width: 20px !important;
    text-align: center !important;
    animation: pulse 2s infinite !important;
    border: 2px solid white !important;
    z-index: 10 !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3) !important;
}

/* تحسين موقع الترحيب */
.top-user-section {
    position: absolute;
    top: 15px;
    right: 20px;
    z-index: 1001;
}

.user-form {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.welcome-label {
    color: white;
    font-weight: 600;
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    white-space: nowrap;
}

#exit_btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#exit_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .top-user-section {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px 20px;
        text-align: center;
    }
    
    .user-form {
        flex-direction: column;
        gap: 10px;
    }
    
    .welcome-label {
        font-size: 0.9rem;
    }
    
    #exit_btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    .notification-badge-nav {
        top: -5px !important;
        right: 5px !important;
        padding: 2px 5px !important;
        font-size: 0.6rem !important;
        min-width: 16px !important;
    }
}

/* تحسين النافذة المنبثقة للهواتف */
@media (max-width: 480px) {
    .notifications-modal-content {
        width: 98% !important;
        margin: 1% !important;
        max-height: 95vh !important;
    }
    
    .notifications-modal-header {
        padding: 12px 15px !important;
    }
    
    .notifications-modal-header h3 {
        font-size: 1rem !important;
    }
    
    .notification-item {
        padding: 12px 15px !important;
    }
    
    .notification-title {
        font-size: 0.9rem !important;
    }
    
    .notification-message {
        font-size: 0.8rem !important;
    }
}

/* تأثيرات إضافية */
.notifications-nav-item a.active {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تحسين الانيميشن */
@keyframes pulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 4px 10px rgba(255, 71, 87, 0.5);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
}

/* إخفاء الشارة عند عدم وجود إشعارات */
.notification-badge-nav[style*='display: none'] {
    display: none !important;
}

/* تحسين التباعد في الشريط الجانبي */
nav ul li {
    margin-bottom: 2px;
}

nav ul li a {
    padding: 12px 20px !important;
    border-radius: 5px !important;
    margin: 0 10px !important;
}
";

if (file_put_contents('css/navbar_notifications.css', $navbar_css)) {
    echo "<p style='color: green;'>✅ تم إنشاء ملف CSS للشريط الجانبي</p>";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف CSS</p>";
}

echo "<h3>إضافة CSS للصفحات:</h3>";

// إضافة رابط CSS للصفحات التي تحتاجه
$pages_needing_css = ['home.php', 'statistics.php', 'manage_needs.php', 'manage_leaves.php'];

foreach ($pages_needing_css as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        
        // التحقق من وجود رابط CSS
        if (strpos($content, 'navbar_notifications.css') === false) {
            // إضافة رابط CSS في head
            $css_link = '<link rel="stylesheet" href="css/navbar_notifications.css">';
            
            if (preg_match('/<\/head>/i', $content)) {
                $content = preg_replace('/(<\/head>)/i', "    $css_link\n$1", $content);
                
                if (file_put_contents($page, $content)) {
                    echo "<p style='color: green;'>✅ تم إضافة CSS لـ $page</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في إضافة CSS لـ $page</p>";
                }
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ CSS موجود مسبقاً في $page</p>";
        }
    }
}

echo "<h3>ملخص التحديث:</h3>";
echo "<p>📊 تم تنظيف <strong>$updated_count</strong> صفحة</p>";
echo "<p>🎨 تم إنشاء ملفات CSS محسنة</p>";
echo "<p>🔔 تم إضافة نظام الإشعارات الجديد في شريط التنقل</p>";

if (!empty($errors)) {
    echo "<h4>الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>❌ $error</p>";
    }
}

echo "<h3>اختبار النظام:</h3>";
echo "<ol>";
echo "<li>قم بزيارة <a href='home.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li>ابحث عن زر 'الإشعارات' في الشريط الجانبي (أول زر)</li>";
echo "<li>لاحظ الشارة الحمراء إذا كانت هناك إشعارات</li>";
echo "<li>انقر على زر الإشعارات لفتح النافذة المنبثقة</li>";
echo "<li>تأكد من عمل النظام على الهاتف والكمبيوتر</li>";
echo "</ol>";

echo "<h3>الميزات الجديدة:</h3>";
echo "<ul>";
echo "<li>🔔 زر الإشعارات في أول الشريط الجانبي</li>";
echo "<li>🔴 شارة حمراء تظهر عدد الإشعارات غير المقروءة</li>";
echo "<li>📱 نافذة منبثقة كاملة تعمل على جميع الأجهزة</li>";
echo "<li>⬆️ رفع الترحيب وزر الخروج بجانب الأزرار</li>";
echo "<li>🎨 تصميم محسن ومتجاوب</li>";
echo "<li>⚡ تحديث تلقائي كل 30 ثانية</li>";
echo "</ul>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='home.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>اختبار النظام</a>";
echo "<a href='test_notifications.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إرسال إشعار تجريبي</a>";
echo "</p>";
?>
