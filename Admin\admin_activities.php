<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول الأنشطة
$create_activities = "CREATE TABLE IF NOT EXISTS app_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    activity_date DATE NOT NULL,
    activity_time TIME,
    location VARCHAR(255),
    max_participants INT DEFAULT 0,
    current_participants INT DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_activities);

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_activity':
            $title = mysqli_real_escape_string($con, $_POST['title'] ?? '');
            $description = mysqli_real_escape_string($con, $_POST['description'] ?? '');
            $activity_date = mysqli_real_escape_string($con, $_POST['activity_date'] ?? '');
            $activity_time = mysqli_real_escape_string($con, $_POST['activity_time'] ?? '');
            $location = mysqli_real_escape_string($con, $_POST['location'] ?? '');
            $max_participants = (int)($_POST['max_participants'] ?? 0);
            
            if (!empty($title) && !empty($description) && !empty($activity_date)) {
                $sql = "INSERT INTO app_activities (title, description, activity_date, activity_time, location, max_participants, status, created_at) 
                        VALUES ('$title', '$description', '$activity_date', '$activity_time', '$location', $max_participants, 'active', NOW())";
                if (@mysqli_query($con, $sql)) {
                    $message = 'تم إضافة النشاط بنجاح!';
                } else {
                    $message = 'حدث خطأ أثناء إضافة النشاط!';
                    $messageType = 'error';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة!';
                $messageType = 'error';
            }
            break;
            
        case 'delete_activity':
            $id = (int)($_POST['id'] ?? 0);
            if ($id > 0) {
                if (@mysqli_query($con, "DELETE FROM app_activities WHERE id = $id")) {
                    $message = 'تم حذف النشاط بنجاح!';
                } else {
                    $message = 'حدث خطأ أثناء حذف النشاط!';
                    $messageType = 'error';
                }
            }
            break;
    }
}

// جلب الأنشطة
$activities = @mysqli_query($con, "SELECT * FROM app_activities ORDER BY activity_date DESC, created_at DESC");
if (!$activities) $activities = false;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 إدارة الأنشطة - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container-fluid {
            max-width: 1400px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .content-card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .btn-primary-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-danger-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
            color: white;
        }

        .activity-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .activity-title {
            color: #2c3e50;
            font-weight: bold;
            font-size: 1.3rem;
            margin: 0;
        }

        .activity-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .activity-description {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            line-height: 1.6;
            color: #2c3e50;
        }

        .participants-info {
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .badge-custom {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .status-completed { background: #17a2b8; color: white; }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .alert-error-custom {
            background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border-right: 5px solid #e74c3c;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .content-card-body {
                padding: 1.5rem;
            }
            
            .activity-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .activity-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container-fluid">
        <!-- Header -->
        <div class="header-card">
            <h1>🎯 إدارة الأنشطة</h1>
            <p style="color: #7f8c8d; margin: 0;">تنظيم الأنشطة والفعاليات المدرسية والترفيهية</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom <?php echo $messageType == 'success' ? 'alert-success-custom' : 'alert-error-custom'; ?>">
                <i class="fas <?php echo $messageType == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Add Activity Form -->
            <div class="col-md-4">
                <div class="content-card">
                    <div class="content-card-header">
                        <i class="fas fa-plus"></i> إضافة نشاط جديد
                    </div>
                    <div class="content-card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_activity">
                            <div class="mb-3">
                                <label class="form-label">عنوان النشاط:</label>
                                <input type="text" name="title" class="form-control" required placeholder="اكتب عنوان النشاط...">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تاريخ النشاط:</label>
                                <input type="date" name="activity_date" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">وقت النشاط:</label>
                                <input type="time" name="activity_time" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المكان:</label>
                                <input type="text" name="location" class="form-control" placeholder="مكان إقامة النشاط...">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للمشاركين:</label>
                                <input type="number" name="max_participants" class="form-control" min="0" placeholder="0 = بدون حد أقصى">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">وصف النشاط:</label>
                                <textarea name="description" class="form-control" rows="4" required placeholder="اكتب وصف مفصل للنشاط..."></textarea>
                            </div>
                            <button type="submit" class="btn-primary-custom w-100">
                                <i class="fas fa-plus"></i> إضافة النشاط
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Activities List -->
            <div class="col-md-8">
                <div class="content-card">
                    <div class="content-card-header">
                        <i class="fas fa-calendar-check"></i> قائمة الأنشطة
                    </div>
                    <div class="content-card-body">
                        <?php if ($activities && mysqli_num_rows($activities) > 0): ?>
                            <?php while ($row = mysqli_fetch_assoc($activities)): 
                                $activity_date = new DateTime($row['activity_date']);
                                $today = new DateTime();
                                $is_past = $activity_date < $today;
                                $status_class = $is_past ? 'status-completed' : 'status-active';
                                $status_text = $is_past ? 'منتهي' : 'نشط';
                            ?>
                                <div class="activity-card">
                                    <div class="activity-header">
                                        <h4 class="activity-title"><?php echo htmlspecialchars($row['title']); ?></h4>
                                        <div class="d-flex gap-2 align-items-center">
                                            <span class="badge-custom <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا النشاط؟')">
                                                <input type="hidden" name="action" value="delete_activity">
                                                <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                                <button type="submit" class="btn-danger-custom">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar" style="color: #3498db;"></i>
                                            <span><?php echo $activity_date->format('Y-m-d'); ?></span>
                                        </div>
                                        <?php if (!empty($row['activity_time'])): ?>
                                            <div class="meta-item">
                                                <i class="fas fa-clock" style="color: #f39c12;"></i>
                                                <span><?php echo date('H:i', strtotime($row['activity_time'])); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($row['location'])): ?>
                                            <div class="meta-item">
                                                <i class="fas fa-map-marker-alt" style="color: #e74c3c;"></i>
                                                <span><?php echo htmlspecialchars($row['location']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="activity-description">
                                        <?php echo nl2br(htmlspecialchars($row['description'])); ?>
                                    </div>
                                    
                                    <?php if ($row['max_participants'] > 0): ?>
                                        <div class="participants-info">
                                            <h6 style="color: #667eea; margin-bottom: 0.5rem;">
                                                <i class="fas fa-users"></i> المشاركون
                                            </h6>
                                            <div class="progress" style="height: 10px; border-radius: 10px;">
                                                <?php 
                                                $percentage = $row['max_participants'] > 0 ? 
                                                    ($row['current_participants'] / $row['max_participants']) * 100 : 0;
                                                ?>
                                                <div class="progress-bar" style="width: <?php echo $percentage; ?>%; background: linear-gradient(45deg, #667eea, #764ba2);"></div>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo $row['current_participants']; ?> من <?php echo $row['max_participants']; ?> مشارك
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="text-center" style="padding: 3rem;">
                                <i class="fas fa-calendar-check" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                                <h4 style="color: #7f8c8d;">لا توجد أنشطة حتى الآن</h4>
                                <p style="color: #95a5a6;">ابدأ بإضافة أول نشاط باستخدام النموذج على اليسار</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // Set minimum date to today for new activities
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="activity_date"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.min = today;
            }
        });

        // Form submission loading state
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.classList.contains('btn-danger-custom')) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                    submitBtn.disabled = true;
                }
            });
        });
    </script>
</body>
</html>
