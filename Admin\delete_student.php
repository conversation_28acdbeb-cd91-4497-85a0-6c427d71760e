<?php
session_start();
header('Content-Type: application/json');

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);
$student_id = isset($input['id']) ? (int)$input['id'] : 0;

if ($student_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب غير صحيح']);
    exit;
}

// بدء المعاملة
$con->begin_transaction();

try {
    // حذف المدفوعات أولاً
    $delete_payments = $con->prepare("DELETE FROM stud_pay WHERE id_stud = ?");
    $delete_payments->bind_param("i", $student_id);
    $delete_payments->execute();
    
    // حذف الطالب
    $delete_student = $con->prepare("DELETE FROM stud_tb WHERE id = ?");
    $delete_student->bind_param("i", $student_id);
    $delete_student->execute();
    
    if ($delete_student->affected_rows > 0) {
        $con->commit();
        echo json_encode(['success' => true, 'message' => 'تم حذف الطالب بنجاح']);
    } else {
        $con->rollback();
        echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
    }
} catch (Exception $e) {
    $con->rollback();
    echo json_encode(['success' => false, 'message' => 'خطأ في حذف الطالب: ' . $e->getMessage()]);
}

$con->close();
?>
