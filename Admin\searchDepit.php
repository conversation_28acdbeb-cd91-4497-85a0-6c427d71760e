<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث عن المصروفات</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>

   </head>
   <body>
  

  <div class="search">
  <section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
    <input type="text" id="live_search" placeholder="يمكنك البحث عن معلومات الطالب هنا">
    <button class="btn btn-secondary"  ><a href="info_depit.php" >اظهار حسب المستخدم</a></button>
    
  </div>
    <table class="table">
  <thead>
    <tr>
    <th scope="col">العمليات</th>
      <th scope="col">المستخدم</th>
      <th scope="col"> تاريخ عملية المصروفات</th>
      <th scope="col">قيمة المصروفات</th>
      <th scope="col">وصف المصروفات</th>
      
    </tr>
  </thead>
  <tbody id="tb">
  </tbody>

   </body>
   <script>
      $(document).ready(function () {
        $("#live_search").keydown(function(){
          var input = $(this).val();
          
          if(input != ""){
            $.ajax({
              method: "POST",
              url: "addon/searchFD.php",
              data: {input:input},
              success:function (data) {
                $("#tb").html(data);
              }
            });

          }else{
          
          }
        })
      });
    </script>
        <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           console.log(id)
          $.ajax({url:'addon/removedepit.php',
          method:"POST",
          data:({name:id}),
          success:function(response){
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

      }
    });
        });
      }
    
    </script>
</html>