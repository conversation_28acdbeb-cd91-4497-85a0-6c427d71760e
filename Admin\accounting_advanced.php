<?php
session_start();

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location:../login.php");
    exit();
}

if (isset($_GET['exitbtn'])) {
    header("location:../logout.php");
    exit();
}

include "addon/dbcon.php";

// معالجة الفلاتر
$selected_user = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
$selected_category = isset($_GET['category']) ? $_GET['category'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// جلب جميع المستخدمين
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = mysqli_query($con, $users_query);

// بناء شروط البحث
$where_conditions = [];
$params = [];

if ($selected_user > 0) {
    $where_conditions[] = "u.id_user = ?";
    $params[] = $selected_user;
}

if (!empty($selected_category)) {
    switch ($selected_category) {
        case 'ايرادات':
            $where_conditions[] = "sp.stud_pay > 0";
            break;
        case 'مصروفات':
            $where_conditions[] = "d.depit_amount > 0";
            break;
        case 'رواتب':
            $where_conditions[] = "e.employ_salary > 0";
            break;
    }
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(COALESCE(sp.date_pay, d.depit_date, e.employ_date)) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(COALESCE(sp.date_pay, d.depit_date, e.employ_date)) <= ?";
    $params[] = $date_to;
}

if (!empty($search_term)) {
    $where_conditions[] = "(u.user_name LIKE ? OR s.stud_name LIKE ? OR d.depit_name LIKE ? OR e.employ_name LIKE ?)";
    $search_param = "%$search_term%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

// بناء الاستعلام الموحد
$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// استعلام الإيرادات
$revenues_query = "
    SELECT 
        'ايرادات' as type,
        u.user_name,
        s.stud_name as item_name,
        sp.stud_pay as amount,
        sp.date_pay as transaction_date,
        sp.id as transaction_id,
        'طالب' as category
    FROM stud_pay sp
    JOIN stud_tb s ON sp.id_stud = s.id
    JOIN users_tb u ON s.userID = u.id_user
    $where_clause
";

// استعلام المصروفات
$expenses_query = "
    SELECT 
        'مصروفات' as type,
        u.user_name,
        d.depit_name as item_name,
        d.depit_amount as amount,
        d.depit_date as transaction_date,
        d.id as transaction_id,
        'مصروف' as category
    FROM depit_tb d
    JOIN users_tb u ON d.user_id = u.id_user
    $where_clause
";

// استعلام الرواتب
$salaries_query = "
    SELECT 
        'رواتب' as type,
        u.user_name,
        e.employ_name as item_name,
        e.employ_salary as amount,
        e.employ_date as transaction_date,
        e.id as transaction_id,
        'موظف' as category
    FROM employ_tb e
    JOIN users_tb u ON e.user_id = u.id_user
    $where_clause
";

// دمج الاستعلامات
$final_query = "";
if (empty($selected_category) || $selected_category == 'ايرادات') {
    $final_query .= $revenues_query;
}
if (empty($selected_category) || $selected_category == 'مصروفات') {
    if (!empty($final_query)) $final_query .= " UNION ALL ";
    $final_query .= $expenses_query;
}
if (empty($selected_category) || $selected_category == 'رواتب') {
    if (!empty($final_query)) $final_query .= " UNION ALL ";
    $final_query .= $salaries_query;
}

$final_query .= " ORDER BY transaction_date DESC LIMIT 1000";

// تنفيذ الاستعلام
$result = mysqli_query($con, $final_query);

// حساب الإحصائيات السريعة
$stats = [
    'total_revenue' => 0,
    'total_expenses' => 0,
    'total_salaries' => 0,
    'net_profit' => 0,
    'transaction_count' => 0
];

if ($result) {
    mysqli_data_seek($result, 0);
    while ($row = mysqli_fetch_assoc($result)) {
        $stats['transaction_count']++;
        switch ($row['type']) {
            case 'ايرادات':
                $stats['total_revenue'] += $row['amount'];
                break;
            case 'مصروفات':
                $stats['total_expenses'] += $row['amount'];
                break;
            case 'رواتب':
                $stats['total_salaries'] += $row['amount'];
                break;
        }
    }
    $stats['net_profit'] = $stats['total_revenue'] - $stats['total_expenses'] - $stats['total_salaries'];
    mysqli_data_seek($result, 0); // إعادة تعيين المؤشر
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الحسابات المتقدم - أكاديمية الأطفال</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/admin_navbar.css">
    <link rel="icon" href="css/icon.ico">
    
    <?php include "addon/topbar.php"; ?>
    
    <style>
        .accounting-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .filters-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            border-color: #3498db;
            outline: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.revenue {
            border-left-color: #27ae60;
        }

        .stat-card.expenses {
            border-left-color: #e74c3c;
        }

        .stat-card.salaries {
            border-left-color: #f39c12;
        }

        .stat-card.profit {
            border-left-color: #8e44ad;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stat-card.revenue .stat-icon { color: #27ae60; }
        .stat-card.expenses .stat-icon { color: #e74c3c; }
        .stat-card.salaries .stat-icon { color: #f39c12; }
        .stat-card.profit .stat-icon { color: #8e44ad; }

        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .actions-bar {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: #3498db;
            color: white;
        }

        .action-btn.success {
            background: #27ae60;
            color: white;
        }

        .action-btn.warning {
            background: #f39c12;
            color: white;
        }

        .action-btn.danger {
            background: #e74c3c;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }

        .transactions-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #34495e;
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }

        .table {
            margin: 0;
        }

        .table th {
            background: #ecf0f1;
            color: #2c3e50;
            font-weight: bold;
            border: none;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .type-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .type-badge.revenue {
            background: #d5f4e6;
            color: #27ae60;
        }

        .type-badge.expenses {
            background: #fadbd8;
            color: #e74c3c;
        }

        .type-badge.salaries {
            background: #fdeaa7;
            color: #f39c12;
        }

        .amount {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .amount.positive {
            color: #27ae60;
        }

        .amount.negative {
            color: #e74c3c;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .accounting-container {
                padding: 10px;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .actions-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="accounting-container">
        <div class="page-header">
            <h1><i class="fas fa-calculator"></i> نظام الحسابات المتقدم</h1>
            <p>إدارة شاملة للإيرادات والمصروفات والرواتب مع تحليلات مالية متقدمة</p>
        </div>

        <!-- قسم الفلاتر -->
        <div class="filters-section">
            <h3><i class="fas fa-filter"></i> فلاتر البحث والتصفية</h3>
            <form method="GET" action="" id="filtersForm">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="user_id">اختيار المستخدم</label>
                        <select name="user_id" id="user_id">
                            <option value="0">جميع المستخدمين</option>
                            <?php while ($user_row = mysqli_fetch_assoc($users_result)): ?>
                                <option value="<?php echo $user_row['id_user']; ?>"
                                        <?php echo ($selected_user == $user_row['id_user']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user_row['user_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="category">نوع الحساب</label>
                        <select name="category" id="category">
                            <option value="">جميع الأنواع</option>
                            <option value="ايرادات" <?php echo ($selected_category == 'ايرادات') ? 'selected' : ''; ?>>الإيرادات</option>
                            <option value="مصروفات" <?php echo ($selected_category == 'مصروفات') ? 'selected' : ''; ?>>المصروفات</option>
                            <option value="رواتب" <?php echo ($selected_category == 'رواتب') ? 'selected' : ''; ?>>الرواتب</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" name="date_from" id="date_from" value="<?php echo $date_from; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" name="date_to" id="date_to" value="<?php echo $date_to; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="search">البحث النصي</label>
                        <input type="text" name="search" id="search" placeholder="ابحث في الأسماء..." value="<?php echo htmlspecialchars($search_term); ?>">
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="action-btn primary">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                    <a href="accounting_advanced.php" class="action-btn warning">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="stats-grid">
            <div class="stat-card revenue">
                <div class="stat-icon">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_revenue']); ?></div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>

            <div class="stat-card expenses">
                <div class="stat-icon">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_expenses']); ?></div>
                <div class="stat-label">إجمالي المصروفات</div>
            </div>

            <div class="stat-card salaries">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_salaries']); ?></div>
                <div class="stat-label">إجمالي الرواتب</div>
            </div>

            <div class="stat-card profit">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number <?php echo ($stats['net_profit'] >= 0) ? 'positive' : 'negative'; ?>">
                    <?php echo number_format($stats['net_profit']); ?>
                </div>
                <div class="stat-label">صافي الربح</div>
            </div>
        </div>

        <!-- شريط الإجراءات -->
        <div class="actions-bar">
            <div>
                <span style="font-weight: bold; color: #2c3e50;">
                    <i class="fas fa-list"></i> عدد المعاملات: <?php echo number_format($stats['transaction_count']); ?>
                </span>
            </div>

            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button onclick="exportToExcel()" class="action-btn success">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button onclick="printReport()" class="action-btn primary">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <a href="add_depit.php" class="action-btn warning">
                    <i class="fas fa-plus"></i> إضافة مصروف
                </a>
                <button onclick="showSummaryModal()" class="action-btn danger">
                    <i class="fas fa-chart-pie"></i> ملخص مالي
                </button>
            </div>
        </div>

        <!-- جدول المعاملات -->
        <div class="transactions-table">
            <div class="table-header">
                <i class="fas fa-table"></i> سجل المعاملات المالية
            </div>

            <div class="table-responsive">
                <?php if ($result && mysqli_num_rows($result) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>المستخدم</th>
                                <th>اسم العنصر</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الفئة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td>
                                        <span class="type-badge <?php
                                            echo ($row['type'] == 'ايرادات') ? 'revenue' :
                                                (($row['type'] == 'مصروفات') ? 'expenses' : 'salaries');
                                        ?>">
                                            <?php echo $row['type']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['user_name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['item_name']); ?></td>
                                    <td>
                                        <span class="amount <?php echo ($row['type'] == 'ايرادات') ? 'positive' : 'negative'; ?>">
                                            <?php echo number_format($row['amount']); ?> د.ع
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($row['transaction_date'])); ?></td>
                                    <td><?php echo $row['category']; ?></td>
                                    <td>
                                        <div style="display: flex; gap: 5px;">
                                            <button onclick="viewDetails('<?php echo $row['type']; ?>', <?php echo $row['transaction_id']; ?>)"
                                                    class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($row['type'] == 'مصروفات'): ?>
                                                <a href="edit_depit.php?id=<?php echo $row['transaction_id']; ?>"
                                                   class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            <button onclick="deleteTransaction('<?php echo $row['type']; ?>', <?php echo $row['transaction_id']; ?>)"
                                                    class="btn btn-sm btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="no-data">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px; color: #bdc3c7;"></i>
                        <h3>لا توجد معاملات</h3>
                        <p>لم يتم العثور على معاملات مالية تطابق المعايير المحددة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة للملخص المالي -->
    <div class="modal fade" id="summaryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-chart-pie"></i> الملخص المالي التفصيلي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="pieChart" width="300" height="300"></canvas>
                        </div>
                        <div class="col-md-6">
                            <h6>التفاصيل المالية:</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>إجمالي الإيرادات:</span>
                                    <strong class="text-success"><?php echo number_format($stats['total_revenue']); ?> د.ع</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>إجمالي المصروفات:</span>
                                    <strong class="text-danger"><?php echo number_format($stats['total_expenses']); ?> د.ع</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>إجمالي الرواتب:</span>
                                    <strong class="text-warning"><?php echo number_format($stats['total_salaries']); ?> د.ع</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>صافي الربح:</span>
                                    <strong class="<?php echo ($stats['net_profit'] >= 0) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo number_format($stats['net_profit']); ?> د.ع
                                    </strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لعرض التفاصيل -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل المعاملة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailsContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين المكتبات -->
    <script src="js/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <script>
        // تطبيق الفلاتر تلقائياً عند التغيير
        document.addEventListener('DOMContentLoaded', function() {
            const filterInputs = document.querySelectorAll('#filtersForm select, #filtersForm input');

            filterInputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (this.type !== 'text') {
                        document.getElementById('filtersForm').submit();
                    }
                });
            });

            // البحث النصي مع تأخير
            const searchInput = document.getElementById('search');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    document.getElementById('filtersForm').submit();
                }, 1000);
            });

            // تأثيرات العد التصاعدي للإحصائيات
            animateNumbers();
        });

        // وظيفة تحريك الأرقام
        function animateNumbers() {
            document.querySelectorAll('.stat-number').forEach((element, index) => {
                const targetNumber = parseInt(element.textContent.replace(/,/g, ''));

                if (!isNaN(targetNumber) && targetNumber > 0) {
                    let currentNumber = 0;
                    const increment = targetNumber / 50;
                    const timer = setInterval(() => {
                        currentNumber += increment;
                        if (currentNumber >= targetNumber) {
                            currentNumber = targetNumber;
                            clearInterval(timer);
                        }
                        element.textContent = Math.floor(currentNumber).toLocaleString();
                    }, 30);
                }
            });
        }

        // عرض تفاصيل المعاملة
        function viewDetails(type, id) {
            const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
            const content = document.getElementById('detailsContent');

            content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
            modal.show();

            // استدعاء AJAX لجلب التفاصيل
            fetch(`get_transaction_details.php?type=${type}&id=${id}`)
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل التفاصيل</div>';
                });
        }

        // حذف معاملة
        function deleteTransaction(type, id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                fetch(`delete_transaction.php?type=${type}&id=${id}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ في الحذف: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        // عرض الملخص المالي
        function showSummaryModal() {
            const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
            modal.show();

            // رسم المخطط الدائري
            setTimeout(() => {
                drawPieChart();
            }, 500);
        }

        // رسم المخطط الدائري
        function drawPieChart() {
            const ctx = document.getElementById('pieChart').getContext('2d');

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['الإيرادات', 'المصروفات', 'الرواتب'],
                    datasets: [{
                        data: [
                            <?php echo $stats['total_revenue']; ?>,
                            <?php echo $stats['total_expenses']; ?>,
                            <?php echo $stats['total_salaries']; ?>
                        ],
                        backgroundColor: [
                            '#27ae60',
                            '#e74c3c',
                            '#f39c12'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // تصدير إلى Excel
        function exportToExcel() {
            const table = document.querySelector('.table');
            if (!table) {
                alert('لا توجد بيانات للتصدير');
                return;
            }

            // إنشاء workbook جديد
            const wb = XLSX.utils.book_new();

            // تحويل الجدول إلى worksheet
            const ws = XLSX.utils.table_to_sheet(table);

            // إضافة worksheet إلى workbook
            XLSX.utils.book_append_sheet(wb, ws, 'الحسابات');

            // إنشاء اسم الملف مع التاريخ
            const date = new Date().toISOString().split('T')[0];
            const filename = `تقرير_الحسابات_${date}.xlsx`;

            // تحميل الملف
            XLSX.writeFile(wb, filename);
        }

        // طباعة التقرير
        function printReport() {
            const printWindow = window.open('', '_blank');

            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير الحسابات - أكاديمية الأطفال</title>
                    <style>
                        body {
                            font-family: 'Cairo', Arial, sans-serif;
                            margin: 20px;
                            color: #000;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 3px solid #2c3e50;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #2c3e50;
                            margin: 0;
                            font-size: 28px;
                        }
                        .stats-summary {
                            display: grid;
                            grid-template-columns: repeat(4, 1fr);
                            gap: 15px;
                            margin-bottom: 30px;
                        }
                        .stat-box {
                            border: 2px solid #ddd;
                            padding: 15px;
                            text-align: center;
                            border-radius: 8px;
                        }
                        .stat-box h4 {
                            margin: 0 0 10px 0;
                            color: #2c3e50;
                        }
                        .stat-box .number {
                            font-size: 20px;
                            font-weight: bold;
                        }
                        .revenue .number { color: #27ae60; }
                        .expenses .number { color: #e74c3c; }
                        .salaries .number { color: #f39c12; }
                        .profit .number { color: #8e44ad; }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 20px;
                        }
                        th, td {
                            border: 1px solid #000;
                            padding: 8px;
                            text-align: center;
                        }
                        th {
                            background: #f0f0f0;
                            font-weight: bold;
                        }
                        .print-date {
                            text-align: left;
                            margin-top: 30px;
                            font-size: 12px;
                            color: #666;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📊 تقرير الحسابات - أكاديمية الأطفال</h1>
                        <p>تقرير مالي شامل للإيرادات والمصروفات والرواتب</p>
                    </div>

                    <div class="stats-summary">
                        <div class="stat-box revenue">
                            <h4>إجمالي الإيرادات</h4>
                            <div class="number"><?php echo number_format($stats['total_revenue']); ?> د.ع</div>
                        </div>
                        <div class="stat-box expenses">
                            <h4>إجمالي المصروفات</h4>
                            <div class="number"><?php echo number_format($stats['total_expenses']); ?> د.ع</div>
                        </div>
                        <div class="stat-box salaries">
                            <h4>إجمالي الرواتب</h4>
                            <div class="number"><?php echo number_format($stats['total_salaries']); ?> د.ع</div>
                        </div>
                        <div class="stat-box profit">
                            <h4>صافي الربح</h4>
                            <div class="number"><?php echo number_format($stats['net_profit']); ?> د.ع</div>
                        </div>
                    </div>

                    ${document.querySelector('.table') ? document.querySelector('.table').outerHTML : '<p>لا توجد بيانات للطباعة</p>'}

                    <div class="print-date">
                        تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();

            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }

        // تحديث الصفحة كل 5 دقائق
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 300000);

        console.log('💰 نظام الحسابات المتقدم جاهز!');
    </script>
</body>
</html>
