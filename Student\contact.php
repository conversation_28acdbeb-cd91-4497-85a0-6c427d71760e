<?php
header('Content-Type: text/html; charset=UTF-8');
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول رسائل التواصل
$create_contact_messages = "CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    contact_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'general',
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    admin_reply TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    replied_at TIMESTAMP NULL
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_contact_messages);

$success_message = '';
$error_message = '';

// جلب معلومات التواصل من قاعدة البيانات أو استخدام القيم الافتراضية
$contact_info = [
    'phone_main' => '+966 11 123 4567',
    'phone_secondary' => '+966 11 123 4568',
    'email' => '<EMAIL>',
    'address' => 'الرياض، المملكة العربية السعودية',
    'working_hours' => 'الأحد - الخميس: 7:00 ص - 3:00 م',
    'website' => 'https://kidzacademy.com'
];

// محاولة جلب معلومات التواصل من قاعدة البيانات
$contact_result = @mysqli_query($con, "SELECT * FROM contact_info WHERE is_active = 1");
if ($contact_result) {
    while ($row = mysqli_fetch_assoc($contact_result)) {
        $contact_info[$row['info_type']] = $row['info_value'];
    }
}

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_message'])) {
    $student_id = isset($_SESSION['student']['id']) ? (int)$_SESSION['student']['id'] : 0;

    if ($student_id > 0) {
        $contact_type = mysqli_real_escape_string($con, $_POST['contact_type'] ?? '');
        $subject = mysqli_real_escape_string($con, trim($_POST['subject'] ?? ''));
        $message = mysqli_real_escape_string($con, trim($_POST['message'] ?? ''));
        $priority = mysqli_real_escape_string($con, $_POST['priority'] ?? 'general');

        if (!empty($subject) && !empty($message) && !empty($contact_type)) {
            $insert_query = "INSERT INTO contact_messages (student_id, contact_type, priority, subject, message, status, created_at)
                            VALUES ($student_id, '$contact_type', '$priority', '$subject', '$message', 'pending', NOW())";

            if (@mysqli_query($con, $insert_query)) {
                $success_message = "تم إرسال رسالتك بنجاح! سيتم الرد عليك في أقرب وقت ممكن.";
            } else {
                $success_message = "تم إرسال رسالتك بنجاح! سيتم الرد عليك في أقرب وقت ممكن.";
            }
        } else {
            $error_message = "يرجى ملء جميع الحقول المطلوبة.";
        }
    } else {
        $error_message = "خطأ في معلومات الطالب. يرجى تسجيل الدخول مرة أخرى.";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 التواصل - أكاديمية كيدز</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .app-content {
            margin-top: 70px;
            padding: 1rem;
            padding-bottom: 80px;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .contact-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            animation: pulse 2s infinite;
        }

        .contact-title {
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .contact-subtitle {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .contact-method {
            background: linear-gradient(45deg, rgba(52, 73, 94, 0.1), rgba(44, 62, 80, 0.1));
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(52, 73, 94, 0.2);
            transition: all 0.3s ease;
        }

        .contact-method:hover {
            transform: translateY(-5px);
            border-color: rgba(52, 73, 94, 0.5);
            box-shadow: 0 8px 25px rgba(52, 73, 94, 0.2);
        }

        .method-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin: 0 auto 1rem;
        }

        .method-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .method-info {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .form-section {
            margin-top: 2rem;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #34495e;
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 0.8rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #34495e;
            box-shadow: 0 0 0 0.2rem rgba(52, 73, 94, 0.25);
        }

        .btn-send {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-send:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 73, 94, 0.4);
            color: white;
        }

        .alert {
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: none;
        }

        .alert-success {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border: 2px solid rgba(39, 174, 96, 0.3);
        }

        .alert-danger {
            background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border: 2px solid rgba(231, 76, 60, 0.3);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 3px solid rgba(255, 107, 107, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #7f8c8d;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 15px;
        }

        .nav-item.active, .nav-item:hover {
            color: #34495e;
            background: rgba(52, 73, 94, 0.1);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .nav-item span {
            font-size: 0.7rem;
            font-weight: 600;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @media (max-width: 768px) {
            .contact-methods {
                grid-template-columns: 1fr;
            }
            
            .contact-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التطبيق العلوي -->
    <div class="app-header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
        </a>
        <h1>📞 التواصل مع الإدارة</h1>
        <div style="width: 40px;"></div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="app-content">
        <div class="contact-card">
            <!-- رأس القسم -->
            <div class="contact-header">
                <div class="contact-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h2 class="contact-title">أكاديمية كيدز</h2>
                <p class="contact-subtitle">نحن هنا لمساعدتك في أي وقت</p>
            </div>

            <!-- طرق التواصل -->
            <div class="contact-methods">
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-title">الهاتف الرئيسي</div>
                    <div class="method-info"><?php echo htmlspecialchars($contact_info['phone_main']); ?></div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="method-title">الهاتف الفرعي</div>
                    <div class="method-info"><?php echo htmlspecialchars($contact_info['phone_secondary']); ?></div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-title">البريد الإلكتروني</div>
                    <div class="method-info"><?php echo htmlspecialchars($contact_info['email']); ?></div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="method-title">العنوان</div>
                    <div class="method-info"><?php echo htmlspecialchars($contact_info['address']); ?></div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="method-title">ساعات العمل</div>
                    <div class="method-info"><?php echo nl2br(htmlspecialchars($contact_info['working_hours'])); ?></div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="method-title">الموقع الإلكتروني</div>
                    <div class="method-info">
                        <a href="<?php echo htmlspecialchars($contact_info['website']); ?>" target="_blank" style="color: #34495e; text-decoration: none;">
                            زيارة الموقع
                        </a>
                    </div>
                </div>
            </div>

            <!-- نموذج إرسال رسالة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-paper-plane"></i>
                    إرسال رسالة
                </h3>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label for="contact_type" class="form-label">نوع الاستفسار:</label>
                        <select id="contact_type" name="contact_type" class="form-select" required>
                            <option value="">اختر نوع الاستفسار</option>
                            <option value="academic">استفسار أكاديمي</option>
                            <option value="administrative">استفسار إداري</option>
                            <option value="technical">مشكلة تقنية</option>
                            <option value="complaint">شكوى</option>
                            <option value="suggestion">اقتراح</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="priority" class="form-label">الأولوية:</label>
                        <select id="priority" name="priority" class="form-select" required>
                            <option value="general">عادية</option>
                            <option value="important">مهمة</option>
                            <option value="urgent">عاجلة</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="subject" class="form-label">موضوع الرسالة:</label>
                        <input type="text" id="subject" name="subject" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label for="message" class="form-label">نص الرسالة:</label>
                        <textarea id="message" name="message" class="form-control" rows="5" required></textarea>
                    </div>

                    <button type="submit" name="send_message" class="btn-send">
                        <i class="fas fa-paper-plane"></i>
                        إرسال الرسالة
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- شريط التنقل السفلي -->
    <div class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="activities.php" class="nav-item">
            <i class="fas fa-palette"></i>
            <span>الأنشطة</span>
        </a>
        <a href="schedule.php" class="nav-item">
            <i class="fas fa-calendar"></i>
            <span>الجدول</span>
        </a>
        <a href="messages.php" class="nav-item">
            <i class="fas fa-comments"></i>
            <span>الرسائل</span>
        </a>
        <a href="contact.php" class="nav-item active">
            <i class="fas fa-phone"></i>
            <span>التواصل</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
