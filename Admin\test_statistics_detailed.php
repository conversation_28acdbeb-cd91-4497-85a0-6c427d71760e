<?php
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار الإحصائيات المفصل</h2>";

include "addon/dbcon.php";

// الحصول على جميع المستخدمين الذين لديهم طلاب
$sql = "SELECT DISTINCT u.id_user, u.user_name, COUNT(s.id) as student_count
        FROM users_tb u
        LEFT JOIN stud_tb s ON u.id_user = s.userID
        GROUP BY u.id_user, u.user_name
        HAVING student_count > 0
        ORDER BY student_count DESC";

$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<h3>المستخدمين الذين لديهم طلاب:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>عدد الطلاب</th><th>اختبار الإحصائيات</th></tr>";
    
    while ($user = mysqli_fetch_assoc($result)) {
        $user_id = $user['id_user'];
        $user_name = $user['user_name'];
        $student_count = $user['student_count'];
        
        echo "<tr>";
        echo "<td>$user_id</td>";
        echo "<td>$user_name</td>";
        echo "<td>$student_count</td>";
        echo "<td><a href='#user_$user_id' onclick='testUser($user_id)'>اختبار</a></td>";
        echo "</tr>";
        
        // اختبار الإحصائيات لهذا المستخدم
        echo "<tr id='user_$user_id' style='display: none;'>";
        echo "<td colspan='4'>";
        
        $datenow = date('Y-m-d');
        
        // 1. عدد الطلاب الفعالين
        $sql_active = "SELECT COUNT(*) as active_count FROM stud_tb, stud_pay, users_tb 
                      WHERE stud_tb.id = stud_pay.id_stud 
                      AND users_tb.id_user = stud_tb.userID 
                      AND users_tb.id_user = $user_id
                      AND DATEDIFF(stud_pay.date_exp, '$datenow') > 10";
        $result_active = mysqli_query($con, $sql_active);
        $active_count = $result_active ? mysqli_fetch_assoc($result_active)['active_count'] : 0;

        // 2. عدد الطلاب المنتهي اشتراكهم
        $sql_expired = "SELECT COUNT(*) as expired_count FROM stud_tb, stud_pay, users_tb 
                       WHERE stud_tb.id = stud_pay.id_stud 
                       AND users_tb.id_user = stud_tb.userID 
                       AND users_tb.id_user = $user_id
                       AND DATEDIFF(stud_pay.date_exp, '$datenow') <= 0";
        $result_expired = mysqli_query($con, $sql_expired);
        $expired_count = $result_expired ? mysqli_fetch_assoc($result_expired)['expired_count'] : 0;

        // 3. عدد الطلاب قريب الانتهاء
        $sql_soon = "SELECT COUNT(*) as soon_count FROM stud_tb, stud_pay, users_tb 
                    WHERE stud_tb.id = stud_pay.id_stud 
                    AND users_tb.id_user = stud_tb.userID 
                    AND users_tb.id_user = $user_id
                    AND DATEDIFF(stud_pay.date_exp, '$datenow') BETWEEN 1 AND 10";
        $result_soon = mysqli_query($con, $sql_soon);
        $soon_count = $result_soon ? mysqli_fetch_assoc($result_soon)['soon_count'] : 0;

        // 4. عدد الطلاب التسجيل الجديد
        $current_month = date('Y-m');
        $sql_new = "SELECT COUNT(*) as new_count FROM stud_tb, users_tb 
                   WHERE users_tb.id_user = stud_tb.userID 
                   AND users_tb.id_user = $user_id
                   AND DATE_FORMAT(stud_tb.datein, '%Y-%m') = '$current_month'";
        $result_new = mysqli_query($con, $sql_new);
        $new_count = $result_new ? mysqli_fetch_assoc($result_new)['new_count'] : 0;

        // 5. المجموع الكلي
        $sql_total = "SELECT COUNT(*) as total_count FROM stud_tb, users_tb 
                     WHERE users_tb.id_user = stud_tb.userID 
                     AND users_tb.id_user = $user_id";
        $result_total = mysqli_query($con, $sql_total);
        $total_count = $result_total ? mysqli_fetch_assoc($result_total)['total_count'] : 0;

        echo "<div style='padding: 10px; background: #f0f0f0;'>";
        echo "<h4>إحصائيات المستخدم: $user_name</h4>";
        echo "<p>🟢 الطلاب الفعالين: $active_count</p>";
        echo "<p>🔴 المنتهي اشتراكهم: $expired_count</p>";
        echo "<p>🟡 قريب الانتهاء: $soon_count</p>";
        echo "<p>🔵 التسجيل الجديد: $new_count</p>";
        echo "<p>🟣 المجموع الكلي: $total_count</p>";
        
        // JSON للاختبار
        $statistics = array(
            'active' => $active_count,
            'expired' => $expired_count,
            'soon' => $soon_count,
            'new' => $new_count,
            'total' => $total_count
        );
        echo "<p><strong>JSON:</strong> " . json_encode($statistics, JSON_UNESCAPED_UNICODE) . "</p>";
        echo "</div>";
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ لا توجد مستخدمين لديهم طلاب</p>";
}

// اختبار المستخدم رقم 7 تحديداً
echo "<h3>اختبار المستخدم رقم 7 (A3) تحديداً:</h3>";
$user_id = 7;
$sql_check = "SELECT COUNT(*) as count FROM stud_tb WHERE userID = $user_id";
$result_check = mysqli_query($con, $sql_check);
if ($result_check) {
    $count = mysqli_fetch_assoc($result_check)['count'];
    echo "<p>عدد الطلاب للمستخدم رقم 7: $count</p>";
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠️ المستخدم رقم 7 لا يحتوي على أي طلاب</p>";
        echo "<p>هذا يفسر سبب ظهور 'No data available in table'</p>";
    }
}
?>

<script>
function testUser(userId) {
    var element = document.getElementById('user_' + userId);
    if (element.style.display === 'none') {
        element.style.display = 'table-row';
    } else {
        element.style.display = 'none';
    }
}
</script>
