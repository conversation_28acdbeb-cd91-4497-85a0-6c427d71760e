<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/topbar.php";
include "addon/dbcon.php";

// اختبار سريع للبيانات
if (isset($_POST['test_user'])) {
    $user_id = intval($_POST['test_user']);
    
    echo "<h3>اختبار المستخدم ID: $user_id</h3>";
    
    // اختبار 1: عدد الطلاب
    $count_query = "SELECT COUNT(*) as count FROM stud_tb WHERE userID = $user_id";
    $count_result = mysqli_query($con, $count_query);
    $count = mysqli_fetch_assoc($count_result)['count'];
    echo "<p>عدد الطلاب في stud_tb: $count</p>";
    
    // اختبار 2: عدد الطلاب مع بيانات الدفع
    $pay_query = "SELECT COUNT(*) as count FROM stud_tb, stud_pay WHERE stud_tb.id = stud_pay.id_stud AND stud_tb.userID = $user_id";
    $pay_result = mysqli_query($con, $pay_query);
    $pay_count = mysqli_fetch_assoc($pay_result)['count'];
    echo "<p>عدد الطلاب مع بيانات الدفع: $pay_count</p>";
    
    // اختبار 3: الاستعلام الكامل
    $full_query = "SELECT stud_tb.*, stud_pay.*, users_tb.user_name
                   FROM stud_tb, stud_pay, users_tb 
                   WHERE stud_pay.id_stud = stud_tb.id 
                   AND stud_tb.userID = users_tb.id_user 
                   AND users_tb.id_user = $user_id 
                   ORDER BY stud_tb.datein DESC LIMIT 5";
    
    $full_result = mysqli_query($con, $full_query);
    
    if ($full_result) {
        $full_count = mysqli_num_rows($full_result);
        echo "<p>نتائج الاستعلام الكامل: $full_count</p>";
        
        if ($full_count > 0) {
            echo "<table border='1'>";
            echo "<tr><th>اسم الطالب</th><th>العمر</th><th>الصنف</th><th>تاريخ التسجيل</th><th>المبلغ</th></tr>";
            while ($row = mysqli_fetch_assoc($full_result)) {
                echo "<tr>";
                echo "<td>" . $row['name'] . "</td>";
                echo "<td>" . $row['age'] . "</td>";
                echo "<td>" . $row['catg'] . "</td>";
                echo "<td>" . $row['datein'] . "</td>";
                echo "<td>" . $row['cash_stud'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>لا توجد نتائج للاستعلام الكامل</p>";
        }
    } else {
        echo "<p style='color: red;'>خطأ في الاستعلام: " . mysqli_error($con) . "</p>";
    }
    
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع لإصلاح البيانات</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-box { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background: #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار سريع لإصلاح البيانات</h1>
        
        <div class="test-box">
            <h3>اختبار المستخدمين والبيانات</h3>
            <form method="POST">
                <label>اختر مستخدم للاختبار:</label>
                <select name="test_user" class="form-select" style="margin: 10px 0;">
                    <option value="">اختر مستخدم</option>
                    <?php
                    $users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
                    $users_result = mysqli_query($con, $users_query);
                    if ($users_result) {
                        while ($user = mysqli_fetch_assoc($users_result)) {
                            echo '<option value="' . $user['id_user'] . '">' . $user['user_name'] . ' (ID: ' . $user['id_user'] . ')</option>';
                        }
                    }
                    ?>
                </select>
                <button type="submit" class="btn btn-primary">اختبار البيانات</button>
            </form>
        </div>

        <div class="test-box">
            <h3>إحصائيات عامة</h3>
            <?php
            $total_users = mysqli_num_rows(mysqli_query($con, "SELECT id_user FROM users_tb WHERE role = 'User'"));
            $total_students = mysqli_num_rows(mysqli_query($con, "SELECT id FROM stud_tb"));
            $students_with_pay = mysqli_num_rows(mysqli_query($con, "SELECT DISTINCT stud_tb.id FROM stud_tb, stud_pay WHERE stud_tb.id = stud_pay.id_stud"));
            ?>
            <p><strong>عدد المستخدمين:</strong> <?php echo $total_users; ?></p>
            <p><strong>عدد الطلاب:</strong> <?php echo $total_students; ?></p>
            <p><strong>الطلاب مع بيانات دفع:</strong> <?php echo $students_with_pay; ?></p>
            <p><strong>الطلاب بدون بيانات دفع:</strong> <?php echo $total_students - $students_with_pay; ?></p>
        </div>

        <div class="test-box">
            <h3>اختبار الحقول الجديدة</h3>
            <?php
            $health_check = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'health_status'");
            $registration_check = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'registration_status'");
            $p_name_check = mysqli_query($con, "SHOW COLUMNS FROM stud_tb LIKE 'p_name'");
            
            echo "<p><strong>health_status:</strong> " . (mysqli_num_rows($health_check) > 0 ? "✅ موجود" : "❌ غير موجود") . "</p>";
            echo "<p><strong>registration_status:</strong> " . (mysqli_num_rows($registration_check) > 0 ? "✅ موجود" : "❌ غير موجود") . "</p>";
            echo "<p><strong>p_name:</strong> " . (mysqli_num_rows($p_name_check) > 0 ? "✅ موجود" : "❌ غير موجود") . "</p>";
            ?>
        </div>

        <div class="test-box">
            <h3>المشكلة المحتملة</h3>
            <div class="error">
                <p><strong>السبب الأكثر احتمالاً:</strong></p>
                <ul>
                    <li>المستخدمون لديهم طلاب في جدول stud_tb لكن بدون بيانات دفع في جدول stud_pay</li>
                    <li>الاستعلام يستخدم INNER JOIN مما يتطلب وجود بيانات في كلا الجدولين</li>
                    <li>يجب استخدام LEFT JOIN أو إضافة بيانات دفع للطلاب</li>
                </ul>
            </div>
        </div>

        <div class="test-box">
            <h3>الحل السريع</h3>
            <div class="success">
                <p>سأقوم بتحديث الاستعلام ليستخدم LEFT JOIN بدلاً من INNER JOIN</p>
                <a href="infost.php" class="btn btn-success" target="_blank">اختبار صفحة الطلاب</a>
            </div>
        </div>
    </div>
</body>
</html>
