<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

include "dbcon.php";

// التحقق من الاتصال
if (!$con) {
    echo json_encode(['error' => 'خطأ في الاتصال']);
    exit();
}

// حساب الإحصائيات
$stats = [
    'revenue' => 0,
    'expenses' => 0,
    'salaries' => 0
];

// إجمالي الإيرادات
$revenue_query = "SELECT SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
$revenue_result = mysqli_query($con, $revenue_query);
if ($revenue_result && $row = mysqli_fetch_assoc($revenue_result)) {
    $stats['revenue'] = $row['total'] ? intval($row['total']) : 0;
}

// إجمالي المصروفات
$expenses_query = "SELECT SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
$expenses_result = mysqli_query($con, $expenses_query);
if ($expenses_result && $row = mysqli_fetch_assoc($expenses_result)) {
    $stats['expenses'] = $row['total'] ? intval($row['total']) : 0;
}

// إجمالي الرواتب
$salaries_query = "SELECT SUM(salary) as total FROM employ_tb WHERE salary > 0";
$salaries_result = mysqli_query($con, $salaries_query);
if ($salaries_result && $row = mysqli_fetch_assoc($salaries_result)) {
    $stats['salaries'] = $row['total'] ? intval($row['total']) : 0;
}

// إرجاع النتائج
header('Content-Type: application/json');
echo json_encode($stats);

mysqli_close($con);
?>
