<?php
session_start();
include "dbcon.php";

echo '<div class="alert alert-success">';
echo '<h4>✅ نظام المحاسبة يعمل بنجاح!</h4>';
echo '</div>';

echo '<div class="row">';

// الإيرادات
echo '<div class="col-md-4">';
echo '<div class="card border-success">';
echo '<div class="card-header bg-success text-white">الإيرادات</div>';
echo '<div class="card-body">';

$revenue_query = "SELECT COUNT(*) as count, SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0";
$revenue_result = mysqli_query($con, $revenue_query);
if ($revenue_result) {
    $revenue_data = mysqli_fetch_assoc($revenue_result);
    echo '<h5>عدد المعاملات: ' . $revenue_data['count'] . '</h5>';
    echo '<h4 class="text-success">IQD ' . number_format($revenue_data['total']) . '</h4>';
} else {
    echo '<p>لا توجد بيانات</p>';
}

echo '</div></div></div>';

// المصروفات
echo '<div class="col-md-4">';
echo '<div class="card border-danger">';
echo '<div class="card-header bg-danger text-white">المصروفات</div>';
echo '<div class="card-body">';

$expenses_query = "SELECT COUNT(*) as count, SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0";
$expenses_result = mysqli_query($con, $expenses_query);
if ($expenses_result) {
    $expenses_data = mysqli_fetch_assoc($expenses_result);
    echo '<h5>عدد المعاملات: ' . $expenses_data['count'] . '</h5>';
    echo '<h4 class="text-danger">IQD ' . number_format($expenses_data['total']) . '</h4>';
} else {
    echo '<p>لا توجد بيانات</p>';
}

echo '</div></div></div>';

// الرواتب
echo '<div class="col-md-4">';
echo '<div class="card border-warning">';
echo '<div class="card-header bg-warning text-dark">الرواتب</div>';
echo '<div class="card-body">';

$salaries_query = "SELECT COUNT(*) as count, SUM(salary) as total FROM employ_tb WHERE salary > 0";
$salaries_result = mysqli_query($con, $salaries_query);
if ($salaries_result) {
    $salaries_data = mysqli_fetch_assoc($salaries_result);
    echo '<h5>عدد الموظفين: ' . $salaries_data['count'] . '</h5>';
    echo '<h4 class="text-warning">IQD ' . number_format($salaries_data['total']) . '</h4>';
} else {
    echo '<p>لا توجد بيانات</p>';
}

echo '</div></div></div>';
echo '</div>';

// آخر المعاملات
echo '<div class="mt-4">';
echo '<h5>آخر الإيرادات:</h5>';
echo '<div class="table-responsive">';
echo '<table class="table table-striped">';
echo '<thead><tr><th>الطالب</th><th>المبلغ</th><th>التاريخ</th></tr></thead>';
echo '<tbody>';

$recent_revenue = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein FROM stud_tb, stud_pay WHERE stud_pay.id_stud = stud_tb.id ORDER BY stud_pay.datein DESC LIMIT 5";
$recent_result = mysqli_query($con, $recent_revenue);
if ($recent_result && mysqli_num_rows($recent_result) > 0) {
    while ($row = mysqli_fetch_assoc($recent_result)) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($row['name']) . '</td>';
        echo '<td class="text-success">IQD ' . number_format($row['cash_stud']) . '</td>';
        echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="3" class="text-center">لا توجد بيانات</td></tr>';
}

echo '</tbody></table></div></div>';

mysqli_close($con);
?>
