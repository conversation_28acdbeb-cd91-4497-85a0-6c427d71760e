<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

include 'addon/dbcon.php';

echo "<h2>إصلاح مشاكل نظام الإشعارات</h2>";

// إنشاء جدول الإشعارات إذا لم يكن موجوداً
echo "<h3>1. إنشاء جدول الإشعارات:</h3>";
$create_notifications = "CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('leave_request','need_request','leave_response','need_response','general') NOT NULL,
  `related_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if (mysqli_query($con, $create_notifications)) {
    echo "<p style='color: green;'>✅ تم إنشاء جدول الإشعارات بنجاح</p>";
} else {
    echo "<p style='color: orange;'>⚠️ جدول الإشعارات موجود مسبقاً</p>";
}

// حذف الإشعارات المكررة
echo "<h3>2. حذف الإشعارات المكررة:</h3>";
$delete_duplicates = "DELETE n1 FROM notifications n1
INNER JOIN notifications n2 
WHERE n1.id > n2.id 
AND n1.user_id = n2.user_id 
AND n1.title = n2.title 
AND n1.message = n2.message 
AND n1.type = n2.type 
AND n1.related_id = n2.related_id
AND ABS(TIMESTAMPDIFF(MINUTE, n1.created_at, n2.created_at)) < 5";

$result = mysqli_query($con, $delete_duplicates);
$deleted_count = mysqli_affected_rows($con);
echo "<p style='color: green;'>✅ تم حذف $deleted_count إشعار مكرر</p>";

// إنشاء إشعار تجريبي للمستخدم الحالي
echo "<h3>3. إنشاء إشعار تجريبي:</h3>";
$user_id = $_SESSION['user']->id_user;
$test_notification = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, 'إشعار تجريبي', 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح', 'general')";
$stmt = $con->prepare($test_notification);
$stmt->bind_param("i", $user_id);

if ($stmt->execute()) {
    echo "<p style='color: green;'>✅ تم إنشاء إشعار تجريبي</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الإشعار التجريبي</p>";
}

// عرض إحصائيات الإشعارات
echo "<h3>4. إحصائيات الإشعارات:</h3>";
$stats = [
    'إجمالي الإشعارات' => "SELECT COUNT(*) as count FROM notifications",
    'الإشعارات غير المقروءة' => "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0",
    'إشعارات اليوم' => "SELECT COUNT(*) as count FROM notifications WHERE DATE(created_at) = CURDATE()",
    'إشعارات طلبات الإجازة' => "SELECT COUNT(*) as count FROM notifications WHERE type = 'leave_request'",
    'إشعارات طلبات الاحتياج' => "SELECT COUNT(*) as count FROM notifications WHERE type = 'need_request'"
];

foreach ($stats as $label => $query) {
    $result = mysqli_query($con, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>📊 $label: <strong>{$row['count']}</strong></p>";
    }
}

// عرض آخر 5 إشعارات
echo "<h3>5. آخر 5 إشعارات:</h3>";
$recent_notifications = "SELECT n.*, u.user_name FROM notifications n 
                        LEFT JOIN users_tb u ON n.user_id = u.id_user 
                        ORDER BY n.created_at DESC LIMIT 5";
$result = mysqli_query($con, $recent_notifications);

if (mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>المستخدم</th><th>العنوان</th><th>النوع</th><th>التاريخ</th><th>مقروء</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $read_status = $row['is_read'] ? '✅ مقروء' : '❌ غير مقروء';
        echo "<tr>";
        echo "<td>{$row['user_name']}</td>";
        echo "<td>{$row['title']}</td>";
        echo "<td>{$row['type']}</td>";
        echo "<td>{$row['created_at']}</td>";
        echo "<td>$read_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد إشعارات</p>";
}

echo "<h3>6. اختبار إرسال إشعار:</h3>";
echo "<form method='POST'>";
echo "<input type='hidden' name='test_notification' value='1'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>إرسال إشعار تجريبي لجميع المدراء</button>";
echo "</form>";

if (isset($_POST['test_notification'])) {
    // إرسال إشعار تجريبي لجميع المدراء
    $admin_query = "SELECT id_user, user_name FROM users_tb WHERE role IN ('Admin', 'Moda', 'Modaqeq')";
    $admin_result = mysqli_query($con, $admin_query);
    
    $count = 0;
    while ($admin_row = mysqli_fetch_assoc($admin_result)) {
        $insert_notification = "INSERT INTO notifications (user_id, title, message, type) VALUES (?, 'إشعار تجريبي', 'هذا إشعار تجريبي من النظام للتأكد من عمل الإشعارات', 'general')";
        $notification_stmt = $con->prepare($insert_notification);
        $notification_stmt->bind_param("i", $admin_row['id_user']);
        if ($notification_stmt->execute()) {
            $count++;
        }
    }
    echo "<p style='color: green;'>✅ تم إرسال إشعار تجريبي لـ $count مدير</p>";
}

echo "<p><a href='home.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى الصفحة الرئيسية</a></p>";

$con->close();
?>
