<?php
header('Content-Type: text/html; charset=UTF-8');
session_start();

if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";
mysqli_query($con, "SET NAMES utf8");
mysqli_query($con, "SET CHARACTER SET utf8");
mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء الجداول
$create_activities = "CREATE TABLE IF NOT EXISTS app_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    activity_date DATE NOT NULL,
    activity_time TIME,
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_activities);

$create_gallery = "CREATE TABLE IF NOT EXISTS activity_gallery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_url VARCHAR(500) NOT NULL,
    caption TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_gallery);

// جلب البيانات
$activities = [];
$activities_result = @mysqli_query($con, "SELECT * FROM app_activities WHERE is_active = 1 ORDER BY activity_date DESC");
if ($activities_result) {
    while ($row = mysqli_fetch_assoc($activities_result)) {
        $activities[] = $row;
    }
}

$gallery_images = [];
$gallery_result = @mysqli_query($con, "SELECT * FROM activity_gallery WHERE is_active = 1 ORDER BY upload_date DESC LIMIT 12");
if ($gallery_result) {
    while ($row = mysqli_fetch_assoc($gallery_result)) {
        $gallery_images[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>📅 الأنشطة - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Comic Sans MS', Arial, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            direction: rtl;
            margin: 0;
            padding: 0;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .app-content {
            margin-top: 70px;
            padding: 1rem;
            padding-bottom: 80px;
        }

        .section-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-align: center;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            aspect-ratio: 1;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .gallery-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 0.8rem;
            font-size: 0.8rem;
            text-align: center;
        }

        .activity-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .activity-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .activity-date {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem;
            border-radius: 10px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .activity-description {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .activity-location {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .no-content {
            text-align: center;
            padding: 3rem 1rem;
            color: #7f8c8d;
        }

        .no-content i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .image-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }

        .modal-content img {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
        }

        .modal-caption {
            color: white;
            text-align: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 0 0 10px 10px;
        }

        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .section-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }
            
            .app-content {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
        </a>
        <h1>📅 الأنشطة والفعاليات</h1>
        <div style="width: 40px;"></div>
    </div>

    <div class="app-content">
        <!-- معرض صور الأنشطة -->
        <div class="section-card">
            <h2 class="section-title">
                <i class="fas fa-images"></i> معرض صور الأنشطة
            </h2>
            
            <?php if (!empty($gallery_images)): ?>
                <div class="gallery-grid">
                    <?php foreach ($gallery_images as $image): ?>
                        <div class="gallery-item" onclick="openImageModal('<?php echo htmlspecialchars($image['image_url']); ?>', '<?php echo htmlspecialchars($image['caption']); ?>')">
                            <img src="<?php echo htmlspecialchars($image['image_url']); ?>" 
                                 alt="<?php echo htmlspecialchars($image['caption']); ?>"
                                 onerror="this.src='https://via.placeholder.com/200x200?text=صورة+غير+متاحة'">
                            <div class="gallery-caption">
                                <?php echo htmlspecialchars($image['caption']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-content">
                    <i class="fas fa-camera"></i>
                    <h4>لا توجد صور متاحة</h4>
                    <p>سيتم إضافة صور الأنشطة قريباً</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- الأنشطة القادمة -->
        <div class="section-card">
            <h2 class="section-title">
                <i class="fas fa-calendar-check"></i> الأنشطة القادمة
            </h2>
            
            <?php if (!empty($activities)): ?>
                <?php foreach ($activities as $activity): ?>
                    <div class="activity-card">
                        <div class="activity-title">
                            <?php echo htmlspecialchars($activity['title']); ?>
                        </div>
                        
                        <div class="activity-date">
                            <i class="fas fa-calendar"></i>
                            <?php echo date('Y/m/d', strtotime($activity['activity_date'])); ?>
                            <?php if (!empty($activity['activity_time'])): ?>
                                <i class="fas fa-clock ms-2"></i>
                                <?php echo date('H:i', strtotime($activity['activity_time'])); ?>
                            <?php endif; ?>
                        </div>
                        
                        <?php if (!empty($activity['description'])): ?>
                            <div class="activity-description">
                                <?php echo nl2br(htmlspecialchars($activity['description'])); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($activity['location'])): ?>
                            <div class="activity-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo htmlspecialchars($activity['location']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-content">
                    <i class="fas fa-calendar-times"></i>
                    <h4>لا توجد أنشطة مجدولة</h4>
                    <p>سيتم الإعلان عن الأنشطة الجديدة قريباً</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة عرض الصور -->
    <div class="image-modal" id="imageModal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeImageModal()">&times;</button>
            <img id="modalImage" src="" alt="">
            <div class="modal-caption" id="modalCaption"></div>
        </div>
    </div>

    <script>
        function openImageModal(imageUrl, caption) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');
            
            modalImage.src = imageUrl;
            modalCaption.textContent = caption;
            modal.classList.add('show');
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
