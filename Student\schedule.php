<?php
header('Content-Type: text/html; charset=UTF-8');
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_query($con, "SET NAMES utf8");
mysqli_query($con, "SET CHARACTER SET utf8");
mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول الجدول الدراسي إذا لم يكن موجوداً
$create_table = "CREATE TABLE IF NOT EXISTS app_schedule (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    subject VARCHAR(100) NOT NULL,
    teacher_name VARCHAR(100),
    day_of_week ENUM('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    room VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8";
mysqli_query($con, $create_table);

// جلب الجدول الدراسي
$schedule_query = "SELECT * FROM app_schedule WHERE is_active = 1 ORDER BY 
                  FIELD(day_of_week, 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'), 
                  start_time";
$schedule_result = mysqli_query($con, $schedule_query);

// إضافة جدول افتراضي إذا لم يوجد
if (mysqli_num_rows($schedule_result) == 0) {
    $default_schedule = [
        ['class_name' => 'الروضة الأولى', 'subject' => 'اللغة العربية', 'teacher_name' => 'أ. فاطمة', 'day_of_week' => 'sunday', 'start_time' => '08:00:00', 'end_time' => '09:00:00', 'room' => 'الفصل 1'],
        ['class_name' => 'الروضة الأولى', 'subject' => 'الرياضيات', 'teacher_name' => 'أ. أحمد', 'day_of_week' => 'sunday', 'start_time' => '09:00:00', 'end_time' => '10:00:00', 'room' => 'الفصل 1'],
        ['class_name' => 'الروضة الأولى', 'subject' => 'الفنون', 'teacher_name' => 'أ. مريم', 'day_of_week' => 'monday', 'start_time' => '08:00:00', 'end_time' => '09:00:00', 'room' => 'قاعة الفنون'],
        ['class_name' => 'الروضة الأولى', 'subject' => 'الموسيقى', 'teacher_name' => 'أ. علي', 'day_of_week' => 'tuesday', 'start_time' => '08:00:00', 'end_time' => '09:00:00', 'room' => 'قاعة الموسيقى'],
        ['class_name' => 'الروضة الأولى', 'subject' => 'الرياضة', 'teacher_name' => 'أ. سارة', 'day_of_week' => 'wednesday', 'start_time' => '08:00:00', 'end_time' => '09:00:00', 'room' => 'الملعب'],
        ['class_name' => 'الروضة الأولى', 'subject' => 'العلوم', 'teacher_name' => 'أ. محمد', 'day_of_week' => 'thursday', 'start_time' => '08:00:00', 'end_time' => '09:00:00', 'room' => 'مختبر العلوم']
    ];
    
    foreach ($default_schedule as $item) {
        $insert_query = "INSERT INTO app_schedule (class_name, subject, teacher_name, day_of_week, start_time, end_time, room) 
                        VALUES ('{$item['class_name']}', '{$item['subject']}', '{$item['teacher_name']}', 
                               '{$item['day_of_week']}', '{$item['start_time']}', '{$item['end_time']}', '{$item['room']}')";
        mysqli_query($con, $insert_query);
    }
    
    // إعادة جلب الجدول
    $schedule_result = mysqli_query($con, $schedule_query);
}

// تنظيم الجدول حسب الأيام
$schedule_by_day = [];
$days_arabic = [
    'sunday' => 'الأحد',
    'monday' => 'الاثنين', 
    'tuesday' => 'الثلاثاء',
    'wednesday' => 'الأربعاء',
    'thursday' => 'الخميس',
    'friday' => 'الجمعة',
    'saturday' => 'السبت'
];

while ($row = mysqli_fetch_assoc($schedule_result)) {
    $schedule_by_day[$row['day_of_week']][] = $row;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#45b7d1">
    <title>📅 الجدول الدراسي - أكاديمية كيدز</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/app-style.css">
    <style>
        body {
            font-family: 'Comic Sans MS', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffd1ff 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(45deg, #45b7d1, #96c93d);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-header h1 {
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .app-content {
            margin-top: 70px;
            padding: 1rem;
            padding-bottom: 80px;
        }

        .day-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .day-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 2px dashed #45b7d1;
        }

        .day-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #45b7d1, #96c93d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        .day-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: bold;
            margin: 0;
        }

        .subject-item {
            background: linear-gradient(45deg, rgba(69, 183, 209, 0.1), rgba(150, 201, 61, 0.1));
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border: 2px solid rgba(69, 183, 209, 0.2);
            transition: all 0.3s ease;
        }

        .subject-item:hover {
            transform: translateX(-5px);
            border-color: rgba(69, 183, 209, 0.5);
            box-shadow: 0 5px 15px rgba(69, 183, 209, 0.3);
        }

        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .subject-name {
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .subject-time {
            background: #45b7d1;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .subject-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .teacher-name {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .room-name {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .current-day {
            border: 3px solid #45b7d1;
            box-shadow: 0 0 20px rgba(69, 183, 209, 0.3);
        }

        .current-day .day-header {
            background: linear-gradient(45deg, rgba(69, 183, 209, 0.1), rgba(150, 201, 61, 0.1));
            border-radius: 15px;
            padding: 1rem;
            margin: -0.5rem -0.5rem 1rem -0.5rem;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 3px solid rgba(255, 107, 107, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #7f8c8d;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 15px;
        }

        .nav-item.active, .nav-item:hover {
            color: #45b7d1;
            background: rgba(69, 183, 209, 0.1);
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .nav-item span {
            font-size: 0.7rem;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @media (max-width: 768px) {
            .day-card {
                padding: 1rem;
            }
            
            .subject-header {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }
            
            .subject-details {
                flex-direction: column;
                align-items: stretch;
                gap: 0.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التطبيق العلوي -->
    <div class="app-header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
        </a>
        <h1>📅 الجدول الدراسي</h1>
        <div style="width: 40px;"></div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="app-content">
        <?php if (!empty($schedule_by_day)): ?>
            <?php 
            $current_day = strtolower(date('l'));
            $day_mapping = [
                'sunday' => 'sunday',
                'monday' => 'monday', 
                'tuesday' => 'tuesday',
                'wednesday' => 'wednesday',
                'thursday' => 'thursday',
                'friday' => 'friday',
                'saturday' => 'saturday'
            ];
            ?>
            
            <?php foreach ($days_arabic as $day_en => $day_ar): ?>
                <?php if (isset($schedule_by_day[$day_en])): ?>
                    <div class="day-card <?php echo $current_day == $day_en ? 'current-day' : ''; ?> animate__animated animate__fadeInUp">
                        <div class="day-header">
                            <div class="day-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <h3 class="day-title">
                                <?php echo $day_ar; ?>
                                <?php if ($current_day == $day_en): ?>
                                    <span style="color: #45b7d1; font-size: 0.8rem;">(اليوم)</span>
                                <?php endif; ?>
                            </h3>
                        </div>
                        
                        <?php foreach ($schedule_by_day[$day_en] as $subject): ?>
                            <div class="subject-item">
                                <div class="subject-header">
                                    <div class="subject-name">
                                        <i class="fas fa-book"></i>
                                        <?php echo htmlspecialchars($subject['subject']); ?>
                                    </div>
                                    <div class="subject-time">
                                        <?php echo date('H:i', strtotime($subject['start_time'])); ?> - 
                                        <?php echo date('H:i', strtotime($subject['end_time'])); ?>
                                    </div>
                                </div>
                                <div class="subject-details">
                                    <div class="teacher-name">
                                        <i class="fas fa-user-tie"></i>
                                        <?php echo htmlspecialchars($subject['teacher_name']); ?>
                                    </div>
                                    <div class="room-name">
                                        <i class="fas fa-door-open"></i>
                                        <?php echo htmlspecialchars($subject['room']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-calendar-alt"></i>
                <h3>لا يوجد جدول دراسي حالياً</h3>
                <p>سيتم عرض الجدول الدراسي هنا عند توفره</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- شريط التنقل السفلي -->
    <div class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="activities.php" class="nav-item">
            <i class="fas fa-palette"></i>
            <span>الأنشطة</span>
        </a>
        <a href="schedule.php" class="nav-item active">
            <i class="fas fa-calendar"></i>
            <span>الجدول</span>
        </a>
        <a href="messages.php" class="nav-item">
            <i class="fas fa-comments"></i>
            <span>الرسائل</span>
        </a>
        <a href="news.php" class="nav-item">
            <i class="fas fa-newspaper"></i>
            <span>الأخبار</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الدخول
            const dayCards = document.querySelectorAll('.day-card');
            dayCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate__animated', 'animate__fadeInUp');
                }, index * 100);
            });

            // تمرير تلقائي لليوم الحالي
            const currentDayCard = document.querySelector('.current-day');
            if (currentDayCard) {
                setTimeout(() => {
                    currentDayCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 500);
            }

            // تأثيرات اللمس للهواتف
            if (isMobileDevice()) {
                addTouchEffects();
            }
        });

        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        function addTouchEffects() {
            const touchElements = document.querySelectorAll('.day-card, .subject-item, .nav-item, .back-btn');
            
            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s';
                });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        }
    </script>
</body>
</html>
