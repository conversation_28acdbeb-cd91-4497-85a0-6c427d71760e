@font-face {
    font-family:"LamaSans-Medium";
    src: url(JannaLT.ttf);
}

*{
margin: 0;
padding: 0;
font-family:"LamaSans-Medium",sans-serif;
box-sizing: border-box;
text-decoration: none;
list-style: none;

}
body{
  background-image:url(121.jpg) ; 
  background-repeat:no-repeat;
  background-attachment: fixed;
  background-size:100% 100%;
 
}

nav{
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  height: 90px;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 0 55px;
  flex-wrap: wrap;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تصميم الأزرار السريعة */
.quick-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  margin: 0 10px;
  flex-wrap: wrap;
  max-width: 70%;
  overflow-x: auto;
  padding: 5px 0;
  order: 1;
}

.quick-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  text-decoration: none;
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  min-width: 60px;
  position: relative;
  overflow: hidden;
  font-size: 0.8rem;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.quick-btn:hover:before {
  left: 100%;
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.3);
  color: white;
  text-decoration: none;
  border-color: rgba(255, 255, 255, 0.5);
}

.quick-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

.quick-btn i {
  font-size: 14px;
  color: white;
  margin-left: 2px;
}

.quick-btn span {
  font-size: 11px;
  font-weight: 500;
  color: white;
  text-align: center;
  line-height: 1.2;
}

/* أزرار خاصة بألوان مختلفة */
.quick-btn.home-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.quick-btn.home-btn:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.quick-btn.need-btn {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
}

.quick-btn.need-btn:hover {
  box-shadow: 0 6px 20px rgba(253, 126, 20, 0.4);
}

.quick-btn.leave-btn {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.quick-btn.leave-btn:hover {
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.quick-btn.report-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
  box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
}

.quick-btn.report-btn:hover {
  box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
}

.quick-btn.all-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.quick-btn.all-btn:hover {
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.quick-btn.about-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.quick-btn.about-btn:hover {
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* فاصل بين المجموعات */
.separator {
  width: 2px;
  height: 35px;
  background: linear-gradient(to bottom, transparent, #ffffff80, transparent);
  margin: 0 8px;
  border-radius: 1px;
}

/* تأثيرات خاصة لكل زر */
.quick-btn:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.quick-btn:nth-child(2) {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.quick-btn:nth-child(3) {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
}

.quick-btn:nth-child(4) {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
}
nav .logo{
  padding-bottom:5px;
  color: #fff;
  font-size: 40px;
  font-weight: 600;
}
#exit_btn{
  margin-top: 9px;
 
}
.logo label{
  font-size: 25px;
}

nav ul{
  padding-bottom:5px;
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding-right: 10px;
}
nav ul li{
  
  padding-top: 15px;
  margin: 0 5px;
}
nav ul li a{
  color: #f2f2f2;
  text-decoration: none;
  font-size: 15px;
  font-weight: 300;
  padding: 8px 15px;
  border-radius: 5px;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}
nav ul li a.active,
nav ul li a:hover{
  color: #111;
  background: #fff;
  text-decoration: none;
}
#capss{
  color: aliceblue;
  font-size: 30px;
  cursor: pointer;
  display: none;
  margin-top: 10px;
}
#capss:hover{
  color: rgb(165, 162, 162);
  
}
input[type="checkbox"]{
  display: none;
}
@media (max-width: 1000px){
  nav{
    padding: 0 40px 0 50px;
  }

  .quick-actions {
    gap: 10px;
    margin: 0 10px;
  }

  .quick-btn {
    min-width: 60px;
    padding: 6px 8px;
  }

  .quick-btn i {
    font-size: 16px;
  }

  .quick-btn span {
    font-size: 10px;
  }

}
@media (max-width: 1385px) {


  #capss{
    display: block;

  }

  /* تصغير الأزرار في الشاشات الصغيرة */
  .quick-actions {
    gap: 8px;
    margin: 0 5px;
  }

  .quick-btn {
    min-width: 50px;
    padding: 5px 6px;
  }

  .quick-btn i {
    font-size: 14px;
    margin-bottom: 2px;
  }

  .quick-btn span {
    font-size: 9px;
  }
  
  .logo label{
    font-size: 20px;
  }
  #exit_btn{
    margin-top: 15px;
    font-size: 15px;
  }
  #click:checked ~ .menu-btn i:before{

    content: "\f00d";
    
  }

  nav ul{
    position: absolute;
    top: 80px;
    left: -100%;
    background: #111;
    height: fit-content;
    width: 100%;
    text-align: center;
    display: block;
    transition: all 0.3s ease;
    z-index:10;
  }
  #click:checked ~ ul{
    left: 0;
  }
  
  nav ul li{
    width: 100%;
    margin: 40px 0;
  }
  nav ul li a{
    width: 100%;
    margin-left: -100%;
    display: block;
    font-size: 20px;
    transition: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  #click:checked ~ ul li a{
    margin-left: 0px;
  }
 
  nav ul li a.active,
  nav ul li a:hover{
    background: none;
    color: cyan;
  }
}

/* للشاشات المتوسطة */
@media (max-width: 1024px) {
  .quick-actions {
    gap: 6px;
    margin: 0 10px;
  }

  .quick-btn {
    min-width: 60px;
    padding: 6px 8px;
  }

  .quick-btn span {
    font-size: 10px;
  }
}

/* للشاشات الصغيرة */
@media (max-width: 768px) {
  nav {
    padding: 8px 10px;
    flex-wrap: wrap;
    min-height: auto;
  }

  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 8px;
    margin: 10px 0;
    max-width: 100%;
    width: 100%;
    order: 2;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    overflow: visible;
  }

  .quick-btn {
    min-width: 70px;
    padding: 12px 8px;
    border-radius: 12px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 70px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .quick-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  .quick-btn span {
    font-size: 10px;
    font-weight: 600;
    margin-top: 4px;
    text-align: center;
    line-height: 1.1;
    display: block;
  }

  .quick-btn i {
    font-size: 18px;
    margin-bottom: 4px;
  }

  .separator {
    display: none;
  }

  .logo {
    order: 1;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .logo2 {
    order: 0;
  }
}

/* للشاشات الصغيرة جداً */
@media (max-width: 480px) {
  nav {
    padding: 5px;
  }

  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 6px;
    padding: 8px;
    margin: 8px 0;
  }

  .quick-btn {
    min-width: 60px;
    padding: 10px 6px;
    height: 60px;
  }

  .quick-btn span {
    font-size: 9px;
    font-weight: 600;
    line-height: 1;
  }

  .quick-btn i {
    font-size: 16px;
    margin-bottom: 3px;
  }

  .separator {
    width: 1px;
    height: 20px;
    margin: 0 2px;
  }

  .logo {
    margin-bottom: 8px;
  }

  .logo label {
    font-size: 14px;
  }
}

/* إضافة مساحة للمحتوى تحت الشريط الثابت */
body {
  margin-top: 90px;
  padding-top: 20px;
}

/* تصميم الشعار */
.logo2 {
  order: 0;
  margin-left: 10px;
}

.logo2 img {
  width: 60px;
  height: 60px;
}

/* تصميم منطقة المستخدم */
.logo {
  order: 2;
  display: flex;
  align-items: center;
  gap: 15px;
}

.grid{
  padding: 45px;
  margin-top: 20px;
  display: grid;
  grid-gap: 16px;


}
@media (min-width:768px){
  .grid{
   
    grid-template-columns:repeat(3,1fr);
  }

}
@media (min-width:1280px){
  .grid{
   
    grid-template-columns:repeat(3,0.5fr);
  }
  
}
.col:hover{
  box-shadow: 5px 0 0 0px rgba(35, 34, 34, 0.5);
}
.col{
  background-color: pink;
  margin-bottom: 16px;
  padding: 16px;
  align-items: center;
  background-color: #ebebeb;
  box-shadow: 5px 0 0 0px rgba(255, 0, 0, 0.5);
  border-radius: 20px;
  text-align: center;
  text-decoration: none;
  height: 150px;
  
}
.col a{
  text-decoration: none;
  color: rgba(255, 0, 0, 0.5);
  font-size: 35px;
}
.col a:hover{
  text-decoration: none;
  color: rgba(77, 77, 77, 0.5);
  font-size: 35px;
}
.col h3{
  padding-top: 10px;
  text-decoration: none;
  color: #1b1b1b;
}
.content{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: -1;
  width: 100%;
  padding: 0 30px;
  color: #1b1b1b;
}
.content div{
  font-size: 40px;
  font-weight: 700;
}

.cards{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px 0;
    flex-wrap: wrap;
    height: 100vh;
    

}
.container{
    max-width: 900px;
    margin: 0 auto;

}
.card{
    position: relative;
    text-align: center;
    width: 31%;
    color: rgb(0, 0, 0);
    border: 3px solid rgba(181, 181, 1818, 0.2);
    border-radius: 15px;
    
}

.box {
    padding-top: 30px;
    padding-left:4%;
  }
  /* Float four columns side by side */
  .column {
    float: right;
    width: 24%;
    padding: 10Px;
    
  }
  
  .row {margin: 0 -5px;}
  
  /* Clear floats after the columns */
  .row:after {
    content: "";
    display: table;
    clear: both;
  }
  
  /* Responsive columns */

  
  /* Style the counter cards */
  .cardt {
    background-color: #ebebeb;
    box-shadow: 5px 0 0 0px rgba(255, 0, 0, 0.5);
    box-sizing: unset;
    padding-right: 16px;
    text-align: center;
    border-radius: 20px;
    margin-right:15px;
    padding-top: 20px;
    padding-bottom: 20px;
   
    

    
  }
  .cardt:hover{
    box-shadow: 5px 0 0 0px rgba(2, 2, 2, 0.5);
  }
  
  .fa {font-size:60px;}
  .pag {
    
    font-family:"LamaSans-Medium",sans-serif;
    color: rgb(0, 0, 0);
    font-size: 25px;
  
  }

  .pag2 {
    font-family: "LamaSans-Medium", sans-serif;
  color: rgb(35, 35, 35);
  font-size: 25px;
  padding-left: 30%;
  padding-right: 30%;
  padding-top: 20px;
  border: 3px;

  text-align: center;
  box-shadow: 0 4px 8px 0 rgba(255, 115, 115, 0.2);
  box-sizing: border-box;
  border-radius: 7px;


  
  
  }
  .continICO{
    margin: 0 auto;
    height: 100vh;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
  
.box h3 {
    font-size: 40px;
  }
  .links :hover{
    color: black;
  }
  .links {
    color: black;
    
    
  }

  .table1{
    width: 50%;
    float: right;
  }
  .table2{
    width: 50%;
    float: left;
  }
  .datetime {
    font-size: 16px;
    padding: 24px;
    color: #ffffff;
    background: #444444;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    border-right: 10px #009578 solid;
    width: 400px;
    font-weight: 500;
    font-family: "Inter", sans-serif;
  }
  
  .time {
    font-size: 3em;
    color: #00ffcc;
  }
  
  .date {
    margin-top: 12px;
    font-size: 1.75em;
  }

  .divicon{
    display: grid;
    height: 10%;
    width: 100%;
    place-items: center;
    }
.wrappericon .button{
  display: inline-block;
  height: 60px;
  width: 60px;
  margin: 0 5px;
  overflow: hidden;
  background: #fff;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0px 10px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease-out;
  text-decoration: none;
}
.wrappericon .button:hover{
  width: 200px;
}
.wrappericon .button .icon{
  display: inline-block;
  position:relative;
  height: 60px;
  width: 60px;
  text-align: center;
  border-radius: 50px;
  box-sizing: border-box;
  line-height: 60px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:nth-child(1):hover .icon{
  background: #4267B2;
}
.wrappericon .button:nth-child(2):hover .icon{
  background: #1df236;
}
.wrappericon .button:nth-child(3):hover .icon{
  background: #E1306C;
}

.wrappericon .button .icon image{
  width: 20px;
  height: 20px;
  font-size: 25px;
  line-height: 60px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:hover .icon i{
  color: #fff;
}
.wrappericon .button span{
  font-size: 20px;
  font-weight: 500;
  line-height: 60px;
  margin-left: 10px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:nth-child(1) span{
  color: #4267B2;
}
.wrappericon .button:nth-child(2) span{
  color: #00b636;
}
.wrappericon .button:nth-child(3) span{
  color: #E1306C;
}
  .continC{
    width: 350px;
    height: 250PX;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: 15%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .continC input{
    text-align: center;
    width:320px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(0, 0, 0);
  }
  .continC button{
    margin-top: 20px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
  }
  .continC button:hover{
    background-color: #E1306C;
    color: rgb(255, 255, 255);
  }
  .continC label{
    margin-top: 2px;
  }
  .contin_employ_admin{
    width: 350px;
    height: fit-content;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: 1%;
    display: block;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .contin_employ_admin input{
    text-align: center;
    width:320px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(0, 0, 0);
  }
  .contin_employ_admin button{
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
    margin-top: -40px;
  }
  .contin_employ_admin button:hover{
    background-color: #ff9f55;
    color: rgb(255, 255, 255);
  }
  .contin_employ_admin label{
    padding-top: 2px;
    margin-top: 2px;
  }
  .contin_employ_admin  div{
    display: block;
    margin-top: 20px;
    padding-top: 10px;
  }
  .contin_employ_admin #selc{
   
    margin-top: 20px;
    margin-right: 25px;
    margin-bottom: 10px;
  }


  .g12{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #39a032;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
  }

  .g13{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #ec7666;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
    
    
  }
  .g14{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #66b0ec;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
    
  }

  .g16{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 20px;
    background-color: #f8cf68;
    color: rgb(0, 0, 0);
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 25px;
    margin: 0 auto;
    
    
  }
  #selc{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
  }
  #selc:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }
  #selc_deit{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
  }
  #selc_deit:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }
  #selc2{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
    margin-top: 15px;
  }
  #selc2:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }


  .contin{
    width: 350px;
    height: fit-content;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: -5%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .contin input{
    text-align: center;
    width:290px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(160, 160, 160);
    padding: 2px;
  
  }
  
  .contin button{
    margin-top: 30px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
  }
  .contin button:hover{
    margin-top: 30px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(255, 132, 75);
    color: rgb(0, 0, 0);
  }
  .contin label{
    margin-top: 2px;
    font-size: 18px;
  }

#catg {
  background-color: #3e3e3e;
  color: whitesmoke;
  border-radius: 4px;
  padding-top: 3px;
  padding-bottom: 3px;
  width: 350px;
}

#sex {
  background-color: #3e3e3e;
  color: whitesmoke;
  border-radius: 4px;
  margin-top: 10px;
  padding-bottom: 3px;
  width: 350px;
}


  .chek input{
    width: 25px;
    height: 20px;
    cursor: pointer;
    padding: 10px;

    
  } 
    .chek{
    align-items: center;
    justify-items: center;
    display: flex;
    margin-left: 27%;
    padding-left: 10px;
    }
    .chek label{
      align-items: flex-end;
      padding-left: 20px;
    }

    .chek2 input {
      width: 25px;
      height: 20px;
      cursor: pointer;
      padding: 10px;
    
    }
    
    .chek2 {
      align-items: center;
      justify-items: center;
      display: flex;
    }
    
    .chek2 label {
      align-items: flex-end;
      padding-left: 20px;
    }
  

      .still{
        background-color: #34a45676;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      .exp{
        background-color: #ff8c8a8e;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      
      .soon{
        background-color: #e7f36486;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      .table{
        align-items: center;
        text-align: center;
        border:2px;
        border-color: rgb(0, 0, 0);
        
        
       }
       .label_acout
       {
        position: relative;
        margin: 0 auto;
        margin-top: -100%;
       }
       .search{
        float: right;
        margin-top: 25px;
        margin-right: 26px;
        margin-bottom: 3px;
        position: relative;
        z-index: 1;

       }
       .search input{
       margin: 10px;
       width: 320px;
       text-align: center;
       border-radius: 3px;

       }
       .search a{
        font-size: 15px;
         margin: 4px;
         text-decoration: none;
         color: whitesmoke;
       }

       .search_ac{
        float: right;
        padding-top: 10px;
        padding-bottom: 15px;
        padding-right: 20px;
        border-radius: 10px;
        padding-left: 15px;
        margin-bottom: 3px;
        background-color: #1b1b1b71;
        position: relative;
        z-index: 1;
        

       }
       .search_ac input{
       margin-right: 10px;
       width: fixed;
       text-align: center;
       border-radius: 3px;

       }
       .search_ac a{
        font-size: 15px;
         margin: 4px;
         text-decoration: none;
         color: whitesmoke;
       }
       .search_ac label{
        font-size: 15px;
         margin-right: 10px;
        
        padding-right: 15px;
        padding: 5px;
        border-radius: 10px;
        color: white;
       }
       
      .table.active{
        filter: blur(200px);
      }
     
       .table tbody{
        font-size: 18px;
        font-weight:50;
        background-color: #ffffff71;
        
        
      }
      .table thead{
        font-size: 19px;
        font-weight:50;
        background-color: #000000c6;
        border: 2px;
        align-items: center;
        color: whitesmoke;
      }
      .table a{
       text-decoration: none;
      }
       #edit_bnt:hover{
        background-color: rgb(38, 41, 43);
       }
       #reomve_btn:hover{
        background-color: rgb(255, 111, 89);
       }
       #renew_btn:hover{
        background-color: rgb(27, 198, 255);
       }

       .nof1{
        font-family:"LamaSans-Medium",sans-serif ;
        text-align: center;
        font-size: 20px;
        background-color: #ff6060;
        color: rgb(0, 0, 0);
        margin-left: 10px;
        padding: 15px; 
        width: fit-content;
        margin-top: 10px;
        border-radius: 20px;
        margin-bottom: -60px;
      }
      .noif2{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ff2222a5;
        padding: 15px; 
        border-radius: 10px;
      
        
      }
      .addon{
        justify-content: center;
        align-items: center;
        font-family:"LamaSans-Medium",sans-serif ;
        text-align: center;
        font-size: 22px;
        background-color: #60e4ffa5;
        color: rgb(27, 27, 27);
        margin-left: 10px;
        padding: 15px; 
        height: 100%;
        padding-top: 25px;
        padding-bottom: 30px;
        width: fit-content;
        margin-top: 0;
        border-radius: 20px;
        margin-bottom: -60px;
      }
      .less{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ff7373a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .aades{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73ceffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .aades2{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73ceffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: 2px;
       
      }
      .aades-note{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ffcd57a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .less-note{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ffcd57a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .acount{
       
        font-family:"LamaSans-Medium",sans-serif ;
        float: left;
        position: relative;
        font-size: 22px;
        background-color: #ff7373a5;
        color: rgb(27, 27, 27);
        margin-left: -5px;
        padding: 15px; 
        margin-right: 15px;
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top: 82px;
        border-radius: 10px;
        margin-bottom: auto;
        padding-left: 15px;
      }
      .acount2{
       
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73e8ffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
        
      }

      
      .nofi3{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #22aeffa5;
        padding: 15px; 
        border-radius: 10px;
      }

      .contin_depit{
        width: 350px;
        height: fit-content;
        background-color: #ececec;
        box-shadow: 0 5px 5px rgb(255, 151, 122);
        margin: 0 auto;
        text-align: center;
        margin-top: 1%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        border-radius: 40px;
        margin-bottom: 20px;
        padding: 0;
      }
      .contin_depit input{
        text-align: center;
        width:320px;
        border-radius: 7px;
        justify-content:space-between;
        border-color: rgb(0, 0, 0);
      }
      .contin_depit button{
        margin-top: 20px;
        font-size: large;
        border-width: 2px;
        background-color: rgb(79, 84, 79);
        color: #ddd;
      }
      .contin_depit button:hover{
        background-color: #ff9f55;
        color: rgb(255, 255, 255);
      }
      .contin_depit label{
        padding-top: 2px;
        margin-top: 2px;
      }

      .contin_user{
        width: 350px;
        height: fit-content;
        background-color: #ececec;
        box-shadow: 0 5px 5px rgb(255, 151, 122);
        margin: 0 auto;
        text-align: center;
        margin-top: 1%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        border-radius: 40px;
        margin-bottom: 20px;
        padding: 0;
      }
      .contin_user input{
        text-align: center;
        width:320px;
        border-radius: 7px;
        justify-content:space-between;
        border-color: rgb(0, 0, 0);
      }
      .contin_user button{
        margin-top: 20px;
        font-size: large;
        border-width: 2px;
        background-color: rgb(79, 84, 79);
        color: #ddd;
      }
      .contin_user button:hover{
        background-color: #ff9f55;
        color: rgb(255, 255, 255);
      }
      .contin_user label{
        padding-top: 2px;
        margin-top: 2px;
      }
      .contin_user div{
        display: flex;
        padding: 10px;
      }
      #selc{
        margin-top: -10px;
        margin-left: 37px;
      }
      #selc{
        margin-top: -10px;
        margin-left: 37px;
      }
    

    
      .logo2 img{
        float:left;
        width:90px;
        height: 90px;
        margin-left: -55px;
       
       
      
        }

        .footer_p{
          text-align: center;
          align-items: center;
          justify-content: center;
          padding-top: 40px;
         margin: 0 auto;
         font-size: 18px;
         color: #b2b2b2;
         
        }

        .continICO{
          margin: 0 auto;
          height: 100vh;
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;
        }


#textarea{
          width: 320px;
          height: 120px;
          text-align: right;
          padding-inline: 5px;
          overflow: auto;
          resize: none;
          text-align:center;
      
}


.rect{
  width: 250px;
  height: 250px;
  background-color: #1f2227;
  position: absolute;
  margin: 0 auto;
  margin-top: 10px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
  box-shadow: 0 0 30px #1f2227a1;
  margin-bottom: -20px;
}

.cir{
  width: 220px;
  height: 220px;
  background: linear-gradient(to right,#fa6a52,#ff2058);
  display:flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;

}
.inner_cir{
  width: 200px;
  height: 200px;
  background: black url(anlog.png);
  border-radius: 50%;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;


}
.center{
  width: 20px;
  height: 20px;
  background:linear-gradient(to right,rgb(43, 42, 42),rgb(38, 37, 37));
  border-radius: 50%;
  z-index: 1;
  position:absolute;
}
.brand{
  position: relative;
  top: -38px;
  left: 1%;
  font-size: 13px;
  font-weight: bold;
  border: 3px;
  color:rgb(255, 34, 0);
  text-shadow: 0 2px 2px rgba(0, 0, 0, 0.384);
  font-family: Arial, Helvetica, sans-serif;


}
#hr{
  width: 120px;
  height: 120px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#min{
  width: 130px;
  height: 130px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#sec{
  width: 150px;
  height: 150px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#hr::before{
  content: "";
  width: 8px;
  height: 70px;
  background-color: rgba(0, 0, 0, 0.682);
  position: absolute;
  top:-15;
  border-radius: 3px;
}

#min::before{
  content: "";
  width: 5px;
  height: 70px;
  background-color: rgba(255, 0, 0, 0.682);
  position: absolute;
  top:-25;
  border-radius: 3px;
}
#sec::before{
  content: "";
  width: 2px;
  height: 100px;
  background-color: rgba(234, 77, 4, 0.682);
  position: absolute;
  top:-25;
  border-radius: 3px;
}



.wrapper{
  width: 420px;
  padding: 40px 20px;
  top: 9%;
  position: relative;
  right: 0;
  
}
#show-toast{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-left: 8px solid rgb(255, 149, 0);;
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(500px);
  transition: 1s;
}
.container-1,.container-2{
  align-self: center;
}
.container-1 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #ff9500;
}
.container-2 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
}
.container-2 p:last-child{
  font-size: 15px;
  color: #656565;
  font-weight: 400;
}


.wrapper2{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast2{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast2{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-11,.container-22{
  align-self: center;
}
.container-11 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-22 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-22 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}




.wrapper3{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast3{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast3{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-33,.container-22{
  align-self: center;
}
.container-33 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-33 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-33 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}

.div-tosar{
  margin: 0;
  padding: 0;
}

.wrapper4{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast4{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast4{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-44,.container-22{
  align-self: center;
}
.container-44 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-44 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-44 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}
/*التحقق من الحذف بوب نافذه  */

section {
  position: relative;
  height: 100%;
  width: 100%;
  background: #e3f2fd;
}
.buttons-method {
  display: flex;
  flex-direction: row;
  padding: 20px 10px;
  margin-top: 15px;
}
.close-btn,.remove-btn {
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  padding: 14px 33px;
  border: none;
  background: #f44040;
  border-radius: 6px;
  cursor: pointer;
}
button:hover {
  background-color: #8f8f8f;
}
button.show-modal,
.modal-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}
section.active .show-modal {
  display: none;
}
.overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  pointer-events: none;
}
section.active .overlay {
  opacity: 1;
  pointer-events: auto;
}
.modal-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 380px;
  width: 100%;
  padding: 30px 20px;
  border-radius: 24px;
  background-color: #fff;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%) scale(1.2);
}
section.active .modal-box {
  opacity: 1;
  pointer-events: auto;
  transform: translate(-50%, -50%) scale(1);
  position: fixed;
}
.modal-box i {
  font-size: 70px;
  color: #4070f4;
}
.modal-box h2 {
  margin-top: 20px;
  font-size: 25px;
  font-weight: 500;
  color: #333;
}
.modal-box h3 {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  text-align: center;
}
.modal-box .buttons {
  margin-top: 25px;
}
.modal-box button {
  font-size: 14px;
  padding: 6px 12px;
  margin: 0 10px;
}

div#Table_filter {
  padding: 15px;
}
div#Table_length {
  padding: 15px;
  
}

.attandes{
  padding: 5px;
}
.attandes input{
  display: inline-grid;
  padding: 5px;
  margin:0px 15px 0;
  width: 20px;
  height: 20px;
}
.attandes label{
  margin-bottom: 5px;
}

button#save {
  margin-right: -1px;
}
button.btn.btn-secondary.text-light {
  margin-right: -16px;
}

.p_stat{
  color: red;
}

.p_stat2{
  color: rgb(7, 162, 61);
}

.p_stat3{
  color: rgb(228, 191, 3);
}
svg#reomve{
  color: #ff00008f;
  cursor: pointer;
}
svg#reomve:hover{
  color: #5553538f;
}
button#reomve_att{
  list-style: none;
  border: none;
  background: none;
}
button#reomve_att:hover{
  list-style: none;
  border: none;
  background: none;
}
button#reomve_att:checked{
  list-style: none;
  border: none;
  background: none;
}

/* تحسينات إضافية للأجهزة المحمولة */
@media (max-width: 768px) {
  /* تحسين الشريط للهواتف */
  nav {
    padding: 5px 10px;
    height: 70px;
  }

  body {
    margin-top: 70px;
  }

  /* تحسين التمرير الأفقي */
  .quick-actions {
    overflow-x: auto;
    overflow-y: visible;
    gap: 4px;
    margin: 0 5px;
    padding: 2px 0;
  }

  /* تحسين اللمس */
  .quick-btn {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* تحسين النص */
  .quick-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  /* تحسين الشعار */
  .logo2 img {
    width: 40px;
    height: 40px;
  }

  /* تحسين زر الخروج */
  .logo form button {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 8px;
  }

  .logo label {
    font-size: 12px;
    margin-bottom: 5px;
  }
}

/* تحسينات للشاشات العريضة جداً */
@media (max-width: 360px) {
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    padding: 6px;
  }

  .quick-btn {
    min-width: 50px;
    height: 50px;
    padding: 8px 4px;
  }

  .quick-btn span {
    font-size: 8px;
  }

  .quick-btn i {
    font-size: 14px;
  }
}

/* تحسين الأداء للموبايل */
@media (max-width: 768px) {
  .quick-btn:before {
    display: none; /* إزالة التأثيرات المتحركة في الموبايل */
  }

  .quick-btn:hover {
    transform: none; /* إزالة التحويلات في الموبايل */
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
  }

  .quick-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* تحسين ترتيب العناصر في الموبايل */
  nav {
    flex-direction: column;
    align-items: stretch;
    padding: 10px;
  }

  .logo {
    order: 1;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
  }

  .logo2 {
    order: 0;
    margin-bottom: 10px;
    text-align: center;
  }

  .logo2 img {
    width: 50px;
    height: 50px;
  }

  .quick-actions {
    order: 2;
  }

  /* تحسين زر تسجيل الخروج */
  .logo form {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
    justify-content: space-between;
  }

  .logo form button {
    padding: 8px 15px;
    font-size: 12px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
  }

  .logo form label {
    font-size: 12px;
    color: white;
    font-weight: 600;
    text-align: right;
    flex: 1;
  }
}

