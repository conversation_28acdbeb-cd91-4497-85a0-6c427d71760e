<?php
header('Content-Type: text/html; charset=UTF-8');
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 لعبة الحساب - أكاديمية كيدز</title>
    <style>
        body {
            font-family: 'Comic Sans MS', Arial, sans-serif;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            margin: 0;
            padding: 20px;
            text-align: center;
            direction: rtl;
            min-height: 100vh;
        }
        
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .game-title {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        
        .number-display {
            font-size: 3rem;
            color: #e74c3c;
            font-weight: bold;
            margin: 1rem;
        }
        
        .score {
            background: #27ae60;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: bold;
            margin: 1rem;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            font-weight: bold;
            display: inline-block;
            margin-top: 1rem;
        }

        @keyframes messageSlide {
            0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">🎮 لعبة الحساب السريع</h1>
        <div class="game-area" id="gameArea">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🎯</div>
            <h3>اختر الإجابة الصحيحة للمسألة الحسابية</h3>
            <div class="number-display" id="question">2 + 3 = ?</div>
            <div>
                <button class="btn" onclick="checkAnswer(5)">5</button>
                <button class="btn" onclick="checkAnswer(6)">6</button>
                <button class="btn" onclick="checkAnswer(4)">4</button>
            </div>
            <div class="score" id="score">النقاط: 0</div>
        </div>
        <a href="games.php" class="back-btn">العودة للألعاب</a>
    </div>
    
    <script>
        let score = 0;
        let currentAnswer = 5;
        
        function generateQuestion() {
            const num1 = Math.floor(Math.random() * 10) + 1;
            const num2 = Math.floor(Math.random() * 10) + 1;
            const operation = Math.random() > 0.5 ? '+' : '-';
            
            if (operation === '+') {
                currentAnswer = num1 + num2;
                document.getElementById('question').textContent = num1 + ' + ' + num2 + ' = ?';
            } else {
                if (num1 >= num2) {
                    currentAnswer = num1 - num2;
                    document.getElementById('question').textContent = num1 + ' - ' + num2 + ' = ?';
                } else {
                    currentAnswer = num2 - num1;
                    document.getElementById('question').textContent = num2 + ' - ' + num1 + ' = ?';
                }
            }
            
            // إنشاء خيارات الإجابة
            const buttons = document.querySelectorAll('.btn:not(.back-btn)');
            const correctIndex = Math.floor(Math.random() * 3);
            
            buttons.forEach((btn, index) => {
                if (index === correctIndex) {
                    btn.textContent = currentAnswer;
                    btn.onclick = () => checkAnswer(currentAnswer);
                } else {
                    let wrongAnswer;
                    do {
                        wrongAnswer = currentAnswer + (Math.random() > 0.5 ? 1 : -1) * (Math.floor(Math.random() * 5) + 1);
                    } while (wrongAnswer < 0 || wrongAnswer === currentAnswer);
                    
                    btn.textContent = wrongAnswer;
                    btn.onclick = () => checkAnswer(wrongAnswer);
                }
            });
        }
        
        function checkAnswer(answer) {
            const gameArea = document.getElementById('gameArea');

            if (answer === currentAnswer) {
                score += 10;
                document.getElementById('score').textContent = 'النقاط: ' + score;
                gameArea.style.background = '#d4edda';

                // تأثير النجاح
                showMessage('✅ صحيح! أحسنت!', '#27ae60');

                setTimeout(() => {
                    gameArea.style.background = '#f8f9fa';
                    generateQuestion();
                }, 1500);
            } else {
                gameArea.style.background = '#f8d7da';
                showMessage('❌ خطأ! الإجابة الصحيحة هي: ' + currentAnswer, '#e74c3c');

                setTimeout(() => {
                    gameArea.style.background = '#f8f9fa';
                }, 1500);
            }
        }

        function showMessage(text, color) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                color: ${color};
                padding: 1rem 2rem;
                border-radius: 10px;
                font-size: 1.2rem;
                font-weight: bold;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 1000;
                animation: messageSlide 0.5s ease;
            `;
            messageDiv.textContent = text;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 1500);
        }
        
        // بدء اللعبة
        generateQuestion();
    </script>
</body>
</html>
