<?php
    if(isset($_POST['myInput']))
    {
      if($_POST['acout_type']=='ايرادات'){
      
      $dates=$_POST['dates'];
      $datee=$_POST['datee'];
      $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE  stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
      $query_run=mysqli_query($con,$query);
      if(mysqli_num_rows($query_run)>0){

       
        ?>            <table class="table">
        <thead>
          <tr>
            <th scope="col"> مستخدم الحضانة </th>
            <th scope="col">تاريخ  التسجيل</th>
            <th scope="col">قيمة الاشتراك</th>
            <th scope="col">اسم  الطالب</th>
            <th scope="col">رقم   الوصل </th>
            
          </tr>
        </thead>
        <tbody>
          <?php
          $count=0;
         foreach($query_run as $items){
          

          $count+=$items['cash_stud'];

          ?>

          <td><?= $items['user_name'];?></td>
          <td><?= $items['datein'];?></td>
          <td> IQD <?= number_format($items['cash_stud']);?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_pay'];?></td>
          
         </tbody>
 
 
        <?php
 

        }
        echo "<script>Toastincoming(".$count.")</script>";
       
      }else{
       
        echo "<script>Nocoming()</script>";
      }
    }
       if($_POST['acout_type']=='مصروفات'){
        $dates=$_POST['dates'];
        $datee=$_POST['datee'];
        $query="SELECT * FROM users_tb,depit_tb WHERE  depit_tb.userID=users_tb.id_user AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'";
        $query_run=mysqli_query($con,$query);
        if(mysqli_num_rows($query_run)>0){
          ?>            <table class="table">
          <thead>
            <tr>
              <th scope="col"> مستخدم الحضانة </th>
              <th scope="col"> التاريخ لعملية المصروفات</th>
              <th scope="col">قيمة المصروفات</th>
              <th scope="col"> وصف المصروفات </th>
              
            </tr>
          </thead>
          <tbody>
            <?php
            $count2=0;
          foreach($query_run as $items){
            $count2+=$items['depit_cash'];
            ?>
  
            <td><?= $items['user_name'];?></td>
            <td><?= $items['depit_date'];?></td>
            <td> IQD <?= number_format($items['depit_cash']);?></td>
            <td><?= $items['depit_note'];?></td>
            
           </tbody>
   
   
          <?php
      }
      echo "<script>ToastDepits(".$count2.")</script>";
      }else{
       
        echo "<script>nodata()</script>";

      }
    }
  }
      
    
    ?>