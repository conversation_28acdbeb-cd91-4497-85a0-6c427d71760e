<?php
// اختبار الترميز العربي
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');

include "addon/dbcon.php";
mysqli_set_charset($con, "utf8mb4");

// اختبار إدخال نص عربي
if (isset($_POST['test_text'])) {
    $text = mysqli_real_escape_string($con, $_POST['test_text']);
    
    // إنشاء جدول اختبار
    $create_test = "CREATE TABLE IF NOT EXISTS test_encoding (
        id INT AUTO_INCREMENT PRIMARY KEY,
        text_content TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    mysqli_query($con, $create_test);
    
    // إدخال النص
    $insert = "INSERT INTO test_encoding (text_content) VALUES ('$text')";
    mysqli_query($con, $insert);
    
    echo "<h3>تم حفظ النص: $text</h3>";
}

// عرض النصوص المحفوظة
$result = mysqli_query($con, "SELECT * FROM test_encoding ORDER BY id DESC LIMIT 5");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>اختبار الترميز العربي</title>
</head>
<body>
    <h1>اختبار الترميز العربي</h1>
    
    <form method="POST">
        <label>اكتب نص عربي:</label><br>
        <input type="text" name="test_text" placeholder="اكتب هنا..." style="width: 300px; padding: 10px;">
        <button type="submit">حفظ</button>
    </form>
    
    <h2>النصوص المحفوظة:</h2>
    <?php if ($result && mysqli_num_rows($result) > 0): ?>
        <ul>
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <li><?php echo htmlspecialchars($row['text_content']); ?> - <?php echo $row['created_at']; ?></li>
            <?php endwhile; ?>
        </ul>
    <?php else: ?>
        <p>لا توجد نصوص محفوظة</p>
    <?php endif; ?>
    
    <a href="student_app_control.php">العودة للوحة التحكم</a>
</body>
</html>
