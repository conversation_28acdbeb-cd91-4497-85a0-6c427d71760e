<!-- نافذة الإشعارات المنبثقة -->
<div class="notifications-modal" id="notificationsModal">
    <div class="notifications-modal-content">
        <div class="notifications-modal-header">
            <h3><i class="fas fa-bell"></i> الإشعارات</h3>
            <div class="notifications-modal-actions">
                <button class="mark-all-read-btn" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                </button>
                <button class="close-modal-btn" onclick="closeNotificationsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="notifications-modal-body" id="notificationsModalBody">
            <div class="loading-notifications">
                <i class="fas fa-spinner fa-spin"></i>
                جاري التحميل...
            </div>
        </div>
        
        <div class="notifications-modal-footer">
            <button class="load-more-btn" onclick="loadMoreNotifications()" id="loadMoreBtn" style="display: none;">
                <i class="fas fa-plus"></i> تحميل المزيد
            </button>
        </div>
    </div>
</div>

<style>
/* أنماط زر الإشعارات في الشريط الجانبي */
.notifications-nav-item {
    position: relative;
}

.notifications-nav-link {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.notification-badge-nav {
    position: absolute;
    top: -5px;
    right: 15px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    animation: pulse 2s infinite;
    border: 2px solid white;
    z-index: 10;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* أنماط النافذة المنبثقة */
.notifications-modal {
    display: none;
    position: fixed;
    z-index: 999999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.notifications-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.notifications-modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.notifications-modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notifications-modal-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.mark-all-read-btn, .close-modal-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.mark-all-read-btn:hover, .close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.notifications-modal-body {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: linear-gradient(90deg, #e3f2fd 0%, #ffffff 100%);
    border-left: 4px solid #2196f3;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 15px;
    width: 10px;
    height: 10px;
    background: #2196f3;
    border-radius: 50%;
    transform: translateY(-50%);
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-message {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.notification-time {
    color: #999;
    font-size: 0.8rem;
}

.notification-type-icon {
    width: 20px;
    text-align: center;
}

.type-leave_request { color: #ff9800; }
.type-need_request { color: #4caf50; }
.type-leave_response { color: #2196f3; }
.type-need_response { color: #9c27b0; }
.type-general { color: #607d8b; }

.loading-notifications {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

.empty-notifications {
    text-align: center;
    padding: 40px;
    color: #999;
}

.empty-notifications i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
    color: #ddd;
}

.notifications-modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.load-more-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.load-more-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* أنماط الترحيب المرفوع */
.top-user-section {
    position: absolute;
    top: 10px;
    right: 20px;
    z-index: 1000;
}

.user-form {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.welcome-label {
    color: white;
    font-weight: 600;
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

#exit_btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

#exit_btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .notifications-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .notifications-modal-header {
        padding: 15px;
    }
    
    .notifications-modal-header h3 {
        font-size: 1.1rem;
    }
    
    .notification-item {
        padding: 15px;
    }
    
    .top-user-section {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 10px;
    }
    
    .user-form {
        flex-direction: column;
        gap: 10px;
    }
    
    .welcome-label {
        font-size: 0.9rem;
    }
}

/* إخفاء الشارة عندما تكون فارغة */
.notification-badge-nav[style*="display: none"] {
    display: none !important;
}
</style>

<script>
let notificationsModalOpen = false;
let notificationsOffset = 0;
const notificationsLimit = 10;

// تحميل عدد الإشعارات غير المقروءة
function loadNotificationCount() {
    fetch('notifications_api.php?action=get_count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => console.error('Error:', error));
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadgeNav');
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
}

// تبديل عرض الإشعارات
function toggleNotifications(event) {
    event.preventDefault();
    const modal = document.getElementById('notificationsModal');
    
    if (!notificationsModalOpen) {
        modal.classList.add('show');
        notificationsModalOpen = true;
        loadNotifications(true);
        document.body.style.overflow = 'hidden'; // منع التمرير
    } else {
        closeNotificationsModal();
    }
}

// إغلاق نافذة الإشعارات
function closeNotificationsModal() {
    const modal = document.getElementById('notificationsModal');
    modal.classList.remove('show');
    notificationsModalOpen = false;
    document.body.style.overflow = 'auto'; // إعادة التمرير
}

// تحميل الإشعارات
function loadNotifications(reset = false) {
    if (reset) {
        notificationsOffset = 0;
        document.getElementById('notificationsModalBody').innerHTML = '<div class="loading-notifications"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    }
    
    fetch(`notifications_api.php?action=get_notifications&limit=${notificationsLimit}&offset=${notificationsOffset}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications, reset);
                notificationsOffset += notificationsLimit;
                
                // إظهار/إخفاء زر تحميل المزيد
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (data.notifications.length === notificationsLimit) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error:', error));
}

// عرض الإشعارات
function displayNotifications(notifications, reset = false) {
    const container = document.getElementById('notificationsModalBody');
    
    if (reset) {
        container.innerHTML = '';
    }
    
    if (notifications.length === 0 && reset) {
        container.innerHTML = `
            <div class="empty-notifications">
                <i class="fas fa-bell-slash"></i>
                <div>لا توجد إشعارات</div>
            </div>
        `;
        return;
    }
    
    notifications.forEach(notification => {
        const item = createNotificationItem(notification);
        container.appendChild(item);
    });
}

// إنشاء عنصر إشعار
function createNotificationItem(notification) {
    const item = document.createElement('div');
    item.className = `notification-item ${notification.is_read == 0 ? 'unread' : ''}`;
    item.onclick = () => markNotificationAsRead(notification.id);
    
    const typeIcons = {
        'leave_request': 'fa-calendar-alt',
        'need_request': 'fa-clipboard-list',
        'leave_response': 'fa-reply',
        'need_response': 'fa-check-circle',
        'general': 'fa-info-circle'
    };
    
    item.innerHTML = `
        <div class="notification-title">
            <i class="fas ${typeIcons[notification.type]} notification-type-icon type-${notification.type}"></i>
            ${notification.title}
        </div>
        <div class="notification-message">${notification.message}</div>
        <div class="notification-time">${notification.time_ago}</div>
    `;
    
    return item;
}

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId) {
    const formData = new FormData();
    formData.append('notification_id', notificationId);
    
    fetch('notifications_api.php?action=mark_read', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            // تحديث العنصر في الواجهة
            const items = document.querySelectorAll('.notification-item');
            items.forEach(item => {
                if (item.onclick.toString().includes(notificationId)) {
                    item.classList.remove('unread');
                }
            });
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('notifications_api.php?action=mark_all_read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotificationCount();
            loadNotifications(true);
        }
    })
    .catch(error => console.error('Error:', error));
}

// تحميل المزيد من الإشعارات
function loadMoreNotifications() {
    loadNotifications(false);
}

// إغلاق النافذة عند النقر على الخلفية
document.addEventListener('click', function(event) {
    const modal = document.getElementById('notificationsModal');
    if (event.target === modal) {
        closeNotificationsModal();
    }
});

// إغلاق النافذة بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && notificationsModalOpen) {
        closeNotificationsModal();
    }
});

// تحميل عدد الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadNotificationCount();
    
    // تحديث العدد كل 30 ثانية
    setInterval(loadNotificationCount, 30000);
});
</script>
