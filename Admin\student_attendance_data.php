<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح', 'success' => false], JSON_UNESCAPED_UNICODE);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// التحقق من الاتصال
if (!$con) {
    http_response_code(500);
    echo json_encode(['error' => 'فشل في الاتصال بقاعدة البيانات', 'success' => false], JSON_UNESCAPED_UNICODE);
    exit();
}

// معالجة المعاملات
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$selected_student = isset($_GET['student']) ? $_GET['student'] : '0';
$selected_category = isset($_GET['category']) ? $_GET['category'] : 'all';

try {
    // جلب إجمالي عدد الطلاب
    $total_query = "SELECT COUNT(*) as total FROM stud_tb";
    $total_result = $con->query($total_query);

    if (!$total_result) {
        throw new Exception("خطأ في جلب عدد الطلاب: " . $con->error);
    }

    $total_count = $total_result->fetch_assoc()['total'];

    // جلب الطلاب مع بيانات الحضور من جدول stat
    $students_with_attendance = [];

    // بناء الاستعلام حسب الفلاتر
    $where_conditions = [];
    $params = [];
    $param_types = "";

    if ($selected_student != '0' && is_numeric($selected_student)) {
        $where_conditions[] = "s.id = ?";
        $params[] = intval($selected_student);
        $param_types .= "i";
    }

    if ($selected_category != 'all' && !empty($selected_category)) {
        $where_conditions[] = "s.catg = ?";
        $params[] = $selected_category;
        $param_types .= "s";
    }

    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

    // جلب الطلاب مع ربط المستخدم
    $students_query = "SELECT s.id, s.name, s.catg, s.p_name, u.user_name
                      FROM stud_tb s
                      LEFT JOIN users_tb u ON s.userID = u.id_user
                      $where_clause
                      ORDER BY s.name
                      LIMIT 100";

    $students_result = null;

    if (!empty($params)) {
        $stmt = $con->prepare($students_query);
        if (!$stmt) {
            throw new Exception("خطأ في إعداد الاستعلام: " . $con->error);
        }
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $students_result = $stmt->get_result();
    } else {
        $students_result = $con->query($students_query);
    }

    if (!$students_result) {
        throw new Exception("خطأ في تنفيذ استعلام الطلاب: " . $con->error);
    }

    if ($students_result->num_rows > 0) {
        while ($student = $students_result->fetch_assoc()) {
            // جلب بيانات الحضور من جدول stat لهذا الطالب في التاريخ المحدد
            $attendance_query = "SELECT stat_stud, data_stat
                                FROM stat
                                WHERE id_stud = ? AND DATE(data_stat) = ?
                                ORDER BY data_stat DESC
                                LIMIT 1";

            $att_stmt = $con->prepare($attendance_query);
            if ($att_stmt) {
                $att_stmt->bind_param("is", $student['id'], $selected_date);
                $att_stmt->execute();
                $attendance_result = $att_stmt->get_result();
                $attendance_data = $attendance_result->fetch_assoc();
            } else {
                $attendance_data = null;
            }

            // تحديد وقت الحضور بناءً على البيانات الموجودة
            $check_in_time = null;
            if ($attendance_data) {
                // استخراج الوقت من data_stat إذا كان يحتوي على وقت
                $datetime = $attendance_data['data_stat'];
                if (strpos($datetime, ' ') !== false) {
                    $check_in_time = date('H:i', strtotime($datetime));
                } else {
                    // وقت افتراضي بناءً على الحالة
                    switch($attendance_data['stat_stud']) {
                        case 'حاضر':
                            $check_in_time = '08:00';
                            break;
                        case 'متأخر':
                            $check_in_time = '09:15';
                            break;
                        default:
                            $check_in_time = 'لم يسجل';
                    }
                }
            }

            $students_with_attendance[] = [
                'id' => $student['id'],
                'name' => $student['name'] ?? 'غير محدد',
                'catg' => $student['catg'] ?? 'غير محدد',
                'p_name' => $student['p_name'] ?? 'غير محدد',
                'user_name' => $student['user_name'] ?? 'غير محدد',
                'attendance' => $attendance_data ? [
                    'status' => $attendance_data['stat_stud'],
                    'check_in_time' => $check_in_time,
                    'data_stat' => $attendance_data['data_stat']
                ] : null
            ];
        }
    }

    // التحقق من وجود جدول stat
    $attendance_table_exists = false;
    $check_attendance_table = $con->query("SHOW TABLES LIKE 'stat'");
    if ($check_attendance_table && $check_attendance_table->num_rows > 0) {
        $attendance_table_exists = true;
    }

    // إرجاع البيانات
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'students' => $students_with_attendance,
        'total_count' => $total_count,
        'selected_date' => $selected_date,
        'selected_student' => $selected_student,
        'selected_category' => $selected_category,
        'attendance_table_exists' => $attendance_table_exists,
        'debug_info' => [
            'query' => $students_query,
            'params' => $params,
            'students_found' => count($students_with_attendance)
        ],
        'success' => true
    ], JSON_UNESCAPED_UNICODE);

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في جلب البيانات',
        'details' => $e->getMessage(),
        'line' => $e->getLine(),
        'file' => basename($e->getFile()),
        'success' => false
    ], JSON_UNESCAPED_UNICODE);
}

// إغلاق الاتصال
if ($con) {
    $con->close();
}
?>
