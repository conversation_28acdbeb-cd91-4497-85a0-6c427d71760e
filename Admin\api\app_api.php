<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

include "../addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// دالة للاستجابة بـ JSON
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة للتحقق من صحة الطالب
function validateStudent($con, $student_id, $password = null) {
    $student_id = mysqli_real_escape_string($con, $student_id);
    $sql = "SELECT * FROM stud_tb WHERE id = '$student_id' OR email = '$student_id' OR phone = '$student_id'";
    
    if ($password) {
        $password = mysqli_real_escape_string($con, $password);
        $sql .= " AND password = '$password'";
    }
    
    $result = @mysqli_query($con, $sql);
    return $result && mysqli_num_rows($result) > 0 ? mysqli_fetch_assoc($result) : false;
}

// الحصول على نوع الطلب
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = $_GET['endpoint'] ?? '';

switch ($endpoint) {
    case 'login':
        if ($method == 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $student_id = $input['student_id'] ?? '';
            $password = $input['password'] ?? '';
            
            if (empty($student_id) || empty($password)) {
                jsonResponse(['success' => false, 'message' => 'يرجى إدخال جميع البيانات المطلوبة'], 400);
            }
            
            $student = validateStudent($con, $student_id, $password);
            if ($student) {
                // تحديث آخر دخول
                @mysqli_query($con, "UPDATE stud_tb SET last_login = NOW() WHERE id = {$student['id']}");
                
                jsonResponse([
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'student' => [
                        'id' => $student['id'],
                        'name' => $student['name'],
                        'email' => $student['email'],
                        'phone' => $student['phone'],
                        'status' => $student['status']
                    ]
                ]);
            } else {
                jsonResponse(['success' => false, 'message' => 'بيانات الدخول غير صحيحة'], 401);
            }
        }
        break;
        
    case 'news':
        if ($method == 'GET') {
            $limit = (int)($_GET['limit'] ?? 10);
            $offset = (int)($_GET['offset'] ?? 0);
            
            $sql = "SELECT * FROM news_announcements WHERE status = 'نشط' ORDER BY created_date DESC LIMIT $limit OFFSET $offset";
            $result = @mysqli_query($con, $sql);
            
            $news = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $news[] = [
                        'id' => $row['id'],
                        'title' => $row['title'],
                        'content' => $row['content'],
                        'type' => $row['type'],
                        'created_date' => $row['created_date']
                    ];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $news]);
        }
        break;
        
    case 'activities':
        if ($method == 'GET') {
            $limit = (int)($_GET['limit'] ?? 10);
            $offset = (int)($_GET['offset'] ?? 0);
            
            $sql = "SELECT * FROM app_activities WHERE status = 'active' ORDER BY activity_date DESC LIMIT $limit OFFSET $offset";
            $result = @mysqli_query($con, $sql);
            
            $activities = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $activities[] = [
                        'id' => $row['id'],
                        'title' => $row['title'],
                        'description' => $row['description'],
                        'activity_date' => $row['activity_date'],
                        'activity_time' => $row['activity_time'],
                        'location' => $row['location'],
                        'max_participants' => $row['max_participants'],
                        'current_participants' => $row['current_participants']
                    ];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $activities]);
        }
        break;
        
    case 'app_content':
        if ($method == 'GET') {
            $content_type = $_GET['type'] ?? '';
            $target_audience = $_GET['audience'] ?? 'all';
            
            $sql = "SELECT * FROM app_content WHERE is_active = 1";
            if ($content_type) {
                $content_type = mysqli_real_escape_string($con, $content_type);
                $sql .= " AND content_type = '$content_type'";
            }
            if ($target_audience != 'all') {
                $target_audience = mysqli_real_escape_string($con, $target_audience);
                $sql .= " AND (target_audience = '$target_audience' OR target_audience = 'all')";
            }
            $sql .= " ORDER BY order_index ASC, created_at DESC";
            
            $result = @mysqli_query($con, $sql);
            
            $content = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $content[] = [
                        'id' => $row['id'],
                        'content_type' => $row['content_type'],
                        'title' => $row['title'],
                        'content' => $row['content'],
                        'image_url' => $row['image_url'],
                        'video_url' => $row['video_url'],
                        'target_audience' => $row['target_audience'],
                        'created_at' => $row['created_at']
                    ];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $content]);
        }
        break;
        
    case 'app_sections':
        if ($method == 'GET') {
            $sql = "SELECT * FROM app_sections WHERE is_enabled = 1 ORDER BY order_index ASC";
            $result = @mysqli_query($con, $sql);
            
            $sections = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $sections[] = [
                        'id' => $row['id'],
                        'section_name' => $row['section_name'],
                        'section_title' => $row['section_title'],
                        'section_description' => $row['section_description'],
                        'icon' => $row['icon'],
                        'background_color' => $row['background_color'],
                        'order_index' => $row['order_index']
                    ];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $sections]);
        }
        break;
        
    case 'contact_info':
        if ($method == 'GET') {
            $sql = "SELECT * FROM contact_info";
            $result = @mysqli_query($con, $sql);
            
            $contact_info = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $contact_info[$row['info_key']] = $row['info_value'];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $contact_info]);
        }
        break;
        
    case 'send_contact_message':
        if ($method == 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $student_id = (int)($input['student_id'] ?? 0);
            $contact_type = mysqli_real_escape_string($con, $input['contact_type'] ?? 'general');
            $priority = mysqli_real_escape_string($con, $input['priority'] ?? 'general');
            $subject = mysqli_real_escape_string($con, $input['subject'] ?? '');
            $message = mysqli_real_escape_string($con, $input['message'] ?? '');
            
            if ($student_id <= 0 || empty($subject) || empty($message)) {
                jsonResponse(['success' => false, 'message' => 'يرجى ملء جميع البيانات المطلوبة'], 400);
            }
            
            // التحقق من وجود الطالب
            if (!validateStudent($con, $student_id)) {
                jsonResponse(['success' => false, 'message' => 'الطالب غير موجود'], 404);
            }
            
            $sql = "INSERT INTO contact_messages (student_id, contact_type, priority, subject, message, status, created_at) 
                    VALUES ($student_id, '$contact_type', '$priority', '$subject', '$message', 'pending', NOW())";
            
            if (@mysqli_query($con, $sql)) {
                jsonResponse(['success' => true, 'message' => 'تم إرسال الرسالة بنجاح']);
            } else {
                jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء إرسال الرسالة'], 500);
            }
        }
        break;
        
    case 'submit_leave_request':
        if ($method == 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $student_id = (int)($input['student_id'] ?? 0);
            $leave_type = mysqli_real_escape_string($con, $input['leave_type'] ?? '');
            $start_date = mysqli_real_escape_string($con, $input['start_date'] ?? '');
            $end_date = mysqli_real_escape_string($con, $input['end_date'] ?? '');
            $reason = mysqli_real_escape_string($con, $input['reason'] ?? '');
            
            if ($student_id <= 0 || empty($leave_type) || empty($start_date) || empty($end_date) || empty($reason)) {
                jsonResponse(['success' => false, 'message' => 'يرجى ملء جميع البيانات المطلوبة'], 400);
            }
            
            // التحقق من وجود الطالب
            if (!validateStudent($con, $student_id)) {
                jsonResponse(['success' => false, 'message' => 'الطالب غير موجود'], 404);
            }
            
            $sql = "INSERT INTO leave_requests (student_id, leave_type, start_date, end_date, reason, status, created_at) 
                    VALUES ($student_id, '$leave_type', '$start_date', '$end_date', '$reason', 'pending', NOW())";
            
            if (@mysqli_query($con, $sql)) {
                jsonResponse(['success' => true, 'message' => 'تم تقديم طلب الإجازة بنجاح']);
            } else {
                jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء تقديم الطلب'], 500);
            }
        }
        break;
        
    case 'student_notifications':
        if ($method == 'GET') {
            $student_id = (int)($_GET['student_id'] ?? 0);
            $limit = (int)($_GET['limit'] ?? 10);
            $offset = (int)($_GET['offset'] ?? 0);
            
            if ($student_id <= 0) {
                jsonResponse(['success' => false, 'message' => 'معرف الطالب مطلوب'], 400);
            }
            
            // جلب الإشعارات المرسلة للطالب
            $sql = "SELECT n.*, ns.read_at, ns.clicked_at 
                    FROM app_notifications n 
                    LEFT JOIN notification_stats ns ON n.id = ns.notification_id AND ns.student_id = $student_id
                    WHERE n.status = 'sent' AND (n.target_audience = 'all' OR n.target_audience = 'students')
                    ORDER BY n.sent_time DESC LIMIT $limit OFFSET $offset";
            
            $result = @mysqli_query($con, $sql);
            
            $notifications = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $notifications[] = [
                        'id' => $row['id'],
                        'title' => $row['title'],
                        'message' => $row['message'],
                        'notification_type' => $row['notification_type'],
                        'priority' => $row['priority'],
                        'image_url' => $row['image_url'],
                        'click_action' => $row['click_action'],
                        'sent_time' => $row['sent_time'],
                        'is_read' => !empty($row['read_at']),
                        'is_clicked' => !empty($row['clicked_at'])
                    ];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $notifications]);
        }
        break;
        
    case 'mark_notification_read':
        if ($method == 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $notification_id = (int)($input['notification_id'] ?? 0);
            $student_id = (int)($input['student_id'] ?? 0);
            
            if ($notification_id <= 0 || $student_id <= 0) {
                jsonResponse(['success' => false, 'message' => 'البيانات المطلوبة مفقودة'], 400);
            }
            
            $sql = "UPDATE notification_stats SET read_at = NOW() 
                    WHERE notification_id = $notification_id AND student_id = $student_id AND read_at IS NULL";
            
            if (@mysqli_query($con, $sql)) {
                jsonResponse(['success' => true, 'message' => 'تم تحديث حالة الإشعار']);
            } else {
                jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء التحديث'], 500);
            }
        }
        break;
        
    case 'app_settings':
        if ($method == 'GET') {
            $sql = "SELECT * FROM app_settings";
            $result = @mysqli_query($con, $sql);
            
            $settings = [];
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            }
            
            jsonResponse(['success' => true, 'data' => $settings]);
        }
        break;
        
    default:
        jsonResponse(['success' => false, 'message' => 'نقطة النهاية غير موجودة'], 404);
}
?>
