<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

echo "<h2>تطبيق تصميم الأزرار الأفقية على صفحات الأدمن</h2>";

// قائمة صفحات الأدمن
$admin_pages = [
    'statistics.php',
    'allstud.php',
    'new_stud.php',
    'addstud.php',
    'manage_needs.php',
    'manage_leaves.php',
    'allDepit.php',
    'info_depit.php',
    'expired_stud.php',
    'expried_soon.php',
    'hadanaStud.php',
    'rodaStud.php',
    'tamhediStud.php',
    'tadeery.php',
    'info_employ.php',
    'info_user.php',
    'acounting.php',
    'infost.php',
    'reports.php',
    'attandec.php',
    'about_us.php'
];

$updated_count = 0;
$errors = [];

echo "<h3>إضافة CSS للأزرار الأفقية:</h3>";

foreach ($admin_pages as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        
        // التحقق من وجود رابط CSS
        if (strpos($content, 'admin_navbar.css') === false) {
            // البحث عن </head> لإضافة CSS قبلها
            if (preg_match('/<\/head>/i', $content)) {
                $css_link = '    <link rel="stylesheet" href="css/admin_navbar.css">';
                $content = preg_replace('/(<\/head>)/i', "$css_link\n$1", $content);
                
                if (file_put_contents($page, $content)) {
                    echo "<p style='color: green;'>✅ تم إضافة CSS لـ $page</p>";
                    $updated_count++;
                } else {
                    $errors[] = "فشل في تحديث $page";
                }
            } else {
                $errors[] = "لم يتم العثور على </head> في $page";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ CSS موجود مسبقاً في $page</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ $page غير موجود</p>";
    }
}

echo "<h3>ملخص التحديث:</h3>";
echo "<p>📊 تم تحديث <strong>$updated_count</strong> صفحة</p>";
echo "<p>🎨 تم إنشاء ملف CSS للأزرار الأفقية</p>";
echo "<p>🔄 تم تحويل الشريط الجانبي إلى أزرار أفقية</p>";

if (!empty($errors)) {
    echo "<h4>الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>❌ $error</p>";
    }
}

echo "<h3>التحديثات المطبقة:</h3>";
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>🎯 التغييرات الرئيسية:</h4>";
echo "<ul>";
echo "<li>✅ <strong>حذف الشريط الجانبي:</strong> إزالة القائمة المنسدلة القديمة</li>";
echo "<li>✅ <strong>أزرار أفقية:</strong> نفس تصميم صفحة المستخدمين</li>";
echo "<li>✅ <strong>ترتيب العناصر:</strong> الشعار → الأزرار → المستخدم</li>";
echo "<li>✅ <strong>زر الإشعارات:</strong> أول زر في الشريط مع شارة</li>";
echo "<li>✅ <strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>";
echo "</ul>";
echo "</div>";

echo "<h3>الأزرار المتوفرة:</h3>";
$buttons = [
    'الإشعارات' => ['fa-bell', '#fd7e14'],
    'الرئيسية' => ['fa-home', '#28a745'],
    'إضافة طالب' => ['fa-user-plus', '#667eea'],
    'الطلاب' => ['fa-graduation-cap', '#667eea'],
    'المصاريف' => ['fa-money-bill', '#667eea'],
    'الإحصائيات' => ['fa-chart-bar', '#667eea'],
    'الحسابات' => ['fa-calculator', '#667eea'],
    'المستخدمين' => ['fa-users', '#667eea'],
    'الموظفين' => ['fa-user-tie', '#667eea'],
    'التقارير' => ['fa-file-alt', '#667eea'],
    'حضور الطلاب' => ['fa-school', '#667eea'],
    'نسخ احتياطي' => ['fa-database', '#667eea'],
    'عنا' => ['fa-info-circle', '#667eea']
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 10px; margin: 20px 0;'>";
foreach ($buttons as $name => $info) {
    $icon = $info[0];
    $color = $info[1];
    echo "<div style='background: linear-gradient(135deg, $color 0%, #764ba2 100%); color: white; padding: 10px; border-radius: 20px; text-align: center; font-size: 0.9rem;'>";
    echo "<i class='fas $icon'></i> $name";
    echo "</div>";
}
echo "</div>";

echo "<h3>مقارنة التصميم:</h3>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
echo "<h4>❌ التصميم القديم:</h4>";
echo "<ul>";
echo "<li>شريط جانبي منسدل</li>";
echo "<li>أزرار عمودية مخفية</li>";
echo "<li>يحتاج نقرة لفتح القائمة</li>";
echo "<li>يأخذ مساحة من الشاشة</li>";
echo "<li>صعب الاستخدام على الهاتف</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px;'>";
echo "<h4>✅ التصميم الجديد:</h4>";
echo "<ul>";
echo "<li>أزرار أفقية ثابتة</li>";
echo "<li>جميع الأزرار مرئية</li>";
echo "<li>وصول مباشر بنقرة واحدة</li>";
echo "<li>استغلال أفضل للمساحة</li>";
echo "<li>سهل الاستخدام على جميع الأجهزة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h3>اختبار التصميم:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h4>📋 خطوات الاختبار:</h4>";
echo "<ol>";
echo "<li>قم بزيارة <a href='home.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li>تأكد من وجود الأزرار في الأعلى (وليس شريط جانبي)</li>";
echo "<li>اختبر النقر على كل زر للتأكد من عمله</li>";
echo "<li>جرب تصغير النافذة لاختبار التصميم المتجاوب</li>";
echo "<li>اختبر على الهاتف أو استخدم أدوات المطور (F12)</li>";
echo "</ol>";
echo "</div>";

echo "<h3>روابط الاختبار:</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";

$test_pages = [
    'home.php' => 'الرئيسية',
    'statistics.php' => 'الإحصائيات',
    'infost.php' => 'الطلاب',
    'addstud.php' => 'إضافة طالب',
    'info_depit.php' => 'المصاريف',
    'manage_needs.php' => 'إدارة الاحتياجات',
    'manage_leaves.php' => 'إدارة الإجازات',
    'info_employ.php' => 'الموظفين',
    'info_user.php' => 'المستخدمين'
];

foreach ($test_pages as $file => $name) {
    if (file_exists($file)) {
        echo "<a href='$file' target='_blank' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 20px; font-size: 0.9rem;'>$name</a>";
    } else {
        echo "<span style='background: #6c757d; color: white; padding: 8px 15px; border-radius: 20px; font-size: 0.9rem;'>$name (غير موجود)</span>";
    }
}
echo "</div>";

echo "<h3>معلومات تقنية:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.9rem;'>";
echo "<strong>ملف CSS الجديد:</strong> css/admin_navbar.css<br>";
echo "<strong>ملف الشريط:</strong> addon/topbar.php<br>";
echo "<strong>ارتفاع الشريط:</strong> 90px (كمبيوتر) / 70px (هاتف)<br>";
echo "<strong>موقع الشريط:</strong> position: fixed; top: 0;<br>";
echo "<strong>ترتيب العناصر:</strong> الشعار (order: 0) → الأزرار (order: 1) → المستخدم (order: 2)<br>";
echo "<strong>مساحة المحتوى:</strong> margin-top: 90px + padding-top: 20px<br>";
echo "</div>";

echo "<h3>نصائح للاستخدام:</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<ul>";
echo "<li>🔄 <strong>تحديث الصفحة:</strong> اضغط Ctrl+F5 لتحديث كامل</li>";
echo "<li>📱 <strong>اختبار الهاتف:</strong> استخدم أدوات المطور (F12) لمحاكاة الهاتف</li>";
echo "<li>🔔 <strong>الإشعارات:</strong> زر الإشعارات أول زر في الشريط</li>";
echo "<li>⚡ <strong>الأداء:</strong> الأزرار محسنة للسرعة والاستجابة</li>";
echo "<li>🎨 <strong>التخصيص:</strong> يمكن تعديل الألوان في ملف CSS</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='home.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 1.1rem; margin: 0 10px;'>🏠 اختبار الصفحة الرئيسية</a>";
echo "<a href='statistics.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 1.1rem; margin: 0 10px;'>📊 اختبار الإحصائيات</a>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 20px; background: #d4edda; border-radius: 10px; text-align: center;'>";
echo "<h4 style='color: #155724; margin: 0;'>🎉 تم تطبيق تصميم الأزرار الأفقية بنجاح!</h4>";
echo "<p style='color: #155724; margin: 10px 0 0 0;'>الآن صفحة الأدمن تستخدم نفس تصميم وترتيب أزرار المستخدمين</p>";
echo "</div>";
?>
