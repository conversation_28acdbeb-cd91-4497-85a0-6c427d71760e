<?php
session_start();

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location:../login.php");
    exit();
}

echo "<h1>اختبار بسيط</h1>";
echo "<p>الصفحة تعمل بشكل صحيح!</p>";
echo "<p>التاريخ: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الاتصال بقاعدة البيانات
include "addon/dbcon.php";

if ($con) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار بسيط
    $test_query = "SELECT COUNT(*) as count FROM users_tb WHERE role = 'User'";
    $test_result = mysqli_query($con, $test_query);
    
    if ($test_result) {
        $user_count = mysqli_fetch_assoc($test_result)['count'];
        echo "<p style='color: green;'>✅ عدد المستخدمين: $user_count</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . mysqli_error($con) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار بسيط</title>
</head>
<body>
    <h2>اختبار الصفحة</h2>
    <p><a href="home.php">العودة للرئيسية</a></p>
</body>
</html>
