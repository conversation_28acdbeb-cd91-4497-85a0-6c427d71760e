<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات طالب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <?php 
    $datenow=date('Y-m-d');
    if(isset($_GET['id'])):
    $id=$_GET['id'];
    $sql="SELECT stud_tb.*, stud_pay.*,
          COALESCE(stud_tb.health_status, 'لا توجد ملاحظات') as health_status,
          COALESCE(stud_tb.registration_status, 'تسجيل جديد') as registration_status
          FROM stud_tb,stud_pay WHERE id=$id AND stud_pay.id_stud=$id";
    $resul=mysqli_query($con,$sql);
    $row=mysqli_fetch_assoc($resul);
          $id=$row['id'];
          $id_note=$row['id_note'];
          $name=$row['name'];
          $age=$row['age'];
          $sex=$row['sex'];
          $catg=$row['catg'];
          $datein=$row['datein'];
          $p_name=$row['p_name'];
          $p_phone=$row['p_phone'];
          $loc=$row['loc'];
          $id_pay=$row['id_pay'];
          $cash_stud=$row['cash_stud'];
          $date_exp=$row['date_exp'];
          $health_status=$row['health_status'];
          $registration_status=$row['registration_status'];
    
?>
  <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <form method="POST">
 <div class='contin'>

 <div class='input-box'>
        <label for="id_note"> رقم الوصل <label>
        <input type="number" placeholder="  رقم وصل الاشتراك" name="id_pay" required value="<?php echo $row['id_pay'];?>">
        <label for="name">اسم الطالب <label>
        <input type="text" name="name" required value="<?php echo $row['name'];?>">
        <label for="age"> العمر <label>
        <input type="number" name="age" required value="<?php echo $row['age'];?>">
        <label for="sex" id='sex'> الجنس  </label>
        <div class="chek" required >
         <label for="">
          ذكر <input type="radio" name="sex" value="ذكر" id="email">
         </label>
         <label for="">
          انثى <input type="radio" name="sex" value="انثى" id="email" >
         </label>
        </div>
        <label for="catg2" id='catg'>صنف التسجيل </label>
        <div class="chek2" required>
        <label for="" <?php if($row['catg']=='روضة'){?> style="background-color: #808080a8;border-radius: 20px;"<?php }?>>
         روضة<input type="radio" name="catg" value="روضة" id="email">
         </label >
        <label for="" <?php if($row['catg']=='حضانة'){?> style="background-color: #808080a8;border-radius: 20px;"<?php }?>>
        حضانة <input type="radio" name="catg" value="حضانة" id=femail>
        </label > 
        <label for=""<?php if($row['catg']=='تحضيري'){?> style="background-color: #808080a8;border-radius: 20px;"<?php }?> >
         تحضيري <input type="radio" name="catg" value="تحضيري" id=femail >
        </label> 
        <label for="" <?php if($row['catg']=='تمهيدي'){?> style="background-color: #808080a8;border-radius: 20px;"<?php }?>>
        تمهيدي <input type="radio" name="catg" value="تمهيدي" id=femail>
        
        </label> 
        </div>
        <label for="loc" >موقع السكن   <label>
        <input type="text" placeholder="بلوك - عمارة " name="loc" required value="<?php echo $row['loc'];?>">
        <label for="p_name">اسم ولي الامر <label>
        <input type="text" placeholder=" الاسم كامل" name="p_name" required value="<?php echo $row['p_name'];?>">
        <label for="p_phone">رقم ولي الامر <label>
        <input type="text" placeholder=" 07xx-xxxx-xxxx " name="p_phone" maxlength="11" required value="<?php echo $row['p_phone'];?>">
        <label for="datein">تاريخ التسجيل <label>
        <input type="date"  name="datein" required value="<?php echo $row['datein'];?>" disabled> 
        <label for="cash_stud"> قيمة الاشتراك <label>
        <input type="number" placeholder=" المبلغ  IQD " name="cash_stud" required value="<?php echo $row['cash_stud'];?>">

        <label for="health_status">الحالة الصحية <label>
        <textarea name="health_status" placeholder="اكتب الحالة الصحية للطالب (أي أمراض أو حساسية أو ملاحظات طبية)" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;"><?php echo htmlspecialchars($health_status); ?></textarea>

        <label for="registration_status">حالة التسجيل <label>
        <select name="registration_status" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;">
            <option value="">اختر حالة التسجيل</option>
            <option value="تسجيل جديد" <?php echo ($registration_status == 'تسجيل جديد') ? 'selected' : ''; ?>>تسجيل جديد</option>
            <option value="مشترك" <?php echo ($registration_status == 'مشترك') ? 'selected' : ''; ?>>مشترك</option>
        </select>

        <div>
        <button class=btn name="addS" id="23">حفظ </button>
        </div>
       
        
        </div>

        <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
      
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
    if(icC=="fa fa-circle-check"){
      dispable ();
      setInterval(()=>{
        window.location.href="infost.php"
      },4700)
      
    }else{
    }
}
  </script>
    <script>
     function dispable () { 

        jQuery("#23").prop("disabled",true)
        jQuery(".contin").css("transition","3s")
        jQuery(".contin").css("opacity","0.0")
        
        
    };
</script>

  <?php
  $id=$_GET['id'];
      if(isset($_POST['addS'])){
        if(!isset($_POST['sex'])){
          $msg1=" ! انتبه ";
          $msg2="يرجى  اختيار  الجنس "; 
          $iconC="fa-solid fa-circle-info";
         echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
        }elseif(!isset($_POST['catg'])){
          $msg1=" ! انتبه ";
          $msg2="يجب  اختيار  صنف التسجيل "; 
          $iconC="fa-solid fa-circle-info";
         echo "<script>StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
        }else{
        $id_note=$row['id_note'];
        $name=$_POST['name'];
        $age=$_POST['age'];
        $sex=$_POST['sex'];
        $catg=$_POST['catg'];
        $datein=$row['datein'];
        $p_name=$_POST['p_name'];
        $p_phone=$_POST['p_phone'];
        $loc=$_POST['loc'];
        $id_pay=$_POST['id_pay'];
        $cash_stud=$_POST['cash_stud'];
        $health_status=$_POST['health_status'];
        $registration_status=$_POST['registration_status'];
        $date_exp=date('Y-m-d', strtotime($datein. ' + 30 days'));
        $userID=$_SESSION['user']->id_user;
        // تحديث جدول الطلاب
        $updateStud="UPDATE stud_tb SET id_note=$id_note,userID=$userID,name='$name',age='$age',sex='$sex',catg='$catg',datein='$datein',p_name='$p_name',p_phone='$p_phone',loc='$loc',health_status='$health_status',registration_status='$registration_status' WHERE id=$id";
        $resultStud=mysqli_query($con,$updateStud);

        // تحديث جدول المدفوعات
        $updatePay="UPDATE stud_pay SET date_exp='$date_exp',cash_stud='$cash_stud',id_pay='$id_pay' WHERE id_stud=$id";
        $resultPay=mysqli_query($con,$updatePay);

        $resul = $resultStud && $resultPay;
        if($resul){
          $msg1=" ! تمت ";
          $msg2="تم التعديل على بيانات الطالب بنجاح";
          $iconC="fa fa-circle-check";
          echo "<script>StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
        } else {
          $msg1=" ! خطأ ";
          $msg2="حدث خطأ أثناء التعديل، يرجى المحاولة مرة أخرى";
          $iconC="fa-solid fa-circle-exclamation";
          echo "<script>StudToast('8px solid rgb(220, 53, 69)','#dc3545','$msg1','$msg2','$iconC')</script>";
        }
      }
    }
  endif;
      ?>
   </body>
   
</html>