<?php

session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        // المدير مصرح له
    } else {
        echo "غير مصرح";
        exit();
    }
} else {
    echo "يجب تسجيل الدخول";
    exit();
}

include "dbcon.php";

// معالجة البيانات المرسلة عبر POST أو GET
$input = isset($_POST['myinput']) ? $_POST['myinput'] : (isset($_GET['myinput']) ? $_GET['myinput'] : '');
$dates = isset($_POST['dates']) ? $_POST['dates'] : (isset($_GET['dates']) ? $_GET['dates'] : '');
$datee = isset($_POST['datee']) ? $_POST['datee'] : (isset($_GET['datee']) ? $_GET['datee'] : '');
$user_id = isset($_POST['user_id']) ? $_POST['user_id'] : (isset($_GET['user_id']) ? $_GET['user_id'] : '0');

if (!empty($input)) {
    if ($input == 'ايرادات') {
        // بناء الاستعلام مع فلتر المستخدم
        $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
        $date_condition = ($dates && $datee) ? " AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'" : "";

        $query = "SELECT stud_tb.*, stud_pay.*, users_tb.user_name
                  FROM stud_tb, stud_pay, users_tb
                  WHERE stud_pay.id_stud = stud_tb.id
                  AND stud_tb.userID = users_tb.id_user
                  $date_condition $user_condition
                  ORDER BY stud_tb.datein DESC";
        $query_run = mysqli_query($con, $query);
        if (mysqli_num_rows($query_run) > 0) {
?>
            <div class="alert alert-success">
                <h5><i class="fas fa-arrow-up"></i> الإيرادات</h5>
                <p>عدد السجلات: <?= mysqli_num_rows($query_run) ?></p>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col"><i class="fas fa-user"></i> مستخدم الحضانة</th>
                            <th scope="col"><i class="fas fa-calendar"></i> تاريخ التسجيل</th>
                            <th scope="col"><i class="fas fa-money-bill"></i> قيمة الاشتراك</th>
                            <th scope="col"><i class="fas fa-child"></i> اسم الطالب</th>
                            <th scope="col"><i class="fas fa-receipt"></i> رقم الوصل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $count = 0;
                        foreach ($query_run as $items) {
                            $count += $items['cash_stud'];
                        ?>
                        <tr>
                            <td><span class="badge badge-primary"><?= htmlspecialchars($items['user_name']); ?></span></td>
                            <td><?= date('Y-m-d', strtotime($items['datein'])); ?></td>
                            <td><strong class="text-success">IQD <?= number_format($items['cash_stud']); ?></strong></td>
                            <td><?= htmlspecialchars($items['name']); ?></td>
                            <td><span class="badge badge-info"><?= $items['id_pay']; ?></span></td>
                        </tr>
                        <?php
                        }
                        ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-success">
                            <th colspan="2">إجمالي الإيرادات:</th>
                            <th><strong>IQD <?= number_format($count); ?></strong></th>
                            <th colspan="2"><?= mysqli_num_rows($query_run) ?> معاملة</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <?php
            $msg1 = "مجموع الايرادات ";
            $iconC = "fa-solid fa-money-bills";
            echo "<script> TosatCash(' 8px solid rgb(22, 204, 247)','rgb(22, 204, 247)','$msg1','$count','$iconC')</script>";
        } else {
            echo "<script>reomvetoast()</script>";
            ?>
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>لا توجد إيرادات</h5>
                <p>لم يتم العثور على إيرادات في الفترة المحددة</p>
            </div>
            <?php
        }
    } elseif ($input == 'مصروفات') {
        // بناء الاستعلام مع فلتر المستخدم
        $user_condition = ($user_id != '0') ? " AND depit_tb.userID = '$user_id'" : "";
        $date_condition = ($dates && $datee) ? " AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'" : "";

        $query = "SELECT depit_tb.*, users_tb.user_name
                  FROM users_tb, depit_tb
                  WHERE depit_tb.userID = users_tb.id_user
                  $date_condition $user_condition
                  ORDER BY depit_tb.depit_date DESC";
        $query_run = mysqli_query($con, $query);
        if (mysqli_num_rows($query_run) > 0) {
?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-arrow-down"></i> المصروفات</h5>
                <p>عدد السجلات: <?= mysqli_num_rows($query_run) ?></p>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col"><i class="fas fa-user"></i> مستخدم الحضانة</th>
                            <th scope="col"><i class="fas fa-calendar"></i> التاريخ</th>
                            <th scope="col"><i class="fas fa-money-bill"></i> قيمة المصروفات</th>
                            <th scope="col"><i class="fas fa-sticky-note"></i> وصف المصروفات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $count2 = 0;
                        foreach ($query_run as $items) {
                            $count2 += $items['depit_cash'];
                        ?>
                        <tr>
                            <td><span class="badge badge-primary"><?= htmlspecialchars($items['user_name']); ?></span></td>
                            <td><?= date('Y-m-d', strtotime($items['depit_date'])); ?></td>
                            <td><strong class="text-danger">IQD <?= number_format($items['depit_cash']); ?></strong></td>
                            <td><?= htmlspecialchars($items['depit_note']); ?></td>
                        </tr>
                        <?php
                        }
                        ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-danger">
                            <th colspan="2">إجمالي المصروفات:</th>
                            <th><strong>IQD <?= number_format($count2); ?></strong></th>
                            <th><?= mysqli_num_rows($query_run) ?> معاملة</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <?php
            $msg1 = "مجموع المصروفات ";
            $iconC = "fa-solid fa-money-bills";
            echo "<script> TosatCash(' 8px solid rgb(247, 22, 43)','rgb(247, 22, 43)','$msg1','$count2','$iconC')</script>";
        } else {
            echo "<script>reomvetoast()</script>";
            ?>
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>لا توجد مصروفات</h5>
                <p>لم يتم العثور على مصروفات في الفترة المحددة</p>
            </div>
            <?php
        }
    } elseif ($input == 'رواتب') {
                $dates = $_GET['dates'];
                $datee = $_GET['datee'];
                $query = "SELECT * FROM users_tb,employ_tb WHERE  employ_tb.userID=users_tb.id_user ";
                $query_run = mysqli_query($con, $query);
                if (mysqli_num_rows($query_run) > 0) {
            ?> <table class="table">
                <thead>
                    <tr>
                        <th scope="col"> مستخدم الحضانة </th>
                        <th scope="col"> الراتب الشهري </th>
                        <th scope="col">المسمى الوظيفي</th>
                        <th scope="col"> تاريخ المباشرة</th>
                        <th scope="col"> اسم الموظف</th>

                    </tr>
                </thead>
                <tbody>
                    <?php
                    $count3 = 0;
                    foreach ($query_run as $items) {
                        $count3 += $items['salary'];
                    ?>

                        <td><?= $items['user_name']; ?></td>
                        <td> IQD <?= number_format($items['salary']); ?></td>
                        <td><?= $items['job']; ?></td>
                        <td><?= $items['date_start']; ?></td>
                        <td><?= $items['f_name']; ?></td>

                </tbody>


<?php
                    }
                    $msg1="مجموع الرواتب ";
                    $iconC="fa-solid fa-people-group";
                    echo "<script> TosatCash(' 8px solid rgb(178, 102, 255)','rgb(178, 102, 255)','$msg1','$count3','$iconC')</script>";
                } else {

                    echo  "<script>reomvetoast()</script>";
                    ?>
                     <table class="table">
                    <thead>
                        <tr>
                            <th > NO DATA FOUND</th>
                        </tr>
                    </thead>
                    <tbody>
                            <td style="font-size: 25px;">لاتوجد بيانات  للموظفين</td>
                    </tbody>
                    <?php
                }
    } elseif ($input == 'الكل' || $input == '') {
        // عرض جميع البيانات
        ?>
        <div class="alert alert-info">
            <h5><i class="fas fa-chart-bar"></i> جميع المعاملات المالية</h5>
            <p>عرض شامل لجميع الإيرادات والمصروفات والرواتب</p>
        </div>

        <div class="row">
            <!-- الإيرادات -->
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6><i class="fas fa-arrow-up"></i> آخر الإيرادات</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
                        $rev_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name
                                      FROM stud_tb, stud_pay, users_tb
                                      WHERE stud_pay.id_stud = stud_tb.id
                                      AND stud_tb.userID = users_tb.id_user
                                      $user_condition
                                      ORDER BY stud_pay.datein DESC LIMIT 5";
                        $rev_result = mysqli_query($con, $rev_query);

                        if ($rev_result && mysqli_num_rows($rev_result) > 0) {
                            while ($item = mysqli_fetch_assoc($rev_result)) {
                                echo '<div class="mb-2 p-2 border-left border-success">';
                                echo '<strong>' . htmlspecialchars($item['name']) . '</strong><br>';
                                echo '<small class="text-success">IQD ' . number_format($item['cash_stud']) . '</small><br>';
                                echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['datein'])) . ' - ' . htmlspecialchars($item['user_name']) . '</small>';
                                echo '</div>';
                            }
                        } else {
                            echo '<p class="text-muted">لا توجد إيرادات</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>

            <!-- المصروفات -->
            <div class="col-md-4">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6><i class="fas fa-arrow-down"></i> آخر المصروفات</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $user_condition = ($user_id != '0') ? " AND depit_tb.userID = '$user_id'" : "";
                        $exp_query = "SELECT depit_tb.depit_note, depit_tb.depit_cash, depit_tb.depit_date, users_tb.user_name
                                      FROM users_tb, depit_tb
                                      WHERE depit_tb.userID = users_tb.id_user
                                      $user_condition
                                      ORDER BY depit_tb.depit_date DESC LIMIT 5";
                        $exp_result = mysqli_query($con, $exp_query);

                        if ($exp_result && mysqli_num_rows($exp_result) > 0) {
                            while ($item = mysqli_fetch_assoc($exp_result)) {
                                echo '<div class="mb-2 p-2 border-left border-danger">';
                                echo '<strong>' . htmlspecialchars($item['depit_note']) . '</strong><br>';
                                echo '<small class="text-danger">IQD ' . number_format($item['depit_cash']) . '</small><br>';
                                echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['depit_date'])) . ' - ' . htmlspecialchars($item['user_name']) . '</small>';
                                echo '</div>';
                            }
                        } else {
                            echo '<p class="text-muted">لا توجد مصروفات</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>

            <!-- الرواتب -->
            <div class="col-md-4">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6><i class="fas fa-users"></i> آخر الرواتب</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $user_condition = ($user_id != '0') ? " AND employ_tb.userID = '$user_id'" : "";
                        $sal_query = "SELECT employ_tb.f_name, employ_tb.job, employ_tb.salary, employ_tb.date_start, users_tb.user_name
                                      FROM users_tb, employ_tb
                                      WHERE employ_tb.userID = users_tb.id_user
                                      $user_condition
                                      ORDER BY employ_tb.date_start DESC LIMIT 5";
                        $sal_result = mysqli_query($con, $sal_query);

                        if ($sal_result && mysqli_num_rows($sal_result) > 0) {
                            while ($item = mysqli_fetch_assoc($sal_result)) {
                                echo '<div class="mb-2 p-2 border-left border-warning">';
                                echo '<strong>' . htmlspecialchars($item['f_name']) . '</strong><br>';
                                echo '<small>' . htmlspecialchars($item['job']) . '</small><br>';
                                echo '<small class="text-warning">IQD ' . number_format($item['salary']) . '</small><br>';
                                echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['date_start'])) . ' - ' . htmlspecialchars($item['user_name']) . '</small>';
                                echo '</div>';
                            }
                        } else {
                            echo '<p class="text-muted">لا توجد رواتب</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-muted">استخدم الفلاتر أعلاه لعرض تفاصيل أكثر لكل نوع من المعاملات</p>
        </div>
        <?php
    }
}
?>