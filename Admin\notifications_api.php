<?php
session_start();
header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

include 'addon/dbcon.php';

$user_id = $_SESSION['user']->id_user;
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_notifications':
        getNotifications($con, $user_id);
        break;
    case 'get_count':
        getUnreadCount($con, $user_id);
        break;
    case 'mark_read':
        markAsRead($con, $user_id, $_POST['notification_id'] ?? 0);
        break;
    case 'mark_all_read':
        markAllAsRead($con, $user_id);
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
}

function getNotifications($con, $user_id) {
    $limit = $_GET['limit'] ?? 10;
    $offset = $_GET['offset'] ?? 0;
    
    $query = "SELECT n.*, 
              CASE 
                WHEN n.type = 'leave_request' THEN lr.employee_name
                WHEN n.type = 'need_request' THEN nr.need_name
                WHEN n.type = 'leave_response' THEN lr.employee_name
                WHEN n.type = 'need_response' THEN nr.need_name
                ELSE NULL
              END as related_name
              FROM notifications n
              LEFT JOIN leave_requests lr ON n.type IN ('leave_request', 'leave_response') AND n.related_id = lr.id_leave
              LEFT JOIN needs_requests nr ON n.type IN ('need_request', 'need_response') AND n.related_id = nr.id_need
              WHERE n.user_id = ? 
              ORDER BY n.created_at DESC 
              LIMIT ? OFFSET ?";
    
    $stmt = $con->prepare($query);
    $stmt->bind_param("iii", $user_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $row['time_ago'] = timeAgo($row['created_at']);
        $notifications[] = $row;
    }
    
    echo json_encode(['success' => true, 'notifications' => $notifications]);
}

function getUnreadCount($con, $user_id) {
    $query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
    $stmt = $con->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    echo json_encode(['success' => true, 'count' => $row['count']]);
}

function markAsRead($con, $user_id, $notification_id) {
    $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ? AND user_id = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("ii", $notification_id, $user_id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث الإشعار']);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في التحديث']);
    }
}

function markAllAsRead($con, $user_id) {
    $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND is_read = 0";
    $stmt = $con->prepare($query);
    $stmt->bind_param("i", $user_id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث جميع الإشعارات']);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في التحديث']);
    }
}

function timeAgo($datetime) {
    // تعيين المنطقة الزمنية للعراق
    date_default_timezone_set('Asia/Baghdad');

    $time = time() - strtotime($datetime);

    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة مضت';
    if ($time < 86400) return floor($time/3600) . ' ساعة مضت';
    if ($time < 2592000) return floor($time/86400) . ' يوم مضى';
    if ($time < 31536000) return floor($time/2592000) . ' شهر مضى';
    return floor($time/31536000) . ' سنة مضت';
}

// إنشاء إشعار جديد (للاستخدام الداخلي)
function createNotification($con, $user_id, $title, $message, $type, $related_id = null) {
    $query = "INSERT INTO notifications (user_id, title, message, type, related_id) VALUES (?, ?, ?, ?, ?)";
    $stmt = $con->prepare($query);
    $stmt->bind_param("isssi", $user_id, $title, $message, $type, $related_id);
    return $stmt->execute();
}

// إرسال إشعار لجميع المدراء والمدققين
function notifyAdmins($con, $title, $message, $type, $related_id = null) {
    $query = "SELECT id_user FROM users_tb WHERE role IN ('Admin', 'Moda', 'Modaqeq')";
    $result = $con->query($query);
    
    while ($row = $result->fetch_assoc()) {
        createNotification($con, $row['id_user'], $title, $message, $type, $related_id);
    }
}

$con->close();
?>
