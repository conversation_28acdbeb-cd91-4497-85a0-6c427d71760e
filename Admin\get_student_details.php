<?php
session_start();
header('Content-Type: application/json');

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($student_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب غير صحيح']);
    exit;
}

// جلب تفاصيل الطالب
$query = "SELECT s.*, sp.cash_stud, sp.date_exp, u.user_name 
          FROM stud_tb s 
          LEFT JOIN stud_pay sp ON s.id = sp.id_stud 
          LEFT JOIN users_tb u ON s.userID = u.id_user 
          WHERE s.id = ?";

$stmt = $con->prepare($query);
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $student = $result->fetch_assoc();
    echo json_encode(['success' => true, 'student' => $student]);
} else {
    echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
}

$con->close();
?>
