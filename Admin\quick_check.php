<?php
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

include "addon/dbcon.php";

echo "<h2>فحص سريع للبيانات</h2>";

// 1. فحص المستخدمين
echo "<h3>1. المستخدمين:</h3>";
$sql = "SELECT id_user, user_name FROM users_tb LIMIT 5";
$result = mysqli_query($con, $sql);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr><td>{$row['id_user']}</td><td>{$row['user_name']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>خطأ: " . mysqli_error($con) . "</p>";
}

// 2. فحص الطلاب
echo "<h3>2. الطلاب:</h3>";
$sql = "SELECT id, name, userID FROM stud_tb LIMIT 5";
$result = mysqli_query($con, $sql);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>اسم الطالب</th><th>ID المستخدم</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['userID']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>خطأ: " . mysqli_error($con) . "</p>";
}

// 3. فحص المدفوعات
echo "<h3>3. المدفوعات:</h3>";
$sql = "SELECT id_pay, id_stud, date_exp FROM stud_pay LIMIT 5";
$result = mysqli_query($con, $sql);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID الدفع</th><th>ID الطالب</th><th>تاريخ الانتهاء</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr><td>{$row['id_pay']}</td><td>{$row['id_stud']}</td><td>{$row['date_exp']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>خطأ: " . mysqli_error($con) . "</p>";
}

// 4. اختبار الاستعلام المدمج
echo "<h3>4. اختبار الاستعلام المدمج:</h3>";
$sql = "SELECT stud_tb.name, users_tb.user_name, stud_pay.date_exp 
        FROM stud_tb, stud_pay, users_tb 
        WHERE stud_pay.id_stud = stud_tb.id 
        AND stud_tb.userID = users_tb.id_user 
        LIMIT 5";
$result = mysqli_query($con, $sql);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>اسم الطالب</th><th>اسم المستخدم</th><th>تاريخ الانتهاء</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr><td>{$row['name']}</td><td>{$row['user_name']}</td><td>{$row['date_exp']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>خطأ في الاستعلام المدمج: " . mysqli_error($con) . "</p>";
}

// 5. اختبار لمستخدم محدد
echo "<h3>5. اختبار لمستخدم محدد:</h3>";
$sql = "SELECT id_user FROM users_tb LIMIT 1";
$result = mysqli_query($con, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    $user_id = mysqli_fetch_assoc($result)['id_user'];
    echo "<p>اختبار للمستخدم ID: $user_id</p>";
    
    $sql2 = "SELECT stud_tb.name, stud_pay.date_exp 
             FROM stud_tb, stud_pay, users_tb 
             WHERE stud_pay.id_stud = stud_tb.id 
             AND stud_tb.userID = users_tb.id_user 
             AND users_tb.id_user = $user_id";
    $result2 = mysqli_query($con, $sql2);
    if ($result2) {
        $count = mysqli_num_rows($result2);
        echo "<p>عدد الطلاب لهذا المستخدم: $count</p>";
        if ($count > 0) {
            echo "<table border='1'>";
            echo "<tr><th>اسم الطالب</th><th>تاريخ الانتهاء</th></tr>";
            while ($row = mysqli_fetch_assoc($result2)) {
                echo "<tr><td>{$row['name']}</td><td>{$row['date_exp']}</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>خطأ: " . mysqli_error($con) . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='infost.php'>العودة إلى صفحة الإحصائيات</a></p>";
echo "<p><a href='test_ajax.php'>اختبار AJAX</a></p>";
echo "<p><a href='direct_test.php'>اختبار مباشر</a></p>";
?>
