<?php
session_start();
header('Content-Type: application/json');

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

$type = isset($_GET['type']) ? $_GET['type'] : '';
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
$datenow = date('Y-m-d');

$response = ['success' => false, 'message' => 'نوع غير صحيح'];

switch ($type) {
    case 'total_students':
        $response = getTotalStudents($con, $user_id);
        break;
    case 'active_students':
        $response = getActiveStudents($con, $user_id, $datenow);
        break;
    case 'expired_students':
        $response = getExpiredStudents($con, $user_id, $datenow);
        break;
    case 'nursery':
        $response = getStudentsByCategory($con, $user_id, 'حضانة');
        break;
    case 'kindergarten':
        $response = getStudentsByCategory($con, $user_id, 'روضة');
        break;
    case 'preparatory':
        $response = getStudentsByCategory($con, $user_id, 'تمهيدي');
        break;
    case 'preschool':
        $response = getStudentsByCategory($con, $user_id, 'تحضيري');
        break;
    case 'total_payments':
        $response = getTotalPayments($con, $user_id);
        break;
    case 'monthly_payments':
        $response = getMonthlyPayments($con, $user_id, $datenow);
        break;
    case 'total_expenses':
        $response = getTotalExpenses($con, $user_id);
        break;
    case 'user_expenses':
        $response = getUserExpenses($con, $user_id);
        break;
    case 'expenses_count':
        $response = getExpensesCount($con, $user_id);
        break;
    case 'last_expense':
        $response = getLastExpense($con, $user_id);
        break;
    case 'expiring_students':
        $response = getExpiringStudents($con, $user_id, $datenow);
        break;
    case 'new_students':
        $response = getNewStudents($con, $user_id, $datenow);
        break;
    case 'employees':
        $response = getEmployees($con);
        break;
    case 'users':
        $response = getUsers($con);
        break;
}

echo json_encode($response);
$con->close();

// وظائف مساعدة
function getTotalStudents($con, $user_id) {
    $condition = $user_id > 0 ? "WHERE s.userID = $user_id" : "";
    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF(sp.date_exp, CURDATE()) as days_remaining
              FROM stud_tb s
              LEFT JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              $condition
              ORDER BY s.name";

    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }
    
    return [
        'success' => true,
        'title' => 'إجمالي الطلاب',
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getActiveStudents($con, $user_id, $datenow) {
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";
    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF(sp.date_exp, '$datenow') as days_remaining
              FROM stud_tb s
              INNER JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE DATE(sp.date_exp) > '$datenow' $condition
              ORDER BY sp.date_exp DESC";
    
    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $row['status'] = 'فعال (' . $row['days_remaining'] . ' يوم)';
        $row['status_class'] = 'status-active';
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }
    
    return [
        'success' => true,
        'title' => 'الطلاب الفعالين',
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getExpiredStudents($con, $user_id, $datenow) {
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";
    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF('$datenow', sp.date_exp) as days_expired
              FROM stud_tb s
              INNER JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE DATE(sp.date_exp) <= '$datenow' $condition
              ORDER BY sp.date_exp DESC";
    
    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $row['status'] = 'منتهي منذ ' . $row['days_expired'] . ' يوم';
        $row['status_class'] = 'status-expired';
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }
    
    return [
        'success' => true,
        'title' => 'الطلاب منتهي الاشتراك',
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getStudentsByCategory($con, $user_id, $category) {
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";
    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF(sp.date_exp, CURDATE()) as days_remaining
              FROM stud_tb s
              LEFT JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE s.catg = '$category' $condition
              ORDER BY s.name";
    
    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        if ($row['days_remaining'] !== null) {
            if ($row['days_remaining'] <= 0) {
                $row['status'] = 'منتهي';
                $row['status_class'] = 'status-expired';
            } elseif ($row['days_remaining'] <= 10) {
                $row['status'] = 'ينتهي قريباً (' . $row['days_remaining'] . ' أيام)';
                $row['status_class'] = 'status-warning';
            } else {
                $row['status'] = 'فعال (' . $row['days_remaining'] . ' يوم)';
                $row['status_class'] = 'status-active';
            }
        } else {
            $row['status'] = 'غير محدد';
            $row['status_class'] = 'status-unknown';
        }
        
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }
    
    return [
        'success' => true,
        'title' => "طلاب صنف $category",
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getTotalPayments($con, $user_id) {
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";
    $query = "SELECT sp.*, s.name as student_name, u.user_name
              FROM stud_pay sp
              INNER JOIN stud_tb s ON sp.id_stud = s.id
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE 1=1 $condition
              ORDER BY sp.date_exp DESC";
    
    $result = mysqli_query($con, $query);
    $payments = [];
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['cash_stud'];
        $payments[] = $row;
    }
    
    return [
        'success' => true,
        'title' => 'إجمالي المدفوعات',
        'type' => 'payments',
        'data' => $payments,
        'summary' => [
            'total' => count($payments),
            'amount' => number_format($total_amount),
            'average' => count($payments) > 0 ? number_format($total_amount / count($payments)) : 0
        ]
    ];
}

function getMonthlyPayments($con, $user_id, $datenow) {
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";
    $query = "SELECT sp.*, s.name as student_name, u.user_name
              FROM stud_pay sp
              INNER JOIN stud_tb s ON sp.id_stud = s.id
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE MONTH(sp.date_exp) = MONTH('$datenow') 
              AND YEAR(sp.date_exp) = YEAR('$datenow') $condition
              ORDER BY sp.date_exp DESC";
    
    $result = mysqli_query($con, $query);
    $payments = [];
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['cash_stud'];
        $payments[] = $row;
    }
    
    return [
        'success' => true,
        'title' => 'مدفوعات هذا الشهر',
        'type' => 'payments',
        'data' => $payments,
        'summary' => [
            'total' => count($payments),
            'amount' => number_format($total_amount),
            'average' => count($payments) > 0 ? number_format($total_amount / count($payments)) : 0
        ]
    ];
}

function getTotalExpenses($con, $user_id) {
    // إجمالي المصروفات لجميع المستخدمين (تجاهل user_id)
    $query = "SELECT d.*, u.user_name
              FROM depit_tb d
              LEFT JOIN users_tb u ON d.userID = u.id_user
              ORDER BY d.depit_date DESC";

    $result = mysqli_query($con, $query);
    $expenses = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['depit_cash'];
        $expenses[] = $row;
    }

    return [
        'success' => true,
        'title' => 'إجمالي المصروفات',
        'type' => 'expenses',
        'data' => $expenses,
        'summary' => [
            'total' => count($expenses),
            'amount' => number_format($total_amount),
            'average' => count($expenses) > 0 ? number_format($total_amount / count($expenses)) : 0
        ]
    ];
}

function getUserExpenses($con, $user_id) {
    $query = "SELECT d.*, u.user_name
              FROM depit_tb d
              LEFT JOIN users_tb u ON d.userID = u.id_user
              WHERE d.userID = $user_id
              ORDER BY d.depit_date DESC";

    $result = mysqli_query($con, $query);
    $expenses = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['depit_cash'];
        $expenses[] = $row;
    }

    return [
        'success' => true,
        'title' => 'مصروفات المستخدم',
        'type' => 'expenses',
        'data' => $expenses,
        'summary' => [
            'total' => count($expenses),
            'amount' => number_format($total_amount),
            'average' => count($expenses) > 0 ? number_format($total_amount / count($expenses)) : 0
        ]
    ];
}

function getExpensesCount($con, $user_id) {
    return getUserExpenses($con, $user_id);
}

function getLastExpense($con, $user_id) {
    $query = "SELECT d.*, u.user_name
              FROM depit_tb d
              LEFT JOIN users_tb u ON d.userID = u.id_user
              WHERE d.userID = $user_id
              ORDER BY d.depit_date DESC
              LIMIT 10";

    $result = mysqli_query($con, $query);
    $expenses = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $total_amount += $row['depit_cash'];
        $expenses[] = $row;
    }

    return [
        'success' => true,
        'title' => 'آخر المصروفات',
        'type' => 'expenses',
        'data' => $expenses,
        'summary' => [
            'total' => count($expenses),
            'amount' => number_format($total_amount),
            'average' => count($expenses) > 0 ? number_format($total_amount / count($expenses)) : 0
        ]
    ];
}

function getExpiringStudents($con, $user_id, $datenow) {
    $date_3_days = date('Y-m-d', strtotime($datenow. ' + 3 days'));
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";

    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF(sp.date_exp, '$datenow') as days_remaining
              FROM stud_tb s
              INNER JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE DATE(sp.date_exp) BETWEEN '$datenow' AND '$date_3_days' $condition
              ORDER BY sp.date_exp ASC";

    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $row['status'] = 'ينتهي خلال ' . $row['days_remaining'] . ' أيام';
        $row['status_class'] = 'status-warning';
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }

    return [
        'success' => true,
        'title' => 'الطلاب قريبي الانتهاء',
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getNewStudents($con, $user_id, $datenow) {
    $date_30_days_ago = date('Y-m-d', strtotime($datenow. ' - 30 days'));
    $condition = $user_id > 0 ? "AND s.userID = $user_id" : "";

    $query = "SELECT s.*, sp.cash_stud, sp.date_exp, sp.id_pay, u.user_name,
              DATEDIFF('$datenow', s.datein) as days_since_registration
              FROM stud_tb s
              LEFT JOIN stud_pay sp ON s.id = sp.id_stud
              LEFT JOIN users_tb u ON s.userID = u.id_user
              WHERE s.datein >= '$date_30_days_ago' $condition
              ORDER BY s.datein DESC";

    $result = mysqli_query($con, $query);
    $students = [];
    $total_amount = 0;

    while ($row = mysqli_fetch_assoc($result)) {
        $row['status'] = 'طالب جديد (' . $row['days_since_registration'] . ' أيام)';
        $row['status_class'] = 'status-new';
        $total_amount += $row['cash_stud'] ?? 0;
        $students[] = $row;
    }

    return [
        'success' => true,
        'title' => 'الطلاب الجدد',
        'type' => 'students',
        'data' => $students,
        'summary' => [
            'total' => count($students),
            'amount' => number_format($total_amount),
            'average' => count($students) > 0 ? number_format($total_amount / count($students)) : 0
        ]
    ];
}

function getEmployees($con) {
    // التحقق من وجود جدول الموظفين
    $table_check = mysqli_query($con, "SHOW TABLES LIKE 'employ_tb'");
    if (mysqli_num_rows($table_check) == 0) {
        return [
            'success' => true,
            'title' => 'الموظفين',
            'type' => 'other',
            'data' => [],
            'summary' => [
                'total' => 0,
                'amount' => 0,
                'average' => 0
            ]
        ];
    }

    $query = "SELECT * FROM employ_tb ORDER BY id";
    $result = mysqli_query($con, $query);
    $employees = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $employees[] = $row;
    }

    return [
        'success' => true,
        'title' => 'الموظفين',
        'type' => 'other',
        'data' => $employees,
        'summary' => [
            'total' => count($employees),
            'amount' => 0,
            'average' => 0
        ]
    ];
}

function getUsers($con) {
    $query = "SELECT u.*, COUNT(s.id) as student_count
              FROM users_tb u
              LEFT JOIN stud_tb s ON u.id_user = s.userID
              WHERE u.role = 'User'
              GROUP BY u.id_user, u.user_name
              ORDER BY u.user_name";

    $result = mysqli_query($con, $query);
    $users = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $users[] = $row;
    }

    return [
        'success' => true,
        'title' => 'المستخدمين',
        'type' => 'other',
        'data' => $users,
        'summary' => [
            'total' => count($users),
            'amount' => 0,
            'average' => 0
        ]
    ];
}
?>
