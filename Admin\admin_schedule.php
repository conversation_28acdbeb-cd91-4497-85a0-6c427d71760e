<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📅 الجدول الدراسي - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container {
            max-width: 1000px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .coming-soon-icon {
            font-size: 5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }

        .coming-soon-title {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .coming-soon-text {
            color: #7f8c8d;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .features-list {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: right;
        }

        .features-list h4 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .feature-icon {
            color: #667eea;
            font-size: 1.2rem;
            width: 20px;
        }

        .btn-back {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-back:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .content-card {
                padding: 2rem;
            }
            
            .coming-soon-icon {
                font-size: 4rem;
            }
            
            .coming-soon-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container">
        <!-- Header -->
        <div class="header-card">
            <h1>📅 الجدول الدراسي</h1>
            <p style="color: #7f8c8d; margin: 0;">إدارة الجدول الدراسي والحصص والمواعيد</p>
        </div>

        <!-- Coming Soon Content -->
        <div class="content-card">
            <div class="coming-soon-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            
            <h2 class="coming-soon-title">قريباً جداً!</h2>
            
            <p class="coming-soon-text">
                نعمل حالياً على تطوير نظام إدارة الجدول الدراسي المتقدم.<br>
                سيكون متاحاً قريباً مع مميزات رائعة لتنظيم الحصص والمواعيد.
            </p>

            <div class="features-list">
                <h4>
                    <i class="fas fa-star"></i>
                    المميزات القادمة
                </h4>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>إنشاء وتعديل الجدول الدراسي الأسبوعي</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>إضافة المواد الدراسية والمعلمين</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>تحديد أوقات الحصص والاستراحات</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>إرسال إشعارات للطلاب عن التغييرات</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>طباعة الجدول وتصديره</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>
                    <span>عرض الجدول في التطبيق للطلاب</span>
                </div>
            </div>

            <a href="student_app_control.php" class="btn-back">
                <i class="fas fa-arrow-right"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
