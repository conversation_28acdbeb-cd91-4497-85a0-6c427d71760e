<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات الموظفين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <form action="" metho="POST">
  <div class="search">
  <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected >اختر احد المستخدمين</option>
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>   
        </select>
  <button class="btn btn-success text-light" name="sub"  ><a href="../Admin/addon/expor_employ.php">تحميل اكسل</a> </button>
  <button class="btn btn-primary text-light" onclick="printEmployeesTable()">طباعة</button>
  <button class="btn btn-secondary text-light" name="sub"  ><a href="search_employ.php">بحث </a> </button>
  <button class="btn btn-secondary text-light" name="sub"  ><a href="addemploy.php">اضافة موظف</a> </button>
  
    
    
  </div>
  </form>

    <table class="table">
  <thead>
    <tr>
    
      <th scope="col"> العمليات</th>
      <th scope="col"> تابع الى</th>
      <th scope="col"> الراتب الشهري</th>
      <th scope="col"> السكن</th>
      <th scope="col">تاريخ المباشرة</th>
      <th scope="col"> العنوان الوظيفي   </th>
      <th scope="col">  تاريخ الميلاد</th>
      <th scope="col"> اسم الموظف الثلاثي</th>
   
    </tr>
  </thead>
  <tbody id="myTable">

   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>


   </body>
   <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        //console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           //console.log(id)
          $.ajax({url:'addon/reomve_employ.php',
          method:"POST",
          data:({name:id}),
          success:function(response){
            console.log(response)
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

         }
        });
        });
      }
    
    </script>
   <script>
    $("#selc").change(function(){
      var selc =$(this).val();
     // console.log(selc)
        $.ajax({
          method: "POST",
          url: "addon/infoemployF.php",
          data: {id:selc},
          success: function (data) {
            $("#myTable").html(data);
          }
        
        })
      });
</script>

<script>
function printEmployeesTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول وإزالة عمود العمليات
    var tableElement = document.getElementById('myTable');
    if (!tableElement) {
        alert('لا توجد بيانات للطباعة');
        return;
    }

    // إنشاء جدول جديد للطباعة
    var printTable = document.createElement('table');
    printTable.className = 'table';

    // إضافة رأس الجدول
    var thead = document.createElement('thead');
    var headerRow = document.createElement('tr');
    var headers = ['اسم الموظف الثلاثي', 'تاريخ الميلاد', 'العنوان الوظيفي', 'تاريخ المباشرة', 'السكن', 'الراتب الشهري', 'تابع الى'];
    headers.forEach(function(headerText) {
        var th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    printTable.appendChild(thead);

    // إضافة بيانات الجدول (تجاهل عمود العمليات)
    var tbody = document.createElement('tbody');
    var rows = tableElement.querySelectorAll('tr');
    rows.forEach(function(row) {
        var cells = row.querySelectorAll('td');
        if (cells.length > 1) { // تأكد من وجود بيانات
            var newRow = document.createElement('tr');
            // تجاهل العمود الأول (العمليات) وأخذ باقي الأعمدة
            for (var i = 1; i < cells.length; i++) {
                var newCell = document.createElement('td');
                newCell.textContent = cells[i].textContent;
                newRow.appendChild(newCell);
            }
            tbody.appendChild(newRow);
        }
    });
    printTable.appendChild(tbody);

    var tableContent = printTable.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير معلومات الموظفين</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير معلومات الموظفين</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>اسم الموظف الثلاثي</th>
                    <th>تاريخ الميلاد</th>
                    <th>العنوان الوظيفي</th>
                    <th>تاريخ المباشرة</th>
                    <th>السكن</th>
                    <th>الراتب الشهري</th>
                    <th>تابع الى</th>
                </tr>
            </thead>
            <tbody>
                ${document.getElementById('myTable').innerHTML}
            </tbody>
        </table>

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>

</html>