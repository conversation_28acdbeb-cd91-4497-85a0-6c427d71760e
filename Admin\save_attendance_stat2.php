<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit();
}

// الاتصال بقاعدة البيانات
include "addon/dbcon.php";

// جلب البيانات من الطلب
$employee_id = $_POST['employee_id'] ?? '';
$employee_name = $_POST['employee_name'] ?? '';
$attendance_date = $_POST['attendance_date'] ?? '';
$status = $_POST['status'] ?? '';
$check_in_time = $_POST['check_in_time'] ?? null;
$notes = $_POST['notes'] ?? '';
$leave_start_date = $_POST['leave_start_date'] ?? null;
$leave_end_date = $_POST['leave_end_date'] ?? null;

// التحقق من البيانات المطلوبة
if (empty($employee_id) || empty($employee_name) || empty($attendance_date) || empty($status)) {
    echo json_encode([
        'success' => false,
        'error' => 'البيانات المطلوبة مفقودة'
    ]);
    exit();
}

try {
    // التحقق من وجود جدول stat2
    $check_table = $con->query("SHOW TABLES LIKE 'stat2'");
    if (!$check_table || $check_table->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'error' => 'جدول stat2 غير موجود',
            'action' => 'check_table'
        ]);
        exit();
    }
    
    // تحويل الحالة إلى الصيغة المطلوبة
    $stat_employee = $status;
    switch($status) {
        case 'حضور في الوقت المحدد':
        case 'حاضر':
            $stat_employee = 'حاضر';
            break;
        case 'حضور متأخر':
        case 'متأخر':
            $stat_employee = 'متأخر';
            break;
        case 'غائب':
            $stat_employee = 'غائب';
            break;
        case 'إجازة':
            $stat_employee = 'إجازة';
            break;
        default:
            $stat_employee = $status;
    }
    
    // إنشاء التاريخ والوقت
    $data_stat = $attendance_date;
    if ($check_in_time && $stat_employee !== 'غائب') {
        $data_stat = $attendance_date . ' ' . $check_in_time . ':00';
    }
    
    // التحقق من وجود سجل سابق لنفس الموظف والتاريخ
    $check_existing = $con->prepare("SELECT id FROM stat2 WHERE id_employee = ? AND DATE(data_stat) = ?");
    $check_existing->bind_param("ss", $employee_id, $attendance_date);
    $check_existing->execute();
    $existing_result = $check_existing->get_result();
    
    if ($existing_result->num_rows > 0) {
        // تحديث السجل الموجود
        $update_query = "UPDATE stat2 SET 
                        stat_employee = ?, 
                        data_stat = ?, 
                        leave_start_date = ?, 
                        leave_end_date = ?
                        WHERE id_employee = ? AND DATE(data_stat) = ?";
        
        $stmt = $con->prepare($update_query);
        $stmt->bind_param("ssssss", $stat_employee, $data_stat, $leave_start_date, $leave_end_date, $employee_id, $attendance_date);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث بيانات الحضور بنجاح',
                'action' => 'updated'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في تحديث البيانات: ' . $con->error
            ]);
        }
    } else {
        // إدراج سجل جديد
        $insert_query = "INSERT INTO stat2 (id_employee, stat_employee, data_stat, leave_start_date, leave_end_date) 
                        VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $con->prepare($insert_query);
        $stmt->bind_param("sssss", $employee_id, $stat_employee, $data_stat, $leave_start_date, $leave_end_date);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حفظ بيانات الحضور بنجاح',
                'action' => 'inserted'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في حفظ البيانات: ' . $con->error
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في العملية: ' . $e->getMessage()
    ]);
}

// إغلاق الاتصال
$con->close();
?>
