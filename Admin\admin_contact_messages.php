<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// إنشاء جدول رسائل التواصل
$create_contact_messages = "CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    contact_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'general',
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    admin_reply TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    replied_at TIMESTAMP NULL
) DEFAULT CHARSET=utf8";
@mysqli_query($con, $create_contact_messages);

$message = '';
$messageType = 'success';

// معالجة الرد على الرسائل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reply_message'])) {
    $message_id = (int)($_POST['message_id'] ?? 0);
    $admin_reply = mysqli_real_escape_string($con, $_POST['admin_reply'] ?? '');
    
    if ($message_id > 0 && !empty($admin_reply)) {
        $sql = "UPDATE contact_messages SET admin_reply = '$admin_reply', status = 'replied', replied_at = NOW() WHERE id = $message_id";
        if (@mysqli_query($con, $sql)) {
            $message = 'تم الرد على الرسالة بنجاح!';
        } else {
            $message = 'حدث خطأ أثناء إرسال الرد!';
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى كتابة الرد!';
        $messageType = 'error';
    }
}

// جلب رسائل التواصل
$contact_messages = @mysqli_query($con, "SELECT cm.*, s.name as student_name FROM contact_messages cm LEFT JOIN stud_tb s ON cm.student_id = s.id ORDER BY cm.created_at DESC");
if (!$contact_messages) {
    $contact_messages = false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 رسائل التواصل - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container-fluid {
            max-width: 1400px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .content-card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .message-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .message-info h5 {
            color: #2c3e50;
            margin: 0;
            font-weight: bold;
        }

        .message-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .badge-custom {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .priority-general { background: #6c757d; color: white; }
        .priority-important { background: #ffc107; color: #212529; }
        .priority-urgent { background: #dc3545; color: white; }

        .type-academic { background: #007bff; color: white; }
        .type-administrative { background: #17a2b8; color: white; }
        .type-technical { background: #28a745; color: white; }
        .type-complaint { background: #dc3545; color: white; }
        .type-suggestion { background: #ffc107; color: #212529; }

        .status-pending { background: #ffc107; color: #212529; }
        .status-replied { background: #28a745; color: white; }

        .message-content {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            line-height: 1.6;
            color: #2c3e50;
        }

        .admin-reply {
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            border-right: 4px solid #667eea;
        }

        .btn-reply {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-reply:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px 20px 0 0;
            border: none;
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success-custom {
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            color: #27ae60;
            border-right: 5px solid #27ae60;
        }

        .alert-error-custom {
            background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
            color: #e74c3c;
            border-right: 5px solid #e74c3c;
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .message-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .message-meta {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container-fluid">
        <!-- Header -->
        <div class="header-card">
            <h1>💬 رسائل التواصل</h1>
            <p style="color: #7f8c8d; margin: 0;">عرض والرد على رسائل التواصل من الطلاب</p>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
            <div class="alert-custom <?php echo $messageType == 'success' ? 'alert-success-custom' : 'alert-error-custom'; ?>">
                <i class="fas <?php echo $messageType == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Messages List -->
        <div class="content-card">
            <div class="content-card-header">
                <i class="fas fa-comments"></i> جميع رسائل التواصل
            </div>
            <div class="content-card-body">
                <?php if ($contact_messages && mysqli_num_rows($contact_messages) > 0): ?>
                    <?php while ($row = mysqli_fetch_assoc($contact_messages)): 
                        $priority_classes = [
                            'general' => 'priority-general',
                            'important' => 'priority-important',
                            'urgent' => 'priority-urgent'
                        ];
                        $type_classes = [
                            'academic' => 'type-academic',
                            'administrative' => 'type-administrative',
                            'technical' => 'type-technical',
                            'complaint' => 'type-complaint',
                            'suggestion' => 'type-suggestion'
                        ];
                        $priority_names = [
                            'general' => 'عادية',
                            'important' => 'مهمة',
                            'urgent' => 'عاجلة'
                        ];
                        $type_names = [
                            'academic' => 'أكاديمي',
                            'administrative' => 'إداري',
                            'technical' => 'تقني',
                            'complaint' => 'شكوى',
                            'suggestion' => 'اقتراح'
                        ];
                    ?>
                        <div class="message-item">
                            <div class="message-header">
                                <div class="message-info">
                                    <h5><?php echo htmlspecialchars($row['subject']); ?></h5>
                                    <small class="text-muted">
                                        من: <?php echo htmlspecialchars($row['student_name'] ?? 'غير محدد'); ?> | 
                                        <?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="message-meta">
                                    <span class="badge-custom <?php echo $type_classes[$row['contact_type']] ?? 'type-academic'; ?>">
                                        <?php echo $type_names[$row['contact_type']] ?? $row['contact_type']; ?>
                                    </span>
                                    <span class="badge-custom <?php echo $priority_classes[$row['priority']] ?? 'priority-general'; ?>">
                                        <?php echo $priority_names[$row['priority']] ?? $row['priority']; ?>
                                    </span>
                                    <span class="badge-custom <?php echo $row['status'] == 'replied' ? 'status-replied' : 'status-pending'; ?>">
                                        <?php echo $row['status'] == 'replied' ? 'تم الرد' : 'في الانتظار'; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="message-content">
                                <?php echo nl2br(htmlspecialchars($row['message'])); ?>
                            </div>
                            
                            <?php if (!empty($row['admin_reply'])): ?>
                                <div class="admin-reply">
                                    <h6 style="color: #667eea; margin-bottom: 0.5rem;">
                                        <i class="fas fa-reply"></i> رد الإدارة:
                                    </h6>
                                    <p style="margin: 0; line-height: 1.6;">
                                        <?php echo nl2br(htmlspecialchars($row['admin_reply'])); ?>
                                    </p>
                                    <small class="text-muted">
                                        تم الرد في: <?php echo date('Y-m-d H:i', strtotime($row['replied_at'])); ?>
                                    </small>
                                </div>
                            <?php else: ?>
                                <div class="text-end">
                                    <button type="button" class="btn-reply" onclick="replyMessage(<?php echo $row['id']; ?>)">
                                        <i class="fas fa-reply"></i> رد على الرسالة
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="text-center" style="padding: 3rem;">
                        <i class="fas fa-comments" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                        <h4 style="color: #7f8c8d;">لا توجد رسائل تواصل حتى الآن</h4>
                        <p style="color: #95a5a6;">عندما يرسل الطلاب رسائل تواصل، ستظهر هنا</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div class="modal fade" id="replyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الرد على رسالة التواصل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="message_id" id="replyMessageId">
                        <div class="mb-3">
                            <label class="form-label">الرد على الرسالة:</label>
                            <textarea name="admin_reply" class="form-control" rows="6" required placeholder="اكتب ردك هنا..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="reply_message" class="btn-reply">
                            <i class="fas fa-paper-plane"></i> إرسال الرد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function replyMessage(messageId) {
            document.getElementById('replyMessageId').value = messageId;
            const modal = new bootstrap.Modal(document.getElementById('replyModal'));
            modal.show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
