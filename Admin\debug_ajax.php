<?php
session_start();

// محاكاة جلسة المدير
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

// تسجيل جميع البيانات المرسلة
error_log("POST data: " . print_r($_POST, true));
error_log("GET data: " . print_r($_GET, true));

echo "تم استلام الطلب بنجاح<br>";
echo "البيانات المرسلة:<br>";
echo "<pre>";
print_r($_POST);
print_r($_GET);
echo "</pre>";

include "addon/dbcon.php";

if ($con) {
    echo "الاتصال بقاعدة البيانات ناجح<br>";
    
    // اختبار استعلام بسيط
    $test_query = "SELECT COUNT(*) as count FROM users_tb";
    $test_result = mysqli_query($con, $test_query);
    
    if ($test_result) {
        $count = mysqli_fetch_assoc($test_result)['count'];
        echo "عدد المستخدمين في قاعدة البيانات: $count<br>";
    } else {
        echo "خطأ في الاستعلام: " . mysqli_error($con) . "<br>";
    }
    
    // اختبار بيانات الإيرادات
    $revenue_query = "SELECT COUNT(*) as count FROM stud_pay WHERE cash_stud > 0";
    $revenue_result = mysqli_query($con, $revenue_query);
    
    if ($revenue_result) {
        $count = mysqli_fetch_assoc($revenue_result)['count'];
        echo "عدد الإيرادات: $count<br>";
    }
    
    // اختبار بيانات المصروفات
    $expenses_query = "SELECT COUNT(*) as count FROM depit_tb WHERE depit_cash > 0";
    $expenses_result = mysqli_query($con, $expenses_query);
    
    if ($expenses_result) {
        $count = mysqli_fetch_assoc($expenses_result)['count'];
        echo "عدد المصروفات: $count<br>";
    }
    
    mysqli_close($con);
} else {
    echo "فشل الاتصال بقاعدة البيانات<br>";
}

echo "<hr>";
echo "تم انتهاء الاختبار بنجاح";
?>
