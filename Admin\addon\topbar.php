<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){


 }else{
        header("location:../login.php",true);
        die("");

    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      if(isset($_POST['exitbtn'])){
        header("location:../logout.php",true);
        exit();
      }


?>

<!-- تضمين CSS للأزرار الأفقية -->
<link rel="stylesheet" href="css/admin_navbar.css">
<link rel="stylesheet" href="css/navbar_notifications.css">

<style>
/* إخفاء التصميم القديم وإظهار التصميم الجديد */
nav ul {
    display: none !important;
}

.quick-actions {
    display: flex !important;
}

/* تأكيد أن الأزرار تظهر بالشكل الصحيح */
.quick-btn {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إخفاء عناصر القائمة القديمة */
.menu-btn, #click, #capss {
    display: none !important;
}

/* تأكيد تطبيق التصميم الجديد */
nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 999 !important;
}

body {
    margin-top: 90px !important;
    padding-top: 20px !important;
}

/* إخفاء الشريط الجانبي القديم تماماً */
.sidebar, .sidebar-wrapper, .sidebar-content {
    display: none !important;
}

/* تأكيد عرض الأزرار الأفقية */
.quick-actions .quick-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    padding: 6px 10px !important;
    margin: 0 3px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.quick-actions .quick-btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    color: white !important;
    text-decoration: none !important;
}

.quick-actions .quick-btn i {
    color: white !important;
    margin-left: 2px !important;
}

.quick-actions .quick-btn span {
    color: white !important;
    font-size: 11px !important;
}
</style>

<script>
// التأكد من إخفاء العناصر القديمة وإظهار الجديدة
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء القائمة القديمة
    const oldNavUl = document.querySelector('nav ul:not(.quick-actions)');
    if (oldNavUl && !oldNavUl.classList.contains('quick-actions')) {
        oldNavUl.style.display = 'none';
    }

    // إظهار الأزرار الجديدة
    const quickActions = document.querySelector('.quick-actions');
    if (quickActions) {
        quickActions.style.display = 'flex';
        quickActions.style.visibility = 'visible';
        quickActions.style.opacity = '1';
    }

    // إخفاء عناصر القائمة القديمة
    const menuBtn = document.querySelector('.menu-btn');
    const clickInput = document.querySelector('#click');
    const capss = document.querySelector('#capss');

    if (menuBtn) menuBtn.style.display = 'none';
    if (clickInput) clickInput.style.display = 'none';
    if (capss) capss.style.display = 'none';

    // تطبيق التصميم الجديد على nav
    const nav = document.querySelector('nav');
    if (nav) {
        nav.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        nav.style.position = 'fixed';
        nav.style.top = '0';
        nav.style.left = '0';
        nav.style.zIndex = '999';
        nav.style.display = 'flex';
        nav.style.alignItems = 'center';
        nav.style.justifyContent = 'space-between';
    }
});
</script>

<nav>
<div class='logo2'><img src="css/logooo.png" alt=""></div>

         <!-- الأزرار الأفقية مثل صفحة المستخدمين -->
         <div class="quick-actions">
             <!-- زر الإشعارات -->
             <a href="#" onclick="toggleNotifications(event)" class="quick-btn notifications-btn" title="الإشعارات">
                 <i class="fas fa-bell"></i>
                 <span>الإشعارات</span>
                 <span class="notification-badge-nav" id="notificationBadgeNav" style="display: none;">0</span>
             </a>

             <!-- الأزرار الرئيسية -->
             <a href="home.php" class="quick-btn home-btn" title="الرئيسية">
                 <i class="fa-solid fa-home"></i>
                 <span>الرئيسية</span>
             </a>
             <a href="addstud.php" class="quick-btn" title="إضافة طالب">
                 <i class="fa-solid fa-user-plus"></i>
                 <span>إضافة طالب</span>
             </a>
             <a href="infost.php" class="quick-btn" title="الطلاب">
                 <i class="fa-solid fa-graduation-cap"></i>
                 <span>الطلاب</span>
             </a>
             <a href="info_depit.php" class="quick-btn" title="المصاريف">
                 <i class="fa-solid fa-money-bill"></i>
                 <span>المصاريف</span>
             </a>
             <a href="statistics.php" class="quick-btn" title="الإحصائيات">
                 <i class="fa-solid fa-chart-bar"></i>
                 <span>الإحصائيات</span>
             </a>
             <a href="acounting.php" class="quick-btn" title="الحسابات">
                 <i class="fa-solid fa-calculator"></i>
                 <span>الحسابات</span>
             </a>
             <a href="info_user.php" class="quick-btn" title="المستخدمين">
                 <i class="fa-solid fa-users"></i>
                 <span>المستخدمين</span>
             </a>
             <a href="info_employ.php" class="quick-btn" title="الموظفين">
                 <i class="fa-solid fa-user-tie"></i>
                 <span>الموظفين</span>
             </a>
             <a href="reports.php" class="quick-btn" title="تقارير الطلاب">
                 <i class="fa-solid fa-file-alt"></i>
                 <span>التقارير</span>
             </a>
             <a href="attandec.php" class="quick-btn" title="حضور الطلاب">
                 <i class="fa-solid fa-school"></i>
                 <span>حضور الطلاب</span>
             </a>
             <a href="employee_attendance.php" class="quick-btn" title="حضور الموظفين">
                 <i class="fa-solid fa-user-clock"></i>
                 <span>حضور الموظفين</span>
             </a>
             <a href="manage_needs.php" class="quick-btn" title="الاحتياجات">
                 <i class="fa-solid fa-clipboard-list"></i>
                 <span>الاحتياجات</span>
             </a>
             <a href="student_app_control.php" class="quick-btn student-app-btn" title="تطبيق الطالب">
                 <i class="fa-solid fa-mobile-alt"></i>
                 <span>تطبيق الطالب</span>
             </a>
             <a href="manage_leaves.php" class="quick-btn" title="الإجازات">
                 <i class="fa-solid fa-calendar-times"></i>
                 <span>الإجازات</span>
             </a>
             <a href="addon/bkF.php" class="quick-btn" title="نسخ احتياطي">
                 <i class="fa-solid fa-database"></i>
                 <span>نسخ احتياطي</span>
             </a>
             <a href="about_us.php" class="quick-btn" title="عنا">
                 <i class="fa-solid fa-info-circle"></i>
                 <span>عنا</span>
             </a>
         </div>

         <!-- منطقة المستخدم -->
         <div class="logo">
             <form method="POST" action="" class="user-form">
                 <label class="welcome-label"><?php echo $_SESSION['user']->user_name; ?> مرحبا بك</label>
                 <button class='btn btn-danger' name="exitbtn" type='submit' id='exit_btn'
                         onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                     <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                 </button>
             </form>
         </div>

      </nav>

<!-- إضافة مكون الإشعارات الجديد -->
<?php
$notifications_file = 'notifications_navbar.php';
if (file_exists($notifications_file)) {
    include $notifications_file;
} elseif (file_exists('../Admin/notifications_navbar.php')) {
    include '../Admin/notifications_navbar.php';
}
?>

<style>
/* تأكيد إظهار الأزرار في جميع الصفحات */
.quick-actions {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    flex-wrap: wrap !important;
    gap: 6px !important;
    align-items: center !important;
    margin: 0 10px !important;
    max-width: 70% !important;
    overflow-x: auto !important;
    padding: 5px 0 !important;
    order: 1 !important;
}

.quick-btn {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 4px !important;
    padding: 6px 10px !important;
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    text-decoration: none !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    min-width: 60px !important;
    position: relative !important;
    overflow: hidden !important;
    font-size: 0.8rem !important;
    white-space: nowrap !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.quick-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    background: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    text-decoration: none !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.quick-btn i {
    font-size: 14px !important;
    color: white !important;
    margin-left: 2px !important;
}

.quick-btn span {
    font-size: 11px !important;
    font-weight: 500 !important;
    color: white !important;
    text-align: center !important;
    line-height: 1.2 !important;
}

/* أزرار خاصة بألوان مختلفة */
.quick-btn.home-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
}

.quick-btn.home-btn:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.quick-btn.notifications-btn {
    background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%) !important;
    box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3) !important;
    position: relative !important;
}

.quick-btn.notifications-btn:hover {
    box-shadow: 0 6px 20px rgba(253, 126, 20, 0.4) !important;
}

/* إخفاء القائمة القديمة نهائياً */
nav ul:not(.quick-actions) {
    display: none !important;
}

/* تصميم شريط التنقل */
nav {
    display: flex !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 90px !important;
    width: 100% !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 20px 0 55px !important;
    flex-wrap: wrap !important;
    z-index: 999 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

/* تصميم الشعار */
.logo2 {
    order: 0 !important;
    margin-left: 10px !important;
}

.logo2 img {
    width: 60px !important;
    height: 60px !important;
}

/* تصميم منطقة المستخدم */
.logo {
    order: 2 !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

/* إضافة مساحة للمحتوى تحت الشريط الثابت */
body {
    margin-top: 90px !important;
    padding-top: 20px !important;
}
</style>
