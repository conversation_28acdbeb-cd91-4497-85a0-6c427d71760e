<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أضافة مستخدم</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php";
      include "addon/dbcon.php";?>
   </head>
    <body>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
  
    <form method="POST">
 <div class='contin_user'>

 <div class='input-box'>
        <label for="name"> اسم المتسخدم<label>
        <input type="text" name="user_name" required>
        <label for="password"> الرمز السري  <label>
        <input type="password" name="user_pass" required>
        <label for="sex" >الصلاحية</label>
        <div>
        <select name="role" id='selc'>
            <option value="Admin"> ادمن</option>
            <option value="User"> مدير</option>
            <option value="Mod"> مدقق</option>

        </select>
        </div>
        <button class=btn name="addS" id="23">حفظ </button>
        
</div>
  
   </body>
   <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
    if(icC=="fa fa-circle-check"){
      setInterval(()=>{
        window.location.href="info_user.php"
      },4700)
      
    }else{
    }
}
  </script>
   <?php

  
      $dsn = 'mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8';
    $user = 'kidzrcle_rwda';
    $pass = 'kidzrcle_rwda';

    // Create the PDO instance with UTF-8 settings
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8", // Ensure UTF-8 encoding
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION      // Enable exception mode for better error handling
    ]);

  if(isset($_POST['addS'])){
    $user_name=$_POST['user_name'];
    $chek=$pdo->prepare("SELECT * FROM `users_tb` WHERE user_name = ?");
    $chek->execute([$user_name]);
    $res=$chek->rowCount();

    if($res>0){
        $msg1=" ! انتبه ";
        $msg2="المستخدم الذي ادخلته موجود مسبقآ ";
        $iconC="fa-solid fa-circle-info";
        echo "<script> StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
    }else{
    $user_name=$_POST['user_name'];
    $user_pass=$_POST['user_pass'];
    $role=$_POST['role'];
    if(strlen($user_name)<2){
        $msg1=" ! انتبه ";
        $msg2=" يرجى كتابه اسم مستخدم حرفين على الاقل";
        $iconC="fa-solid fa-circle-info";
        echo "<script> StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
    }elseif(strlen($user_pass<5)){
        $msg1=" ! انتبه ";
        $msg2="يرجى كتابة كلمة مرور 6 ارقام على الاقل";
        $iconC="fa-solid fa-circle-info";
        echo "<script> StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
    }else{
    $query="INSERT INTO users_tb(user_name,user_pass,role)VALUES('$user_name','$user_pass','$role') ";
    $res1=mysqli_query($con,$query);
  
    if($res1){
        $msg1=" ! تمت ";
        $msg2="تم اضافة مستخدم جديد  بنجاح";
        $iconC="fa fa-circle-check";
        echo "<script> StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";

    }
}
}
}
   ?>


</html>