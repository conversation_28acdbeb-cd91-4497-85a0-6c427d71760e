<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 معاينة لوحة التحكم الجديدة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 40px;
            color: white;
            box-shadow: 0 15px 35px rgba(31, 38, 135, 0.2);
        }
        
        .preview-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .preview-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            color: white;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(31, 38, 135, 0.25);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-description {
            opacity: 0.8;
            line-height: 1.6;
        }
        
        .demo-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
        }
        
        .demo-button:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
            box-shadow: 0 15px 35px rgba(31, 38, 135, 0.2);
        }
        
        .demo-button.primary {
            background: linear-gradient(45deg, #007bff, #6610f2);
        }
        
        .demo-button.primary:hover {
            background: linear-gradient(45deg, #0056b3, #520dc2);
        }
        
        .comparison-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            color: white;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .before, .after {
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .before {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .after {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
        
        .tech-specs {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            color: white;
        }
        
        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .spec-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .spec-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }
        
        @keyframes float {
            to {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        @media (max-width: 768px) {
            .preview-title {
                font-size: 2rem;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="preview-container">
        <div class="preview-header">
            <h1 class="preview-title">🎨 لوحة التحكم الجديدة</h1>
            <p class="preview-subtitle">
                تصميم احترافي ومبهر يعكس قوة الذكاء الاصطناعي في التطوير
            </p>
            <div>
                <a href="home.php" class="demo-button primary" target="_blank">
                    <i class="fas fa-eye"></i>
                    مشاهدة اللوحة الجديدة
                </a>
                <a href="#features" class="demo-button">
                    <i class="fas fa-star"></i>
                    استكشاف المميزات
                </a>
            </div>
        </div>

        <div class="features-grid" id="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3 class="feature-title">تصميم عصري ومبهر</h3>
                <p class="feature-description">
                    واجهة مستخدم حديثة مع تأثيرات بصرية متطورة وألوان مريحة للعين
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3 class="feature-title">متجاوب بالكامل</h3>
                <p class="feature-description">
                    يعمل بشكل مثالي على جميع الأجهزة من الهواتف إلى أجهزة الكمبيوتر
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">إحصائيات تفاعلية</h3>
                <p class="feature-description">
                    بطاقات إحصائية متحركة مع عدادات تلقائية وتأثيرات بصرية جذابة
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="feature-title">ساعة متطورة</h3>
                <p class="feature-description">
                    ساعة رقمية وتناظرية مع تصميم أنيق وتحديث مباشر
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <h3 class="feature-title">تنظيم ذكي</h3>
                <p class="feature-description">
                    تنظيم المحتوى في أقسام منطقية مع تدرج أولويات واضح
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3 class="feature-title">أداء سريع</h3>
                <p class="feature-description">
                    تحميل سريع مع تأثيرات انتقالية سلسة وتجربة مستخدم ممتازة
                </p>
            </div>
        </div>

        <div class="comparison-section">
            <h2 style="text-align: center; margin-bottom: 20px;">
                <i class="fas fa-exchange-alt"></i>
                مقارنة التصميم
            </h2>
            <div class="comparison-grid">
                <div class="before">
                    <h4>❌ التصميم السابق</h4>
                    <ul style="text-align: right; list-style: none; padding: 0;">
                        <li>• تصميم بسيط وتقليدي</li>
                        <li>• ألوان محدودة</li>
                        <li>• بطاقات ثابتة</li>
                        <li>• ساعة بسيطة</li>
                        <li>• تنظيم أساسي</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ التصميم الجديد</h4>
                    <ul style="text-align: right; list-style: none; padding: 0;">
                        <li>• تصميم احترافي ومبهر</li>
                        <li>• ألوان متدرجة وجذابة</li>
                        <li>• بطاقات تفاعلية متحركة</li>
                        <li>• ساعة رقمية وتناظرية</li>
                        <li>• تنظيم ذكي ومنطقي</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tech-specs">
            <h2 style="text-align: center; margin-bottom: 20px;">
                <i class="fas fa-cogs"></i>
                المواصفات التقنية
            </h2>
            <div class="specs-grid">
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h4>HTML5 & CSS3</h4>
                    <p>أحدث معايير الويب</p>
                </div>
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fab fa-js-square"></i>
                    </div>
                    <h4>JavaScript ES6+</h4>
                    <p>برمجة تفاعلية متطورة</p>
                </div>
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h4>CSS Grid & Flexbox</h4>
                    <p>تخطيط مرن ومتجاوب</p>
                </div>
                <div class="spec-item">
                    <div class="spec-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h4>CSS Animations</h4>
                    <p>تأثيرات بصرية متطورة</p>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="home.php" class="demo-button primary" target="_blank">
                <i class="fas fa-rocket"></i>
                تجربة اللوحة الآن
            </a>
        </div>
    </div>

    <script>
        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + 'vw';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            particle.style.opacity = Math.random();
            document.getElementById('particles').appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 500);

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add hover effects to cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
