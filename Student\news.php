<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

// جلب الأخبار والإعلانات من قاعدة البيانات
$sql = "SELECT * FROM news_announcements WHERE status = 'نشط' ORDER BY created_date DESC";
$result = mysqli_query($con, $sql);
$news = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $news[] = $row;
    }
}

// إذا لم توجد بيانات في قاعدة البيانات، استخدم بيانات افتراضية
if (empty($news)) {
    $news = [
        [
            'title' => 'بداية العام الدراسي الجديد',
            'content' => 'نرحب بجميع الطلاب في بداية العام الدراسي الجديد. نتمنى لكم عاماً مليئاً بالنجاح والتفوق.',
            'type' => 'إعلان',
            'created_date' => date('Y-m-d H:i:s', strtotime('-2 days')),
            'priority' => 'عالية'
        ],
        [
            'title' => 'تحديث في نظام الحضور',
            'content' => 'تم تحديث نظام الحضور الإلكتروني. يرجى من جميع الطلاب التأكد من تسجيل الحضور يومياً.',
            'type' => 'تحديث',
            'created_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'priority' => 'متوسطة'
        ],
        [
            'title' => 'فعالية ترفيهية قادمة',
            'content' => 'ستقام فعالية ترفيهية للطلاب يوم الجمعة القادم. سيتم الإعلان عن التفاصيل قريباً.',
            'type' => 'فعالية',
            'created_date' => date('Y-m-d H:i:s'),
            'priority' => 'منخفضة'
        ],
        [
            'title' => 'إجراءات السلامة الجديدة',
            'content' => 'تم تطبيق إجراءات سلامة جديدة في المبنى. يرجى الالتزام بجميع التعليمات المعلنة.',
            'type' => 'تنبيه',
            'created_date' => date('Y-m-d H:i:s', strtotime('-3 days')),
            'priority' => 'عالية'
        ],
        [
            'title' => 'تحديث أوقات الدوام',
            'content' => 'تم تعديل أوقات الدوام لتصبح من الساعة 8:00 صباحاً حتى 2:00 ظهراً اعتباراً من الأسبوع القادم.',
            'type' => 'إعلان',
            'created_date' => date('Y-m-d H:i:s', strtotime('-5 days')),
            'priority' => 'عالية'
        ]
    ];
}

// تصنيف الأخبار حسب الأولوية
$high_priority = [];
$medium_priority = [];
$low_priority = [];

foreach ($news as $item) {
    $priority = isset($item['priority']) ? $item['priority'] : 'منخفضة';
    switch ($priority) {
        case 'عالية':
            $high_priority[] = $item;
            break;
        case 'متوسطة':
            $medium_priority[] = $item;
            break;
        default:
            $low_priority[] = $item;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📰 الأخبار - أكاديمية كيدز</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/student-style.css">
    <link rel="icon" href="css/icon.ico">
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container-fluid">
        <div class="news-section">
            <div class="news-header">
                <div class="header-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h1>الأخبار والإعلانات</h1>
                <p>آخر الأخبار والإعلانات المهمة</p>
            </div>
            
            <!-- الأخبار عالية الأولوية -->
            <?php if (!empty($high_priority)): ?>
                <div class="news-category">
                    <div class="category-header high-priority">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h2>إعلانات مهمة</h2>
                    </div>
                    <div class="news-grid">
                        <?php foreach ($high_priority as $item): ?>
                            <div class="news-card high-priority-card">
                                <div class="news-badge high">
                                    <i class="fas fa-star"></i>
                                    مهم
                                </div>
                                <div class="news-content">
                                    <div class="news-type">
                                        <?php echo htmlspecialchars($item['type'] ?? 'إعلان'); ?>
                                    </div>
                                    <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                    <p><?php echo nl2br(htmlspecialchars($item['content'])); ?></p>
                                    <div class="news-date">
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('Y/m/d H:i', strtotime($item['created_date'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- الأخبار متوسطة الأولوية -->
            <?php if (!empty($medium_priority)): ?>
                <div class="news-category">
                    <div class="category-header medium-priority">
                        <i class="fas fa-info-circle"></i>
                        <h2>أخبار عامة</h2>
                    </div>
                    <div class="news-grid">
                        <?php foreach ($medium_priority as $item): ?>
                            <div class="news-card medium-priority-card">
                                <div class="news-badge medium">
                                    <i class="fas fa-info"></i>
                                    عام
                                </div>
                                <div class="news-content">
                                    <div class="news-type">
                                        <?php echo htmlspecialchars($item['type'] ?? 'خبر'); ?>
                                    </div>
                                    <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                    <p><?php echo nl2br(htmlspecialchars($item['content'])); ?></p>
                                    <div class="news-date">
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('Y/m/d H:i', strtotime($item['created_date'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- الأخبار منخفضة الأولوية -->
            <?php if (!empty($low_priority)): ?>
                <div class="news-category">
                    <div class="category-header low-priority">
                        <i class="fas fa-bullhorn"></i>
                        <h2>فعاليات وأنشطة</h2>
                    </div>
                    <div class="news-grid">
                        <?php foreach ($low_priority as $item): ?>
                            <div class="news-card low-priority-card">
                                <div class="news-badge low">
                                    <i class="fas fa-calendar"></i>
                                    فعالية
                                </div>
                                <div class="news-content">
                                    <div class="news-type">
                                        <?php echo htmlspecialchars($item['type'] ?? 'فعالية'); ?>
                                    </div>
                                    <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                    <p><?php echo nl2br(htmlspecialchars($item['content'])); ?></p>
                                    <div class="news-date">
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('Y/m/d H:i', strtotime($item['created_date'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (empty($news)): ?>
                <div class="no-news">
                    <div class="no-news-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3>لا توجد أخبار حالياً</h3>
                    <p>لم يتم نشر أي أخبار أو إعلانات حتى الآن</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.news-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('animate-in');
            });
            
            // تأثير النقر على البطاقات
            $('.news-card').click(function() {
                $(this).toggleClass('expanded');
            });
        });
    </script>
    
    <style>
        .news-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .news-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .news-header h1 {
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .news-header p {
            color: rgba(255,255,255,0.9);
        }
        
        .news-category {
            margin-bottom: 3rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 15px;
            color: white;
            font-weight: bold;
        }
        
        .category-header.high-priority {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .category-header.medium-priority {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .category-header.low-priority {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .category-header h2 {
            margin: 0;
            font-size: 1.3rem;
        }
        
        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .news-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }
        
        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .news-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
        }
        
        .high-priority-card::before {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .medium-priority-card::before {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .low-priority-card::before {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .news-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }
        
        .news-badge.high {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .news-badge.medium {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .news-badge.low {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .news-content {
            margin-top: 2rem;
        }
        
        .news-type {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 0.3rem 0.8rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .news-content h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            line-height: 1.4;
        }
        
        .news-content p {
            color: #34495e;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .news-date {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #ecf0f1;
        }
        
        .news-date i {
            color: #3498db;
        }
        
        .no-news {
            text-align: center;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .no-news-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
        }
        
        .no-news h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .no-news p {
            color: #7f8c8d;
        }
        
        .news-card.expanded {
            transform: scale(1.02);
        }
        
        @media (max-width: 768px) {
            .news-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .news-card {
                padding: 1rem;
            }
            
            .category-header {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }
            
            .news-badge {
                position: static;
                display: inline-block;
                margin-bottom: 1rem;
            }
            
            .news-content {
                margin-top: 0;
            }
        }
    </style>
</body>
</html>
