<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

// جلب الإحصائيات الشاملة
$stats = [
    'total_students' => 0,
    'active_students' => 0,
    'total_news' => 0,
    'total_activities' => 0,
    'total_messages' => 0,
    'pending_messages' => 0,
    'total_leave_requests' => 0,
    'pending_leave_requests' => 0,
    'total_notifications' => 0,
    'sent_notifications' => 0
];

// إحصائيات الطلاب
$result = @mysqli_query($con, "SELECT COUNT(*) as total, SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active FROM stud_tb");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_students'] = $row['total'];
    $stats['active_students'] = $row['active'];
}

// إحصائيات الأخبار
$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM news_announcements");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_news'] = $row['count'];
}

// إحصائيات الأنشطة
$result = @mysqli_query($con, "SELECT COUNT(*) as count FROM app_activities");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_activities'] = $row['count'];
}

// إحصائيات الرسائل
$result = @mysqli_query($con, "SELECT COUNT(*) as total, SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending FROM contact_messages");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_messages'] = $row['total'];
    $stats['pending_messages'] = $row['pending'];
}

// إحصائيات طلبات الإجازة
$result = @mysqli_query($con, "SELECT COUNT(*) as total, SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending FROM leave_requests");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_leave_requests'] = $row['total'];
    $stats['pending_leave_requests'] = $row['pending'];
}

// إحصائيات الإشعارات
$result = @mysqli_query($con, "SELECT COUNT(*) as total, SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent FROM app_notifications");
if ($result && $row = mysqli_fetch_assoc($result)) {
    $stats['total_notifications'] = $row['total'];
    $stats['sent_notifications'] = $row['sent'];
}

// إحصائيات شهرية للأخبار والأنشطة
$monthly_news = [];
$monthly_activities = [];

for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $month_name = date('M Y', strtotime("-$i months"));
    
    // أخبار الشهر
    $result = @mysqli_query($con, "SELECT COUNT(*) as count FROM news_announcements WHERE DATE_FORMAT(created_date, '%Y-%m') = '$month'");
    $monthly_news[$month_name] = $result && $row = mysqli_fetch_assoc($result) ? $row['count'] : 0;
    
    // أنشطة الشهر
    $result = @mysqli_query($con, "SELECT COUNT(*) as count FROM app_activities WHERE DATE_FORMAT(created_at, '%Y-%m') = '$month'");
    $monthly_activities[$month_name] = $result && $row = mysqli_fetch_assoc($result) ? $row['count'] : 0;
}

// أحدث الأنشطة
$recent_activities = @mysqli_query($con, "SELECT 'news' as type, title, created_date as date FROM news_announcements 
                                         UNION ALL 
                                         SELECT 'activity' as type, title, created_at as date FROM app_activities 
                                         UNION ALL 
                                         SELECT 'message' as type, subject as title, created_at as date FROM contact_messages 
                                         ORDER BY date DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 التقارير والإحصائيات - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: #7f8c8d;
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.students { border-left-color: #3498db; }
        .stat-card.news { border-left-color: #2ecc71; }
        .stat-card.activities { border-left-color: #f39c12; }
        .stat-card.messages { border-left-color: #e74c3c; }
        .stat-card.notifications { border-left-color: #9b59b6; }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .stat-card.students .icon { color: #3498db; }
        .stat-card.news .icon { color: #2ecc71; }
        .stat-card.activities .icon { color: #f39c12; }
        .stat-card.messages .icon { color: #e74c3c; }
        .stat-card.notifications .icon { color: #9b59b6; }

        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #7f8c8d;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .stat-card .sub-stat {
            font-size: 0.9rem;
            color: #95a5a6;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chart-card h5 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: bold;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #f1f2f6;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: #f8f9fa;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            color: white;
            font-size: 1.2rem;
        }

        .activity-icon.news { background: #2ecc71; }
        .activity-icon.activity { background: #f39c12; }
        .activity-icon.message { background: #e74c3c; }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .activity-date {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .chart-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>📊 التقارير والإحصائيات</h1>
            <p>نظرة شاملة على أداء النظام والإحصائيات المهمة</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card students">
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="number"><?php echo $stats['total_students']; ?></div>
                <div class="label">إجمالي الطلاب</div>
                <div class="sub-stat">النشطون: <?php echo $stats['active_students']; ?></div>
            </div>

            <div class="stat-card news">
                <div class="icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="number"><?php echo $stats['total_news']; ?></div>
                <div class="label">الأخبار المنشورة</div>
                <div class="sub-stat">هذا الشهر: <?php echo end($monthly_news); ?></div>
            </div>

            <div class="stat-card activities">
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="number"><?php echo $stats['total_activities']; ?></div>
                <div class="label">الأنشطة المضافة</div>
                <div class="sub-stat">هذا الشهر: <?php echo end($monthly_activities); ?></div>
            </div>

            <div class="stat-card messages">
                <div class="icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="number"><?php echo $stats['total_messages']; ?></div>
                <div class="label">رسائل التواصل</div>
                <div class="sub-stat">في الانتظار: <?php echo $stats['pending_messages']; ?></div>
            </div>

            <div class="stat-card messages">
                <div class="icon">
                    <i class="fas fa-calendar-minus"></i>
                </div>
                <div class="number"><?php echo $stats['total_leave_requests']; ?></div>
                <div class="label">طلبات الإجازة</div>
                <div class="sub-stat">في الانتظار: <?php echo $stats['pending_leave_requests']; ?></div>
            </div>

            <div class="stat-card notifications">
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="number"><?php echo $stats['total_notifications']; ?></div>
                <div class="label">الإشعارات</div>
                <div class="sub-stat">المرسلة: <?php echo $stats['sent_notifications']; ?></div>
            </div>
        </div>

        <div class="row">
            <!-- Monthly Chart -->
            <div class="col-md-8">
                <div class="chart-card">
                    <h5><i class="fas fa-chart-line"></i> الإحصائيات الشهرية</h5>
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-md-4">
                <div class="chart-card">
                    <h5><i class="fas fa-clock"></i> آخر الأنشطة</h5>
                    <div class="activity-list">
                        <?php if ($recent_activities && mysqli_num_rows($recent_activities) > 0): ?>
                            <?php while ($activity = mysqli_fetch_assoc($recent_activities)): ?>
                                <div class="activity-item">
                                    <div class="activity-icon <?php echo $activity['type']; ?>">
                                        <?php 
                                        $icons = ['news' => 'fa-newspaper', 'activity' => 'fa-calendar', 'message' => 'fa-comment'];
                                        echo '<i class="fas ' . ($icons[$activity['type']] ?? 'fa-circle') . '"></i>';
                                        ?>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?php echo htmlspecialchars(substr($activity['title'], 0, 30)) . '...'; ?></div>
                                        <div class="activity-date"><?php echo date('Y-m-d H:i', strtotime($activity['date'])); ?></div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-clock" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                                <p style="color: #7f8c8d;">لا توجد أنشطة حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Monthly Chart
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_keys($monthly_news)); ?>,
                datasets: [{
                    label: 'الأخبار',
                    data: <?php echo json_encode(array_values($monthly_news)); ?>,
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'الأنشطة',
                    data: <?php echo json_encode(array_values($monthly_activities)); ?>,
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
