<?php
session_start();

if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

$message = '';
$messageType = '';

// معالجة إضافة صورة جديدة
if (isset($_POST['add_image'])) {
    $image_url = mysqli_real_escape_string($con, trim($_POST['image_url']));
    $caption = mysqli_real_escape_string($con, trim($_POST['caption']));
    $activity_id = isset($_POST['activity_id']) ? intval($_POST['activity_id']) : 0;
    
    if (!empty($image_url) && !empty($caption)) {
        // إنشاء جدول معرض الصور إذا لم يكن موجود
        $create_table = "CREATE TABLE IF NOT EXISTS activity_gallery (
            id INT AUTO_INCREMENT PRIMARY KEY,
            activity_id INT,
            image_url VARCHAR(500) NOT NULL,
            caption TEXT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            uploaded_by INT,
            is_active BOOLEAN DEFAULT TRUE
        )";
        @mysqli_query($con, $create_table);
        
        $insert_query = "INSERT INTO activity_gallery (activity_id, image_url, caption, uploaded_by, is_active) 
                        VALUES ($activity_id, '$image_url', '$caption', {$_SESSION['user']->id_user}, 1)";
        
        if (mysqli_query($con, $insert_query)) {
            $message = 'تم إضافة الصورة بنجاح!';
            $messageType = 'success';
        } else {
            $message = 'حدث خطأ في إضافة الصورة';
            $messageType = 'error';
        }
    }
}

// معالجة حذف صورة
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $image_id = intval($_GET['delete']);
    $delete_query = "UPDATE activity_gallery SET is_active = 0 WHERE id = $image_id";
    
    if (mysqli_query($con, $delete_query)) {
        $message = 'تم حذف الصورة بنجاح!';
        $messageType = 'success';
    }
}

// جلب الصور
$images_query = "SELECT * FROM activity_gallery WHERE is_active = 1 ORDER BY upload_date DESC";
$images_result = mysqli_query($con, $images_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 إدارة معرض الصور - أكاديمية كيدز</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            padding: 2rem 1rem;
        }
        
        .page-header {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .control-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.8rem;
            margin-bottom: 1rem;
        }
        
        .btn-custom {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.8rem 1.5rem;
            font-weight: bold;
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .image-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .image-info {
            padding: 1rem;
        }
        
        .image-caption {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .image-date {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-block;
        }
        
        .alert {
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>📸 إدارة معرض صور الأنشطة</h1>
            <h2 style="color: #3498db;">أكاديمية كيدز</h2>
            <p>إضافة وإدارة صور الأنشطة اليومية</p>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- إضافة صورة جديدة -->
        <div class="control-card">
            <h2 style="color: #2c3e50; margin-bottom: 1.5rem;">
                <i class="fas fa-plus-circle"></i>
                إضافة صورة جديدة
            </h2>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <label>رابط الصورة:</label>
                        <input type="url" name="image_url" class="form-control" 
                               placeholder="https://example.com/image.jpg" required>
                    </div>
                    <div class="col-md-6">
                        <label>وصف الصورة:</label>
                        <input type="text" name="caption" class="form-control" 
                               placeholder="وصف مختصر للصورة" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <label>رقم النشاط (اختياري):</label>
                        <input type="number" name="activity_id" class="form-control" 
                               placeholder="0 للصور العامة">
                    </div>
                </div>
                
                <button type="submit" name="add_image" class="btn-custom">
                    <i class="fas fa-plus"></i> إضافة الصورة
                </button>
            </form>
        </div>
        
        <!-- معرض الصور -->
        <div class="control-card">
            <h2 style="color: #2c3e50; margin-bottom: 1.5rem;">
                <i class="fas fa-images"></i>
                معرض الصور الحالي
            </h2>
            
            <?php if ($images_result && mysqli_num_rows($images_result) > 0): ?>
                <div class="gallery-grid">
                    <?php while ($image = mysqli_fetch_assoc($images_result)): ?>
                        <div class="image-card">
                            <img src="<?php echo htmlspecialchars($image['image_url']); ?>" 
                                 alt="<?php echo htmlspecialchars($image['caption']); ?>"
                                 onerror="this.src='https://via.placeholder.com/300x200?text=صورة+غير+متاحة'">
                            <div class="image-info">
                                <div class="image-caption">
                                    <?php echo htmlspecialchars($image['caption']); ?>
                                </div>
                                <div class="image-date">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('Y/m/d H:i', strtotime($image['upload_date'])); ?>
                                </div>
                                <a href="?delete=<?php echo $image['id']; ?>" 
                                   class="btn-delete"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الصورة؟')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 3rem; color: #7f8c8d;">
                    <i class="fas fa-camera" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                    <h3>لا توجد صور في المعرض</h3>
                    <p>ابدأ بإضافة صور الأنشطة اليومية</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- روابط سريعة -->
        <div class="control-card">
            <h2 style="color: #2c3e50; margin-bottom: 1.5rem;">
                <i class="fas fa-link"></i>
                روابط سريعة
            </h2>
            
            <div class="row">
                <div class="col-md-3">
                    <a href="student_app_control.php" class="btn-custom" style="text-decoration: none; display: block; text-align: center; margin-bottom: 1rem;">
                        <i class="fas fa-cogs"></i> إدارة التطبيق
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../Student/activities.php" target="_blank" class="btn-custom" style="text-decoration: none; display: block; text-align: center; margin-bottom: 1rem;">
                        <i class="fas fa-eye"></i> معاينة الأنشطة
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn-custom" style="text-decoration: none; display: block; text-align: center; margin-bottom: 1rem;">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.min.js"></script>
</body>
</html>
