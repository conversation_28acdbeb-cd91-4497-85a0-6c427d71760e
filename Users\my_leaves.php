<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";
include "notifications_helper.php";

$message = '';
$messageType = '';

// التحقق من رسالة النجاح من URL
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "تم إرسال طلب الإجازة بنجاح";
    $messageType = 'success';
}

// معالجة إرسال طلب إجازة جديد
if(isset($_POST['submit_leave'])){
    try {
        $user_id = $_SESSION['user']->id_user;
        $employee_name = trim($_POST['employee_name']);
        $leave_type = $_POST['leave_type'];
        $leave_details = trim($_POST['leave_details']);
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $request_date = date('Y-m-d');

        // حساب عدد الأيام
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $days_count = $start->diff($end)->days + 1;

        if(empty($employee_name) || empty($leave_details) || empty($start_date) || empty($end_date)) {
            $message = "يرجى ملء جميع الحقول المطلوبة";
            $messageType = 'error';
        } elseif($start_date > $end_date) {
            $message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية";
            $messageType = 'error';
        } else {
            $stmt = $con->prepare("INSERT INTO leave_requests (user_id, employee_name, request_date, leave_type, leave_details, start_date, end_date, days_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("issssssi", $user_id, $employee_name, $request_date, $leave_type, $leave_details, $start_date, $end_date, $days_count);

            if($stmt->execute()) {
                $message = "تم إرسال طلب الإجازة بنجاح";
                $messageType = 'success';

                // الحصول على معرف الطلب الجديد
                $leave_id = $con->insert_id;

                // إرسال إشعار للإدارة والمدقق
                try {
                    addLeaveRequestNotification($con, $_SESSION['user']->user_name, $leave_type, $start_date, $end_date, $leave_id);
                } catch (Exception $e) {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
                }

                // إعادة توجيه لتجنب إعادة الإرسال عند التحديث
                header("Location: my_leaves.php?success=1");
                exit();

            } else {
                $message = "حدث خطأ أثناء إرسال الطلب: " . $con->error;
                $messageType = 'error';
            }
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

$user_id = $_SESSION['user']->id_user;
$query = "SELECT lr.*, u.user_name as response_by_name
          FROM leave_requests lr
          LEFT JOIN users_tb u ON lr.response_by = u.id_user
          WHERE lr.user_id = ?
          ORDER BY lr.created_at DESC";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجازاتي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .leaves-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .leave-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .leave-card:hover {
            transform: translateY(-5px);
        }
        
        .leave-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .employee-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .leave-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
        }
        
        .info-item i {
            color: #667eea;
            width: 20px;
        }
        
        .leave-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .admin-response {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .response-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .new-request-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            border: none;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .new-request-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            color: white;
        }

        .request-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .request-form.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .submit-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }

        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }

        .days-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
            text-align: center;
            font-weight: bold;
            color: #1976d2;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
        
        .leave-type-badge {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .date-range {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="leaves-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-calendar-check"></i>
                الإجازات - عرض وطلب
            </h1>
            <button class="new-request-btn" onclick="toggleRequestForm()">
                <i class="fas fa-plus"></i>
                طلب إجازة جديدة
            </button>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if(!empty($message)): ?>
            <div class="alert alert-<?= $messageType ?>">
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                <?= $message ?>
            </div>
        <?php endif; ?>

        <!-- نموذج طلب إجازة جديدة -->
        <div class="request-form" id="requestForm">
            <h3 style="color: #667eea; margin-bottom: 25px;">
                <i class="fas fa-calendar-alt"></i>
                طلب إجازة جديدة
            </h3>

            <form method="POST" id="leaveForm">
                <div class="form-group">
                    <label class="form-label">اسم الموظف *</label>
                    <input type="text" name="employee_name" class="form-control" placeholder="أدخل اسم الموظف" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">تاريخ البداية *</label>
                        <input type="date" name="start_date" class="form-control" id="startDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تاريخ النهاية *</label>
                        <input type="date" name="end_date" class="form-control" id="endDate" required>
                    </div>
                </div>

                <div id="daysInfo" class="days-info" style="display: none;">
                    عدد أيام الإجازة: <span id="daysCount">0</span> يوم
                </div>

                <div class="form-group">
                    <label class="form-label">نوع الإجازة *</label>
                    <select name="leave_type" class="form-select" required>
                        <option value="">اختر نوع الإجازة</option>
                        <option value="مرضية">مرضية</option>
                        <option value="عرضية">عرضية</option>
                        <option value="طارئة">طارئة</option>
                        <option value="زمنية">زمنية</option>
                        <option value="ظروف أخرى">ظروف أخرى</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">تفاصيل الإجازة *</label>
                    <textarea name="leave_details" class="form-control" rows="5" placeholder="اكتب تفاصيل وأسباب طلب الإجازة..." required></textarea>
                </div>

                <button type="submit" name="submit_leave" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الطلب
                </button>
            </form>
        </div>
        
        <?php if($result->num_rows > 0): ?>
            <?php while($leave = $result->fetch_assoc()): ?>
                <div class="leave-card">
                    <div class="leave-header">
                        <div>
                            <h3 class="employee-name"><?php echo htmlspecialchars($leave['employee_name']); ?></h3>
                            <span class="leave-type-badge"><?php echo $leave['leave_type']; ?></span>
                        </div>
                        <span class="status-badge status-<?php 
                            echo $leave['status'] == 'قيد المراجعة' ? 'pending' : 
                                ($leave['status'] == 'موافق' ? 'approved' : 'rejected'); 
                        ?>">
                            <?php echo $leave['status']; ?>
                        </span>
                    </div>
                    
                    <div class="date-range">
                        <i class="fas fa-calendar"></i>
                        من <?php echo date('Y/m/d', strtotime($leave['start_date'])); ?> 
                        إلى <?php echo date('Y/m/d', strtotime($leave['end_date'])); ?>
                        (<?php echo $leave['days_count']; ?> يوم)
                    </div>
                    
                    <div class="leave-info">
                        <div class="info-item">
                            <i class="fas fa-calendar-plus"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($leave['request_date'])); ?></span>
                        </div>
                        <?php if($leave['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($leave['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="leave-details">
                        <strong>تفاصيل الإجازة:</strong><br>
                        <?php echo nl2br(htmlspecialchars($leave['leave_details'])); ?>
                    </div>
                    
                    <?php if($leave['admin_response']): ?>
                        <div class="admin-response">
                            <div class="response-header">
                                <i class="fas fa-reply"></i>
                                رد الإدارة:
                                <?php if($leave['response_by_name']): ?>
                                    (بواسطة: <?php echo htmlspecialchars($leave['response_by_name']); ?>)
                                <?php endif; ?>
                            </div>
                            <?php echo nl2br(htmlspecialchars($leave['admin_response'])); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="leave-card">
                <div class="empty-state">
                    <i class="fas fa-calendar-alt"></i>
                    <h3>لا توجد طلبات إجازة</h3>
                    <p>لم تقم بإرسال أي طلبات إجازة بعد</p>
                    <button class="new-request-btn" onclick="toggleRequestForm()">
                        <i class="fas fa-plus"></i>
                        إرسال طلب إجازة
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function toggleRequestForm() {
            const form = document.getElementById('requestForm');
            const button = document.querySelector('.new-request-btn');

            if (form.classList.contains('show')) {
                form.classList.remove('show');
                button.innerHTML = '<i class="fas fa-plus"></i> طلب إجازة جديدة';
            } else {
                form.classList.add('show');
                button.innerHTML = '<i class="fas fa-minus"></i> إخفاء النموذج';
                form.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function calculateDays() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const daysInfo = document.getElementById('daysInfo');
            const daysCount = document.getElementById('daysCount');

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);

                if (end >= start) {
                    const timeDiff = end.getTime() - start.getTime();
                    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

                    daysCount.textContent = daysDiff;
                    daysInfo.style.display = 'block';
                } else {
                    daysInfo.style.display = 'none';
                }
            } else {
                daysInfo.style.display = 'none';
            }
        }

        // إضافة مستمعي الأحداث لحساب الأيام
        document.addEventListener('DOMContentLoaded', function() {
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');

            if (startDate && endDate) {
                startDate.addEventListener('change', calculateDays);
                endDate.addEventListener('change', calculateDays);
            }
        });

        // إظهار النموذج تلقائياً إذا كان هناك رسالة خطأ
        <?php if(!empty($message) && $messageType === 'error'): ?>
            document.addEventListener('DOMContentLoaded', function() {
                toggleRequestForm();
            });
        <?php endif; ?>
    </script>
</body>
</html>
