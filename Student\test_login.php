<?php
include "addon/dbcon.php";

echo "<h2>اختبار تسجيل الدخول للطلاب</h2>";

// اختبار الاتصال بقاعدة البيانات
if ($con) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
    
    // جلب بعض الطلاب للاختبار
    $sql = "SELECT s.id, s.name, s.id_note, s.p_phone, s.datein 
            FROM stud_tb s 
            LIMIT 5";
    $result = mysqli_query($con, $sql);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<h3>بيانات الطلاب المتاحة للاختبار:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>
                <th>رقم الطالب</th>
                <th>الاسم</th>
                <th>رقم الوصل</th>
                <th>رقم الهاتف</th>
                <th>تاريخ التسجيل</th>
                <th>كلمات المرور المتاحة</th>
              </tr>";
        
        while ($student = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $student['id'] . "</td>";
            echo "<td>" . $student['name'] . "</td>";
            echo "<td>" . ($student['id_note'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($student['p_phone'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($student['datein'] ?? 'غير محدد') . "</td>";
            echo "<td>";
            echo "• " . ($student['p_phone'] ?? 'غير محدد') . "<br>";
            echo "• " . ($student['datein'] ?? 'غير محدد') . "<br>";
            echo "• 123456<br>";
            echo "• " . $student['id'] . "<br>";
            echo "• " . $student['name'];
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<br><h3>تعليمات الاختبار:</h3>";
        echo "<ol>";
        echo "<li>اذهب إلى <a href='login.php'>صفحة تسجيل الدخول</a></li>";
        echo "<li>استخدم أي رقم طالب من الجدول أعلاه</li>";
        echo "<li>استخدم أي من كلمات المرور المذكورة</li>";
        echo "<li>اضغط تسجيل الدخول</li>";
        echo "</ol>";
        
    } else {
        echo "<p style='color: red;'>❌ لا توجد بيانات طلاب في قاعدة البيانات</p>";
        echo "<p>يرجى التأكد من وجود بيانات في جدول stud_tb</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

// اختبار الجلسة
session_start();
if (isset($_SESSION['student'])) {
    echo "<br><h3 style='color: green;'>✅ تم تسجيل الدخول بنجاح!</h3>";
    echo "<p><strong>اسم الطالب:</strong> " . $_SESSION['student']->name . "</p>";
    echo "<p><strong>رقم الطالب:</strong> " . $_SESSION['student']->id . "</p>";
    echo "<p><a href='index.php'>الذهاب إلى الصفحة الرئيسية</a></p>";
    echo "<p><a href='logout.php'>تسجيل الخروج</a></p>";
} else {
    echo "<br><h3>لم يتم تسجيل الدخول بعد</h3>";
}
?>

<style>
    body {
        font-family: Arial, sans-serif;
        direction: rtl;
        padding: 20px;
        background: #f5f5f5;
    }
    
    table {
        background: white;
        margin: 10px 0;
    }
    
    th, td {
        padding: 8px;
        text-align: center;
        border: 1px solid #ddd;
    }
    
    th {
        background: #007bff;
        color: white;
    }
    
    a {
        color: #007bff;
        text-decoration: none;
    }
    
    a:hover {
        text-decoration: underline;
    }
</style>
