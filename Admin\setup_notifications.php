<?php
session_start();

// التحقق من صلاحيات الأدمن
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

// الاتصال بقاعدة البيانات
$user = 'kidzrcle_rwda';
$pass = 'kidzrcle_rwda';
$con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
$con->set_charset("utf8");

if ($con->connect_error) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $con->connect_error);
}

echo "<h2>إعداد نظام الإشعارات</h2>";

// قراءة وتنفيذ ملف SQL
$sql_file = 'create_notifications_system.sql';
if (file_exists($sql_file)) {
    $sql_content = file_get_contents($sql_file);
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(DELIMITER|--)/i', $query)) {
            try {
                if (mysqli_query($con, $query)) {
                    echo "<p style='color: green;'>✅ تم تنفيذ الاستعلام بنجاح</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ تحذير: " . mysqli_error($con) . "</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ تحذير: " . $e->getMessage() . "</p>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>❌ ملف SQL غير موجود</p>";
}

// التحقق من وجود الجداول
echo "<h3>التحقق من الجداول:</h3>";

$tables = ['notifications', 'notification_settings'];
foreach ($tables as $table) {
    $check = mysqli_query($con, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($check) > 0) {
        echo "<p style='color: green;'>✅ جدول $table موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
    }
}

// إنشاء إشعارات تجريبية
echo "<h3>إنشاء إشعارات تجريبية:</h3>";

// التحقق من وجود جدول الإشعارات
$check_notifications = mysqli_query($con, "SHOW TABLES LIKE 'notifications'");
if (mysqli_num_rows($check_notifications) > 0) {
    // إضافة إشعار تجريبي للأدمن
    $admin_id = $_SESSION['user']->id_user;
    $test_notification = "INSERT IGNORE INTO notifications (user_id, title, message, type) VALUES (?, 'مرحباً بك في نظام الإشعارات', 'تم تفعيل نظام الإشعارات بنجاح! ستتلقى إشعارات عند تقديم طلبات جديدة.', 'general')";
    $stmt = $con->prepare($test_notification);
    $stmt->bind_param("i", $admin_id);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ تم إنشاء إشعار تجريبي</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ الإشعار التجريبي موجود مسبقاً</p>";
    }
}

// إحصائيات النظام
echo "<h3>إحصائيات النظام:</h3>";

$stats_queries = [
    'عدد المستخدمين' => "SELECT COUNT(*) as count FROM users_tb",
    'عدد الإشعارات' => "SELECT COUNT(*) as count FROM notifications",
    'الإشعارات غير المقروءة' => "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0",
    'طلبات الإجازة المعلقة' => "SELECT COUNT(*) as count FROM leave_requests WHERE status = 'قيد المراجعة'",
    'طلبات الاحتياج المعلقة' => "SELECT COUNT(*) as count FROM needs_requests WHERE status = 'قيد المراجعة'"
];

foreach ($stats_queries as $label => $query) {
    $result = mysqli_query($con, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>📊 $label: <strong>{$row['count']}</strong></p>";
    }
}

echo "<h3>الخطوات التالية:</h3>";
echo "<ul>";
echo "<li>✅ تم إعداد نظام الإشعارات بنجاح</li>";
echo "<li>🔔 ستظهر الإشعارات في شريط التنقل العلوي</li>";
echo "<li>📱 سيتم إرسال إشعارات تلقائية عند تقديم الطلبات</li>";
echo "<li>👥 جميع المدراء والمدققين سيتلقون إشعارات الطلبات الجديدة</li>";
echo "<li>📬 المستخدمون سيتلقون إشعارات عند الرد على طلباتهم</li>";
echo "</ul>";

echo "<p><a href='home.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى الصفحة الرئيسية</a></p>";

$con->close();
?>
