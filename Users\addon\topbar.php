<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
       
    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      // معالجة تسجيل الخروج
      if(isset($_POST['exitbtn'])){
        // تدمير الجلسة
        session_unset();
        session_destroy();
        // إعادة التوجيه لصفحة تسجيل الدخول
        header("location:../login.php");
        exit();
      }
    

?>


<nav>
<div class='logo2'><img src="../Admin/css/logooo.png" alt=""></div>

         <!-- الأزرار المطلوبة فقط -->
         <div class="quick-actions">
             <!-- الأزرار الأساسية بالترتيب المطلوب -->
             <a href="home.php" class="quick-btn home-btn" title="الرئيسية">
                 <i class="fa-solid fa-home"></i>
                 <span>الرئيسية</span>
             </a>
             <a href="addstud.php" class="quick-btn" title="اضافة طالب">
                 <i class="fa-solid fa-user-plus"></i>
                 <span>اضافة طالب</span>
             </a>
             <a href="infost.php" class="quick-btn" title="الطلاب">
                 <i class="fa-solid fa-graduation-cap"></i>
                 <span>الطلاب</span>
             </a>
             <a href="employee_attendance.php" class="quick-btn" title="حضور الموظفين">
                 <i class="fa-solid fa-user-clock"></i>
                 <span>حضور الموظفين</span>
             </a>
             <a href="attandec.php" class="quick-btn" title="حضور الطلاب">
                 <i class="fa-solid fa-school"></i>
                 <span>حضور الطلاب</span>
             </a>
             <a href="info_depit.php" class="quick-btn" title="المصاريف">
                 <i class="fa-solid fa-money-bill-transfer"></i>
                 <span>المصاريف</span>
             </a>
             <a href="info_employ.php" class="quick-btn" title="الموظفين">
                 <i class="fa-solid fa-user-group"></i>
                 <span>الموظفين</span>
             </a>
             <a href="about_us.php" class="quick-btn about-btn" title="حول النظام">
                 <i class="fa-solid fa-info-circle"></i>
                 <span>حول النظام</span>
             </a>
             <a href="my_needs.php" class="quick-btn need-btn" title="الاحتياجات - عرض وطلب">
                 <i class="fa-solid fa-clipboard-list"></i>
                 <span>الاحتياجات</span>
             </a>
             <a href="my_leaves.php" class="quick-btn leave-btn" title="الإجازات - عرض وطلب">
                 <i class="fa-solid fa-calendar-check"></i>
                 <span>الإجازات</span>
             </a>
         </div>

         <div class="logo">
         <!-- إضافة الإشعارات -->
         <?php include '../Users/<USER>'; ?>

         <form method="POST" action="" style="display: inline-block;" onsubmit="return confirmLogout()">
             <button class='btn btn-danger mb-1' name="exitbtn" type='submit' id='exit_btn'>
                 <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
             </button>
         </form>

         <script>
         function confirmLogout() {
             return confirm('هل أنت متأكد من تسجيل الخروج؟');
         }
         </script>
         <label style="margin-right: 10px; color: #333; font-weight: bold;">
             مرحبا بك <?php echo $_SESSION['user']->user_name; ?>
         </label>

         </div>
         <!-- تم حذف القائمة الجانبية - جميع الأزرار متاحة في الأعلى -->
      </nav>
