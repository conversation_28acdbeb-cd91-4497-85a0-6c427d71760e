<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

$con = mysqli_connect("localhost", "kidzrcle_rwda", "kidzrcle_rwda", "kidzrcle_rwda");
mysqli_set_charset($con, "utf8");

if (!$con) {
    die("Connection failed: " . mysqli_connect_error());
}

// Get statistics
$revenue = 0;
$expenses = 0;
$salaries = 0;

$r = mysqli_query($con, "SELECT SUM(cash_stud) as total FROM stud_pay WHERE cash_stud > 0");
if ($r && $row = mysqli_fetch_assoc($r)) {
    $revenue = $row['total'] ? $row['total'] : 0;
}

$e = mysqli_query($con, "SELECT SUM(depit_cash) as total FROM depit_tb WHERE depit_cash > 0");
if ($e && $row = mysqli_fetch_assoc($e)) {
    $expenses = $row['total'] ? $row['total'] : 0;
}

$s = mysqli_query($con, "SELECT SUM(salary) as total FROM employ_tb WHERE salary > 0");
if ($s && $row = mysqli_fetch_assoc($s)) {
    $salaries = $row['total'] ? $row['total'] : 0;
}

$profit = $revenue - $expenses - $salaries;

// Get users
$users = [];
$u = mysqli_query($con, "SELECT DISTINCT user_name FROM users_tb WHERE role = 'User'");
if ($u) {
    while ($row = mysqli_fetch_assoc($u)) {
        $users[] = $row['user_name'];
    }
}

// Get data
$data = [];

// Revenue data
$rev = mysqli_query($con, "SELECT 'إيراد' as type, CONCAT('دفعة: ', COALESCE(st.name, 'غير محدد')) as desc_text, sp.cash_stud as amount, sp.datein as date_field, COALESCE(u.user_name, 'غير محدد') as user_name FROM stud_pay sp LEFT JOIN stud_tb st ON sp.id_stud = st.id LEFT JOIN users_tb u ON st.userID = u.id_user WHERE sp.cash_stud > 0 ORDER BY sp.datein DESC LIMIT 100");
if ($rev) {
    while ($row = mysqli_fetch_assoc($rev)) {
        $data[] = $row;
    }
}

// Expense data
$exp = mysqli_query($con, "SELECT 'مصروف' as type, COALESCE(d.depit_note, 'مصروف') as desc_text, d.depit_cash as amount, d.depit_date as date_field, COALESCE(u.user_name, 'غير محدد') as user_name FROM depit_tb d LEFT JOIN users_tb u ON d.userID = u.id_user WHERE d.depit_cash > 0 ORDER BY d.depit_date DESC LIMIT 100");
if ($exp) {
    while ($row = mysqli_fetch_assoc($exp)) {
        $data[] = $row;
    }
}

// Salary data
$sal = mysqli_query($con, "SELECT 'راتب' as type, CONCAT('راتب: ', COALESCE(e.f_name, 'غير محدد')) as desc_text, e.salary as amount, e.date_start as date_field, COALESCE(u.user_name, 'غير محدد') as user_name FROM employ_tb e LEFT JOIN users_tb u ON e.userID = u.id_user WHERE e.salary > 0 ORDER BY e.date_start DESC LIMIT 100");
if ($sal) {
    while ($row = mysqli_fetch_assoc($sal)) {
        $data[] = $row;
    }
}

// Sort data
usort($data, function($a, $b) {
    return strtotime($b['date_field']) - strtotime($a['date_field']);
});

// Apply filters
$user_filter = isset($_GET['user']) ? $_GET['user'] : '';
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';

$filtered = [];
foreach ($data as $item) {
    $show = true;
    if ($user_filter && stripos($item['user_name'], $user_filter) === false) $show = false;
    if ($type_filter && stripos($item['type'], $type_filter) === false) $show = false;
    if ($show) $filtered[] = $item;
}

$display = !empty($filtered) ? $filtered : $data;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        body { background: #f8f9fa; font-family: Arial; }
        .container { max-width: 1200px; margin: 20px auto; padding: 20px; }
        .card { margin-bottom: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card { text-align: center; padding: 20px; }
        .stat-value { font-size: 2rem; font-weight: bold; }
        .revenue { border-left: 4px solid #28a745; }
        .expense { border-left: 4px solid #dc3545; }
        .salary { border-left: 4px solid #ffc107; }
        .profit { border-left: 4px solid #007bff; }
        .table th { background: #343a40; color: white; }
        .badge-revenue { background: #28a745; }
        .badge-expense { background: #dc3545; }
        .badge-salary { background: #ffc107; color: #000; }
    </style>
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h2><i class="fas fa-calculator"></i> نظام المحاسبة</h2>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="card stat-card revenue">
                    <h5>الإيرادات</h5>
                    <div class="stat-value text-success"><?php echo number_format($revenue); ?></div>
                    <small>دينار عراقي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card expense">
                    <h5>المصروفات</h5>
                    <div class="stat-value text-danger"><?php echo number_format($expenses); ?></div>
                    <small>دينار عراقي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card salary">
                    <h5>الرواتب</h5>
                    <div class="stat-value text-warning"><?php echo number_format($salaries); ?></div>
                    <small>دينار عراقي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card profit">
                    <h5>صافي الربح</h5>
                    <div class="stat-value <?php echo $profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                        <?php echo number_format($profit); ?>
                    </div>
                    <small>دينار عراقي</small>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>فلاتر البحث</h5>
            </div>
            <div class="card-body">
                <form method="GET">
                    <div class="row">
                        <div class="col-md-4">
                            <select name="user" class="form-control">
                                <option value="">جميع المستخدمين</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user; ?>" <?php echo $user_filter == $user ? 'selected' : ''; ?>>
                                        <?php echo $user; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select name="type" class="form-control">
                                <option value="">جميع الأنواع</option>
                                <option value="إيراد" <?php echo $type_filter == 'إيراد' ? 'selected' : ''; ?>>الإيرادات</option>
                                <option value="مصروف" <?php echo $type_filter == 'مصروف' ? 'selected' : ''; ?>>المصروفات</option>
                                <option value="راتب" <?php echo $type_filter == 'راتب' ? 'selected' : ''; ?>>الرواتب</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">بحث</button>
                            <a href="?" class="btn btn-secondary">مسح</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>المعاملات المالية (<?php echo count($display); ?> معاملة)</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($display)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($display as $item): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($item['date_field'])); ?></td>
                                        <td>
                                            <?php if ($item['type'] == 'إيراد'): ?>
                                                <span class="badge badge-revenue">إيراد</span>
                                            <?php elseif ($item['type'] == 'مصروف'): ?>
                                                <span class="badge badge-expense">مصروف</span>
                                            <?php else: ?>
                                                <span class="badge badge-salary">راتب</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($item['desc_text']); ?></td>
                                        <td class="<?php echo $item['type'] == 'إيراد' ? 'text-success' : 'text-danger'; ?>">
                                            <strong><?php echo number_format($item['amount']); ?> د.ع</strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($item['user_name']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <h5>لا توجد معاملات</h5>
                        <p>لم يتم العثور على معاملات مالية</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
<?php mysqli_close($con); ?>
