<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}

include "addon/dbcon.php";

// تعيين الترميز
mysqli_set_charset($con, "utf8");

$student = $_SESSION['student'];
$message = '';
$messageType = '';

// معالجة إرسال طلب الإجازة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $leave_type = trim($_POST['leave_type']);
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    $reason = trim($_POST['reason']);
    
    if (!empty($leave_type) && !empty($start_date) && !empty($end_date) && !empty($reason)) {
        // التحقق من صحة التواريخ
        if (strtotime($start_date) <= strtotime($end_date) && strtotime($start_date) >= strtotime(date('Y-m-d'))) {
            try {
                // إنشاء جدول إجازات الطلاب إذا لم يكن موجوداً
                $create_table_sql = "CREATE TABLE IF NOT EXISTS `student_leaves` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `student_id` int(11) NOT NULL,
                  `student_name` varchar(255) NOT NULL,
                  `leave_type` varchar(100) NOT NULL,
                  `start_date` date NOT NULL,
                  `end_date` date NOT NULL,
                  `reason` text NOT NULL,
                  `request_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `status` varchar(50) NOT NULL DEFAULT 'قيد المراجعة',
                  `admin_response` text,
                  `response_by` int(11),
                  `response_date` datetime,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                mysqli_query($con, $create_table_sql);

                // إدراج الطلب في جدول إجازات الطلاب بطريقة مبسطة
                $sql = "INSERT INTO student_leaves (student_id, student_name, leave_type, start_date, end_date, reason, request_date, status)
                        VALUES ('$student->id', '$student->name', '$leave_type', '$start_date', '$end_date', '$reason', NOW(), 'قيد المراجعة')";

                if (mysqli_query($con, $sql)) {
                    // إرسال إلى جدول الإجازات العام للأدمن (مع معالجة الأخطاء)
                    try {
                        // التحقق من وجود جدول leave_requests
                        $check_table = "SHOW TABLES LIKE 'leave_requests'";
                        $table_result = mysqli_query($con, $check_table);

                        if (mysqli_num_rows($table_result) > 0) {
                            // التحقق من وجود عمود student_request
                            $check_column = "SHOW COLUMNS FROM leave_requests LIKE 'student_request'";
                            $column_result = mysqli_query($con, $check_column);

                            if (mysqli_num_rows($column_result) == 0) {
                                // إضافة العمود إذا لم يكن موجوداً
                                $add_column = "ALTER TABLE leave_requests ADD COLUMN student_request tinyint(1) DEFAULT 0";
                                mysqli_query($con, $add_column);
                            }

                            // حساب عدد الأيام
                            $days_count = (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) + 1;

                            // إدراج في جدول الإجازات العام
                            $sql2 = "INSERT INTO leave_requests (user_id, employee_name, request_date, leave_type, leave_details, start_date, end_date, days_count, status, student_request)
                                     VALUES ('$student->id', '$student->name', CURDATE(), '$leave_type', '$reason', '$start_date', '$end_date', $days_count, 'قيد المراجعة', 1)";
                            mysqli_query($con, $sql2);
                        }

                    } catch (Exception $e) {
                        // إذا فشل إرسال للجدول العام، لا مشكلة - الطلب محفوظ في جدول الطلاب
                    }

                    // محاولة إرسال الإشعارات (اختياري)
                    try {
                        // إنشاء جدول الإشعارات إذا لم يكن موجوداً
                        $create_notifications_sql = "CREATE TABLE IF NOT EXISTS `notifications` (
                          `id` int(11) NOT NULL AUTO_INCREMENT,
                          `user_role` varchar(50) NOT NULL,
                          `user_id` int(11) DEFAULT NULL,
                          `message` text NOT NULL,
                          `type` varchar(100) DEFAULT 'general',
                          `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          `is_read` tinyint(1) DEFAULT 0,
                          PRIMARY KEY (`id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                        mysqli_query($con, $create_notifications_sql);

                        $notification_message = "طلب إجازة جديد من الطالب: " . $student->name . " (" . $leave_type . ")";

                        // إشعار للأدمن
                        $sql3 = "INSERT INTO notifications (user_role, message, type, created_date, is_read)
                                 VALUES ('Admin', '$notification_message', 'leave_request', NOW(), 0)";
                        mysqli_query($con, $sql3);

                        // إشعار للمدقق
                        $sql4 = "INSERT INTO notifications (user_role, message, type, created_date, is_read)
                                 VALUES ('Mod', '$notification_message', 'leave_request', NOW(), 0)";
                        mysqli_query($con, $sql4);

                    } catch (Exception $e) {
                        // إذا فشل إرسال الإشعارات، لا مشكلة
                    }

                    $message = 'تم إرسال طلب الإجازة بنجاح! ✅ سيتم مراجعته من قبل الإدارة وستصلك رسالة بالرد قريباً.';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إرسال الطلب: ' . mysqli_error($con);
                    $messageType = 'error';
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ في النظام: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى التأكد من صحة التواريخ المدخلة.';
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى ملء جميع الحقول المطلوبة.';
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب إجازة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/student-style.css">
    <link rel="icon" href="css/icon.ico">
</head>
<body>
    <?php include "addon/topbar.php"; ?>
    
    <div class="container-fluid">
        <div class="request-section">
            <div class="request-card">
                <div class="request-header">
                    <div class="header-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <h1>طلب إجازة جديد</h1>
                    <p>املأ النموذج أدناه لتقديم طلب إجازة</p>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-custom">
                        <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="request-form">
                    <div class="form-group">
                        <label for="leave_type">
                            <i class="fas fa-tag"></i>
                            نوع الإجازة
                        </label>
                        <select id="leave_type" name="leave_type" class="form-control" required>
                            <option value="">اختر نوع الإجازة</option>
                            <option value="مرضية">إجازة مرضية</option>
                            <option value="طارئة">إجازة طارئة</option>
                            <option value="عائلية">إجازة عائلية</option>
                            <option value="شخصية">إجازة شخصية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start_date">
                                <i class="fas fa-calendar-alt"></i>
                                تاريخ البداية
                            </label>
                            <input type="date" id="start_date" name="start_date" class="form-control" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="end_date">
                                <i class="fas fa-calendar-check"></i>
                                تاريخ النهاية
                            </label>
                            <input type="date" id="end_date" name="end_date" class="form-control" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="reason">
                            <i class="fas fa-comment-alt"></i>
                            سبب الإجازة
                        </label>
                        <textarea id="reason" name="reason" class="form-control" rows="4" 
                                  placeholder="اكتب سبب طلب الإجازة بالتفصيل..." required></textarea>
                        <small class="form-text">يرجى كتابة سبب واضح ومفصل للإجازة</small>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الطلب
                        </button>
                        <a href="index.php" class="btn-cancel">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // تحديث تاريخ النهاية عند تغيير تاريخ البداية
            $('#start_date').change(function() {
                $('#end_date').attr('min', $(this).val());
            });
            
            // تأثير بصري للنموذج
            $('.request-card').addClass('animate-in');
            
            // إخفاء الرسائل تلقائياً
            setTimeout(function() {
                $('.alert-custom').fadeOut();
            }, 5000);
        });
    </script>
    
    <style>
        .request-section {
            padding: 2rem 1rem;
            min-height: calc(100vh - 80px);
        }
        
        .request-card {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .request-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .request-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .request-header p {
            color: #7f8c8d;
        }
        
        .request-form .form-group {
            margin-bottom: 1.5rem;
        }
        
        .request-form label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .request-form label i {
            margin-left: 0.5rem;
            color: #3498db;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .btn-submit, .btn-cancel {
            padding: 0.8rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-submit {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        
        .btn-cancel {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
        }
        
        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .alert-custom {
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .request-card {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .btn-submit, .btn-cancel {
                width: 100%;
            }
        }
    </style>
</body>
</html>
