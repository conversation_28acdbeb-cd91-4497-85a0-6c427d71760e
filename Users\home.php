<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>

<?php

$user='kidzrcle_rwda';
$pass='kidzrcle_rwda';
$con= new mysqli("localhost",$user,$pass,'kidzrcle_rwda');
$con->set_charset("utf8");
$sql="SELECT * FROM stud_tb WHERE userID={$_SESSION['user']->id_user}";
$getUser=mysqli_query($con,$sql);
$datenow=date('Y-m-d');
$Date=date('Y-m-d', strtotime($datenow. ' + 10 days'));
$Date2=date('Y-m-d', strtotime($datenow. ' + 1 days'));
$Date3=date('Y-m-d', strtotime($datenow. ' + 30 days'));
$Date4=date('Y-m-d', strtotime($datenow. ' + 5 days'));
$Date5=date('Y-m-d', strtotime($datenow. ' + 365 days'));
$sql="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.id_user={$_SESSION['user']->id_user}";
$res=mysqli_query($con,$sql);
$sql="SELECT * FROM stud_pay,stud_tb WHERE DATE(date_exp) <= '$datenow' AND stud_tb.userID={$_SESSION['user']->id_user} AND stud_pay.id_stud=stud_tb.id";
$res2=mysqli_query($con,$sql);
$sql="SELECT * FROM stud_pay,stud_tb WHERE DATE(date_exp) BETWEEN '$Date2' AND '$Date' AND stud_tb.userID={$_SESSION['user']->id_user} AND stud_pay.id_stud=stud_tb.id";
$res3=mysqli_query($con,$sql);
$sql="SELECT * FROM `stud_tb` WHERE catg='روضة' AND userID={$_SESSION['user']->id_user}";
$res4=mysqli_query($con,$sql);
$sql="SELECT * FROM `stud_tb` WHERE catg='حضانة' AND userID={$_SESSION['user']->id_user}";
$res5=mysqli_query($con,$sql);
$sql="SELECT * FROM `depit_tb` WHERE userID={$_SESSION['user']->id_user} ";
$res6=mysqli_query($con,$sql);
$sql="SELECT * FROM `stud_tb` WHERE catg='تمهيدي'  AND userID={$_SESSION['user']->id_user} ";
$res7=mysqli_query($con,$sql);

$sql="SELECT * FROM `stud_tb` WHERE catg='تحضيري' AND userID={$_SESSION['user']->id_user} ";
$res8=mysqli_query($con,$sql);

$sql="SELECT * FROM `depit_tb` WHERE userID={$_SESSION['user']->id_user}";
$res9=mysqli_query($con,$sql);









?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 الرئيسية - أكاديمية الأطفال</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/amazing-effects.css">
    <link rel="icon" href="css/icon.ico">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Custom Styles for Amazing Design -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            --gold-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
            --purple-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --orange-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --blue-gradient: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"><animate attributeName="cx" values="200;800;200" dur="20s" repeatCount="indefinite"/></circle><circle cx="800" cy="300" r="150" fill="url(%23a)"><animate attributeName="cy" values="300;700;300" dur="25s" repeatCount="indefinite"/></circle><circle cx="400" cy="600" r="80" fill="url(%23a)"><animate attributeName="r" values="80;120;80" dur="15s" repeatCount="indefinite"/></circle></svg>');
            pointer-events: none;
            z-index: -1;
        }

        /* Hero Section */
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            margin: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Clock */
        .clock-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        .rect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 50%;
            padding: 30px;
            box-shadow:
                0 0 60px rgba(255, 255, 255, 0.2),
                inset 0 0 60px rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .rect::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: conic-gradient(from 0deg, #667eea, #764ba2, #667eea);
            border-radius: 50%;
            z-index: -1;
            animation: borderRotate 3s linear infinite;
        }

        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .cir {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
            box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.2);
        }

        .brand {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 700;
            font-size: 14px;
            color: white;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            z-index: 10;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow:
                0 25px 80px rgba(0, 0, 0, 0.2),
                0 0 60px rgba(255, 255, 255, 0.3);
        }

        .stat-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
            animation: pulse 2s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: white;
            margin: 15px 0;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .stat-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 10px;
        }

        .stat-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Gradient Variations */
        .stat-card:nth-child(1) { background: var(--success-gradient); }
        .stat-card:nth-child(2) { background: var(--warning-gradient); }
        .stat-card:nth-child(3) { background: var(--info-gradient); }
        .stat-card:nth-child(4) { background: var(--dark-gradient); }
        .stat-card:nth-child(5) { background: var(--gold-gradient); }
        .stat-card:nth-child(6) { background: var(--purple-gradient); }
        .stat-card:nth-child(7) { background: var(--orange-gradient); }
        .stat-card:nth-child(8) { background: var(--blue-gradient); }
        .stat-card:nth-child(9) { background: var(--primary-gradient); }

        /* Footer */
        .footer_p {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 50px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            /* إخفاء النص الترحيبي والساعة في الموبايل */
            .hero-section {
                display: none !important;
            }

            .hero-title { font-size: 2rem; }
            .hero-subtitle { font-size: 1rem; }
            .stats-grid {
                grid-template-columns: 1fr;
                padding: 10px;
                margin-top: 20px; /* إضافة مسافة من الأعلى */
            }
            .stat-card { padding: 20px; }
            .stat-number { font-size: 2rem; }

            /* تحسين المسافات */
            body {
                padding-top: 0;
            }
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <?php include "addon/topbar.php" ?>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">
            <i class="fas fa-graduation-cap"></i>
           مرحباً بك في أكاديمية كيدز  
        </h1>
        <p class="hero-subtitle">
            نظام إدارة متطور وشامل لمتابعة الطلاب والموظفين والمصاريف
        </p>

        <!-- Enhanced Clock -->
        <div class="clock-container">
            <div class="rect" id="clock">
                <div class="cir">
                    <div class="inner_cir">
                        <div class="brand">KIDS ACADEMY</div>
                        <div class="center"></div>
                        <div id="hr"></div>
                        <div id="min"></div>
                        <div id="sec"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <!-- Stat Card 1: Active Students -->
        <a href="new_stud.php" class="stat-card" data-aos="fade-up" data-aos-delay="100">
            <div class="stat-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res); ?>">0</div>
            <div class="stat-title">الطلاب الفعالين</div>
            <div class="stat-subtitle">الطلاب النشطين حالياً</div>
        </a>

        <!-- Stat Card 2: Soon to Expire -->
        <a href="expried_soon.php" class="stat-card" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res3); ?>">0</div>
            <div class="stat-title">قريب الانتهاء</div>
            <div class="stat-subtitle">الطلاب على وشك انتهاء الاشتراك</div>
        </a>

        <!-- Stat Card 3: Total Students -->
        <a href="allstud.php" class="stat-card" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($getUser); ?>">0</div>
            <div class="stat-title">إجمالي الطلاب</div>
            <div class="stat-subtitle">العدد الكلي للطلاب المسجلين</div>
        </a>

        <!-- Stat Card 4: Expired Students -->
        <a href="expired_stud.php" class="stat-card" data-aos="fade-up" data-aos-delay="400">
            <div class="stat-icon">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res2); ?>">0</div>
            <div class="stat-title">منتهي الاشتراك</div>
            <div class="stat-subtitle">الطلاب المنتهية اشتراكاتهم</div>
        </a>

        <!-- Stat Card 5: Kindergarten -->
        <a href="hadanaStud.php" class="stat-card" data-aos="fade-up" data-aos-delay="500">
            <div class="stat-icon">
                <i class="fas fa-baby"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res5); ?>">0</div>
            <div class="stat-title">صنف الحضانة</div>
            <div class="stat-subtitle">طلاب الحضانة</div>
        </a>

        <!-- Stat Card 6: Preschool -->
        <a href="rodaStud.php" class="stat-card" data-aos="fade-up" data-aos-delay="600">
            <div class="stat-icon">
                <i class="fas fa-child"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res4); ?>">0</div>
            <div class="stat-title">صنف الروضة</div>
            <div class="stat-subtitle">طلاب الروضة</div>
        </a>

        <!-- Stat Card 7: Preparatory -->
        <a href="tamhediStud.php" class="stat-card" data-aos="fade-up" data-aos-delay="700">
            <div class="stat-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res7); ?>">0</div>
            <div class="stat-title">صنف التمهيدي</div>
            <div class="stat-subtitle">طلاب التمهيدي</div>
        </a>

        <!-- Stat Card 8: Advanced -->
        <a href="tadeery.php" class="stat-card" data-aos="fade-up" data-aos-delay="800">
            <div class="stat-icon">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stat-number" data-count="<?php echo mysqli_num_rows($res8); ?>">0</div>
            <div class="stat-title">صنف التحضيري</div>
            <div class="stat-subtitle">طلاب التحضيري</div>
        </a>

    

    <!-- Enhanced Footer -->
    <footer class="footer_p">
        <div style="max-width: 1200px; margin: 0 auto;">
            <p style="font-size: 1.1rem; margin: 0;">
                <i class="fas fa-heart" style="color: #ff6b6b; animation: heartbeat 1.5s ease-in-out infinite;"></i>
                جميع الحقوق محفوظة لمؤسسة كيدز أكاديمي
                <span style="margin: 0 10px;">|</span>
                <i class="fas fa-calendar-alt"></i>
                <?php echo date('Y'); ?>
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/jquery.min.js"></script>
    <script src="js/all.min.js"></script>

    <script>
        // Loading Screen
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.opacity = '0';
                setTimeout(() => {
                    document.getElementById('loadingOverlay').style.display = 'none';
                }, 500);
            }, 1000);
        });

        // Enhanced Clock
        function updateClock() {
            const now = new Date();
            const seconds = now.getSeconds() * 6;
            const minutes = now.getMinutes() * 6 + now.getSeconds() * 0.1;
            const hours = (now.getHours() % 12) * 30 + now.getMinutes() * 0.5;

            document.getElementById("sec").style.transform = `rotate(${seconds}deg)`;
            document.getElementById("min").style.transform = `rotate(${minutes}deg)`;
            document.getElementById("hr").style.transform = `rotate(${hours}deg)`;
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call

        // Animated Counter
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 30);
        }

        // Initialize counters when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const counters = document.querySelectorAll('.stat-number');
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-count'));
                    animateCounter(counter, target);
                });
            }, 1500);
        });

        // Parallax Effect
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });

        // Card Hover Effects
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.05)';
                this.style.zIndex = '10';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.zIndex = '1';
            });
        });

        // Floating Animation for Icons
        document.querySelectorAll('.stat-icon').forEach((icon, index) => {
            icon.style.animationDelay = `${index * 0.2}s`;
        });

        // Add heartbeat animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes heartbeat {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.2); }
            }
        `;
        document.head.appendChild(style);

        // Console welcome message
        console.log(`
        🎉 مرحباً بك في أكاديمية الأطفال!
        🚀 تم تحميل النظام بنجاح
        ⭐ تصميم احترافي ومبهر
        `);

        // Add some interactive particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
                animation: float 3s ease-out forwards;
            `;

            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';

            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 3000);
        }

        // Create particles periodically
        setInterval(createParticle, 2000);

        // Add float animation
        const floatStyle = document.createElement('style');
        floatStyle.textContent = `
            @keyframes float {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(floatStyle);
    </script>
</body>
</html>
