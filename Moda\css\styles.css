@font-face {
    font-family:"LamaSans-Medium";
    src: url(JannaLT.ttf);
}

*{
margin: 0;
padding: 0;
font-family:"LamaSans-Medium",sans-serif;
box-sizing: border-box;
text-decoration: none;
list-style: none;

}
body{
  background-image:url(121.jpg) ; 
  background-repeat:no-repeat;
  background-attachment: fixed;
  background-size:100% 100%;
 
}

nav{
  display: flex;
  height: 90px;
  width: 100%;
  background: #201f1f;
  align-items: center;
  justify-content: space-between;
  padding:0 20px 0 55px;
  flex-wrap: wrap;
}
nav .logo{
  padding-bottom:5px;
  color: #fff;
  font-size: 40px;
  font-weight: 600;
  margin-left: 26%;
}
#exit_btn{
  margin-top: 9px;
 
}
.logo label{
  font-size: 25px;
}

nav ul{
  padding-bottom:5px;
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding-right: 10px;
}
nav ul li{
  
  padding-top: 15px;
  margin: 0 5px;
}
nav ul li a{
  color: #f2f2f2;
  text-decoration: none;
  font-size: 15px;
  font-weight: 300;
  padding: 8px 8px;
  border-radius: 5px;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}
nav ul li a.active,
nav ul li a:hover{
  color: #111;
  background: #fff;
  text-decoration: none;
}
svg.svg-inline--fa.fa-caret-down {
  padding-right: 15px;
}
.down_menu{
  text-align: center;
  background: #a92c2c;
  list-style: none;
  position: absolute;
  padding-bottom: 15px;
  margin-top: 37px;
  display: table-row;
  opacity: 0;
  visibility: hidden;
  z-index: 111;
  border-radius: 6px;
  padding: 0px 10px 10px 10px;
  transition: .4s;
  top: 70px;
  margin-left: -15px;
  height: 100px;
  
}
.repo:hover> ul{
  top: 55px;
  opacity: 1;
  visibility:visible;
}
.down_menu li {
  float: none;
  display: list-item;
}
#capss{
  color: aliceblue;
  font-size: 30px;
  cursor: pointer;
  display: none;
  margin-top: 10px;
}
#capss:hover{
  color: rgb(165, 162, 162);
  
}
input[type="checkbox"]{
  display: none;
}
@media (max-width: 1000px){
  nav{
    padding: 0 40px 0 50px;
  }

 
}
@media (max-width: 1385px) {

  nav .logo {
    margin-left:0px;
}
  #capss{
    display: block;
 
  }
  
  .logo label{
    font-size: 20px;
  }
  #exit_btn{
    margin-top: 15px;
    font-size: 15px;
  }
  #click:checked ~ .menu-btn i:before{

    content: "\f00d";
    
  }

  nav ul{
    position: absolute;
    top: 80px;
    left: -100%;
    background: #111;
    height: fit-content;
    width: 100%;
    text-align: center;
    display: block;
    transition: all 0.3s ease;
    z-index:10;
  }
  #click:checked ~ ul{
    left: 0;
  }
  
  nav ul li{
    width: 100%;
    margin: 40px 0;
  }
  nav ul li a{
    width: 100%;
    margin-left: -100%;
    display: block;
    font-size: 20px;
    transition: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  #click:checked ~ ul li a{
    margin-left: 0px;
  }
 
  nav ul li a.active,
  nav ul li a:hover{
    background: none;
    color: cyan;
  }
}

.grid{
  padding: 45px;
  margin-top: 20px;
  display: grid;
  grid-gap: 16px;
  
  
}
@media (min-width:768px){
  .grid{
   
    grid-template-columns:repeat(3,1fr);
  }
  .col.last {
    align-items: center;
    position: relative;
    left: 103.5%;
  }

}
@media (min-width:1280px){
  .grid{
   
    grid-template-columns:repeat(3,0.5fr);
  }
  .col.last {
    align-items: center;
    position: relative;
    left: 103.5%;
  }
  
}
.col:hover{
  box-shadow: 5px 0 0 0px rgba(35, 34, 34, 0.5);
}
.col{
  background-color: pink;
  margin-bottom: 16px;
  padding: 10px;
  align-items: center;
  background-color: #ebebeb;
  box-shadow: 5px 0 0 0px rgba(255, 0, 0, 0.5);
  border-radius: 20px;
  text-align: center;
  text-decoration: none;
  height: 163px;
  
}

.col a{
  text-decoration: none;
  color: rgba(255, 0, 0, 0.5);
  font-size: 35px;
}
.col a:hover{
  text-decoration: none;
  color: rgba(77, 77, 77, 0.5);
  font-size: 35px;
}
.col h3{
  padding-top: 10px;
  text-decoration: none;
  color: #1b1b1b;
}
.content{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: -1;
  width: 100%;
  padding: 0 30px;
  color: #1b1b1b;
}
.content div{
  font-size: 40px;
  font-weight: 700;
}

.cards{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px 0;
    flex-wrap: wrap;
    height: 100vh;
    

}
.container{
    max-width: 900px;
    margin: 0 auto;

}
.card{
    position: relative;
    text-align: center;
    width: 31%;
    color: rgb(0, 0, 0);
    border: 3px solid rgba(181, 181, 1818, 0.2);
    border-radius: 15px;
    
}

.box {
    padding-top: 30px;
    padding-left:4%;
  }
  /* Float four columns side by side */
  .column {
    float: right;
    width: 24%;
    padding: 10Px;
    
  }
  
  .row {margin: 0 -5px;}
  
  /* Clear floats after the columns */
  .row:after {
    content: "";
    display: table;
    clear: both;
  }
  
  /* Responsive columns */

  
  /* Style the counter cards */
  .cardt {
    background-color: #ebebeb;
    box-shadow: 5px 0 0 0px rgba(255, 0, 0, 0.5);
    box-sizing: unset;
    padding-right: 16px;
    text-align: center;
    border-radius: 20px;
    margin-right:15px;
    padding-top: 20px;
    padding-bottom: 20px;
   
    

    
  }
  .cardt:hover{
    box-shadow: 5px 0 0 0px rgba(2, 2, 2, 0.5);
  }
  
  .fa {font-size:60px;}
  .pag {
    
    font-family:"LamaSans-Medium",sans-serif;
    color: rgb(0, 0, 0);
    font-size: 25px;
  
  }

  .pag2 {
    font-family: "LamaSans-Medium", sans-serif;
  color: rgb(35, 35, 35);
  font-size: 25px;
  padding-left: 30%;
  padding-right: 30%;
  padding-top: 20px;
  border: 3px;

  text-align: center;
  box-shadow: 0 4px 8px 0 rgba(255, 115, 115, 0.2);
  box-sizing: border-box;
  border-radius: 7px;


  
  
  }
  .continICO{
    margin: 0 auto;
    height: 100vh;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
  
.box h3 {
    font-size: 40px;
  }
  .links :hover{
    color: black;
  }
  .links {
    color: black;
    
    
  }

  .table1{
    width: 50%;
    float: right;
  }
  .table2{
    width: 50%;
    float: left;
  }
  .datetime {
    font-size: 16px;
    padding: 24px;
    color: #ffffff;
    background: #444444;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    border-right: 10px #009578 solid;
    width: 400px;
    font-weight: 500;
    font-family: "Inter", sans-serif;
  }
  
  .time {
    font-size: 3em;
    color: #00ffcc;
  }
  
  .date {
    margin-top: 12px;
    font-size: 1.75em;
  }

  .divicon{
    display: grid;
    height: 10%;
    width: 100%;
    place-items: center;
    }
.wrappericon .button{
  display: inline-block;
  height: 60px;
  width: 60px;
  margin: 0 5px;
  overflow: hidden;
  background: #fff;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0px 10px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease-out;
  text-decoration: none;
}
.wrappericon .button:hover{
  width: 200px;
}
.wrappericon .button .icon{
  display: inline-block;
  position:relative;
  height: 60px;
  width: 60px;
  text-align: center;
  border-radius: 50px;
  box-sizing: border-box;
  line-height: 60px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:nth-child(1):hover .icon{
  background: #4267B2;
}
.wrappericon .button:nth-child(2):hover .icon{
  background: #1df236;
}
.wrappericon .button:nth-child(3):hover .icon{
  background: #E1306C;
}

.wrappericon .button .icon image{
  width: 20px;
  height: 20px;
  font-size: 25px;
  line-height: 60px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:hover .icon i{
  color: #fff;
}
.wrappericon .button span{
  font-size: 20px;
  font-weight: 500;
  line-height: 60px;
  margin-left: 10px;
  transition: all 0.3s ease-out;
}
.wrappericon .button:nth-child(1) span{
  color: #4267B2;
}
.wrappericon .button:nth-child(2) span{
  color: #00b636;
}
.wrappericon .button:nth-child(3) span{
  color: #E1306C;
}
  .continC{
    width: 350px;
    height: 250PX;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: 15%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .continC input{
    text-align: center;
    width:320px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(0, 0, 0);
  }
  .continC button{
    margin-top: 20px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
  }
  .continC button:hover{
    background-color: #E1306C;
    color: rgb(255, 255, 255);
  }
  .continC label{
    margin-top: 2px;
  }
  .contin_employ_admin{
    width: 350px;
    height: fit-content;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: 1%;
    display: block;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .contin_employ_admin input{
    text-align: center;
    width:320px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(0, 0, 0);
  }
  .contin_employ_admin button{
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
    margin-top: -40px;
  }
  .contin_employ_admin button:hover{
    background-color: #ff9f55;
    color: rgb(255, 255, 255);
  }
  .contin_employ_admin label{
    padding-top: 2px;
    margin-top: 2px;
  }
  .contin_employ_admin  div{
    display: block;
    margin-top: 20px;
    padding-top: 10px;
  }
  .contin_employ_admin #selc{
   
    margin-top: 20px;
    margin-right: 25px;
    margin-bottom: 10px;
  }


  .g12{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #39a032;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
  }

  .g13{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #ec7666;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
    
    
  }
  .g14{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 25px;
    background-color: #66b0ec;
    color: rgb(255, 255, 255);
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;
    
  }

  .g16{
    font-family:"LamaSans-Medium",sans-serif ;
    text-align: center;
    font-size: 20px;
    background-color: #f8cf68;
    color: rgb(0, 0, 0);
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 25px;
    margin: 0 auto;
    
    
  }
  #selc{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
  }
  #selc:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }
  #selc_deit{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
  }
  #selc_deit:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }
  #selc2{
    margin-top: 20px;
    width: 230px;
    border-radius: 5px;
    text-align: center;
    margin-top: 15px;
  }
  #selc2:hover{
    background-color: #7c7a7a;
    color:whitesmoke;
  }


  .contin{
    width: 350px;
    height: fit-content;
    background-color: #ececec;
    box-shadow: 0 5px 5px rgb(255, 151, 122);
    margin: 0 auto;
    text-align: center;
    margin-top: -5%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 17px;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0;
  }
  .contin input{
    text-align: center;
    width:290px;
    border-radius: 7px;
    justify-content:space-between;
    border-color: rgb(160, 160, 160);
    padding: 2px;
  
  }
  
  .contin button{
    margin-top: 30px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(79, 84, 79);
    color: #ddd;
  }
  .contin button:hover{
    margin-top: 30px;
    font-size: large;
    border-width: 2px;
    background-color: rgb(255, 132, 75);
    color: rgb(0, 0, 0);
  }
  .contin label{
    margin-top: 2px;
    font-size: 18px;
  }

#catg {
  background-color: #3e3e3e;
  color: whitesmoke;
  border-radius: 4px;
  padding-top: 3px;
  padding-bottom: 3px;
  width: 350px;
}

#sex {
  background-color: #3e3e3e;
  color: whitesmoke;
  border-radius: 4px;
  margin-top: 10px;
  padding-bottom: 3px;
  width: 350px;
}


  .chek input{
    width: 25px;
    height: 20px;
    cursor: pointer;
    padding: 10px;

    
  } 
  
  .chek{
    align-items: center;
    justify-items: center;
    display: flex;
    /* margin-left: 27%; */
    /* padding-left: 10px; */
    justify-content: center;
  }
  .chek label{
    padding: 10px;
  }

    .chek2 input {
      width: 25px;
      height: 20px;
      cursor: pointer;
      padding: 10px;
    
    }
    
    .chek2 {
      align-items: center;
      justify-items: center;
      display: flex;
    }
    
    .chek2 label {
      align-items: flex-end;
      padding: 8px;
    }
  

      .still{
        background-color: #34a45676;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      .exp{
        background-color: #ff8c8a8e;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      
      .soon{
        background-color: #e7f36486;
        border-radius: 15px;
        font-size: 20px;
        padding:3px;
        text-align: center;
      }
      .table{
        align-items: center;
        text-align: center;
        border:2px;
        border-color: rgb(0, 0, 0);
        
        
       }
       .label_acout
       {
        position: relative;
        margin: 0 auto;
        margin-top: -100%;
       }
       .search{
        float: right;
        margin-top: 25px;
        margin-right: 35px;
        margin-bottom: 3px;
        position: relative;
        z-index: 1;

       }
       .search input{
       margin: 10px;
       width: 320px;
       text-align: center;
       border-radius: 3px;

       }
       .search a{
        font-size: 15px;
         margin: 4px;
         text-decoration: none;
         color: whitesmoke;
       }

       .search_ac{
        float: right;
        padding-top: 10px;
        padding-bottom: 15px;
        padding-right: 20px;
        border-radius: 10px;
        padding-left: 15px;
        margin-bottom: 3px;
        background-color: #1b1b1b71;
        position: relative;
        z-index: 1;
        

       }
       .search_ac input{
       margin-right: 10px;
       width: fixed;
       text-align: center;
       border-radius: 3px;

       }
       .search_ac a{
        font-size: 15px;
         margin: 4px;
         text-decoration: none;
         color: whitesmoke;
       }
       .search_ac label{
        font-size: 15px;
         margin-right: 10px;
        
        padding-right: 15px;
        padding: 5px;
        border-radius: 10px;
        color: white;
       }
       
      .table.active{
        filter: blur(200px);
      }
     
       .table tbody{
        font-size: 18px;
        font-weight:50;
        background-color: #ffffff71;
        
        
      }
      .table thead{
        font-size: 19px;
        font-weight:50;
        background-color: #000000c6;
        border: 2px;
        align-items: center;
        color: whitesmoke;
      }
      .table a{
       text-decoration: none;
      }
       #edit_bnt:hover{
        background-color: rgb(38, 41, 43);
       }
       #reomve_btn:hover{
        background-color: rgb(255, 111, 89);
       }
       #renew_btn:hover{
        background-color: rgb(27, 198, 255);
       }

       .nof1{
        font-family:"LamaSans-Medium",sans-serif ;
        text-align: center;
        font-size: 20px;
        background-color: #ff6060;
        color: rgb(0, 0, 0);
        margin-left: 10px;
        padding: 15px; 
        width: fit-content;
        margin-top: 10px;
        border-radius: 20px;
        margin-bottom: -60px;
      }
      .noif2{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ff2222a5;
        padding: 15px; 
        border-radius: 10px;
      
        
      }
      .addon{
        justify-content: center;
        align-items: center;
        font-family:"LamaSans-Medium",sans-serif ;
        text-align: center;
        font-size: 22px;
        background-color: #60e4ffa5;
        color: rgb(27, 27, 27);
        margin-left: 10px;
        padding: 15px; 
        height: 100%;
        padding-top: 25px;
        padding-bottom: 30px;
        width: fit-content;
        margin-top: 0;
        border-radius: 20px;
        margin-bottom: -60px;
      }
      .less{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ff7373a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .aades{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73ceffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .aades2{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73ceffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: 2px;
       
      }
      .aades-note{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ffcd57a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .less-note{
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #ffcd57a5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
      }
      .acount{
       
        font-family:"LamaSans-Medium",sans-serif ;
        float: left;
        position: relative;
        font-size: 22px;
        background-color: #ff7373a5;
        color: rgb(27, 27, 27);
        margin-left: -5px;
        padding: 15px; 
        margin-right: 15px;
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top: 82px;
        border-radius: 10px;
        margin-bottom: auto;
        padding-left: 15px;
      }
      .acount2{
       
        font-family:"LamaSans-Medium",sans-serif ;
        position:absolute;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #73e8ffa5;
        margin-left: -5px;
        padding: 15px; 
        height:80px;
        padding-top: 25px;
        padding-bottom: 25px;
        width:fit-content;
        margin-top:0;
        border-radius: 10px;
        margin-bottom: auto;
       
        
      }

      
      .nofi3{
        font-family:"LamaSans-Medium",sans-serif ;
        position:relative;
        float: left;
        font-size: 22px;
        color: rgb(27, 27, 27);
        background-color: #22aeffa5;
        padding: 15px; 
        border-radius: 10px;
      }

      .contin_depit{
        width: 350px;
        height: fit-content;
        background-color: #ececec;
        box-shadow: 0 5px 5px rgb(255, 151, 122);
        margin: 0 auto;
        text-align: center;
        margin-top: 1%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        border-radius: 40px;
        margin-bottom: 20px;
        padding: 0;
      }
      .contin_depit input{
        text-align: center;
        width:320px;
        border-radius: 7px;
        justify-content:space-between;
        border-color: rgb(0, 0, 0);
      }
      .contin_depit button{
        margin-top: 20px;
        font-size: large;
        border-width: 2px;
        background-color: rgb(79, 84, 79);
        color: #ddd;
      }
      .contin_depit button:hover{
        background-color: #ff9f55;
        color: rgb(255, 255, 255);
      }
      .contin_depit label{
        padding-top: 2px;
        margin-top: 2px;
      }

      .contin_user{
        width: 350px;
        height: fit-content;
        background-color: #ececec;
        box-shadow: 0 5px 5px rgb(255, 151, 122);
        margin: 0 auto;
        text-align: center;
        margin-top: 1%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        border-radius: 40px;
        margin-bottom: 20px;
        padding: 0;
      }
      .contin_user input{
        text-align: center;
        width:320px;
        border-radius: 7px;
        justify-content:space-between;
        border-color: rgb(0, 0, 0);
      }
      .contin_user button{
        margin-top: 20px;
        font-size: large;
        border-width: 2px;
        background-color: rgb(79, 84, 79);
        color: #ddd;
      }
      .contin_user button:hover{
        background-color: #ff9f55;
        color: rgb(255, 255, 255);
      }
      .contin_user label{
        padding-top: 2px;
        margin-top: 2px;
      }
      .contin_user div{
        display: flex;
        padding: 10px;
      }
      #selc{
        margin-top: -10px;
        margin-left: 37px;
      }
      #selc{
        margin-top: -10px;
        margin-left: 37px;
      }
    

    
      .logo2 img{
        float:left;
        width:90px;
        height: 90px;
        margin-left: -55px;
       
       
      
        }

        .footer_p{
          text-align: center;
          align-items: center;
          justify-content: center;
          padding-top: 40px;
         margin: 0 auto;
         font-size: 18px;
         color: #b2b2b2;
         
        }

        .continICO{
          margin: 0 auto;
          height: 100vh;
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;
        }


#textarea{
          width: 320px;
          height: 120px;
          text-align: right;
          padding-inline: 5px;
          overflow: auto;
          resize: none;
          text-align:center;
      
}


.rect{
  width: 250px;
  height: 250px;
  background-color: #1f2227;
  position: absolute;
  margin: 0 auto;
  margin-top: 10px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
  box-shadow: 0 0 30px #1f2227a1;
  margin-bottom: -20px;
}

.cir{
  width: 220px;
  height: 220px;
  background: linear-gradient(to right,#fa6a52,#ff2058);
  display:flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;

}
.inner_cir{
  width: 200px;
  height: 200px;
  background: black url(anlog.png);
  border-radius: 50%;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;


}
.center{
  width: 20px;
  height: 20px;
  background:linear-gradient(to right,rgb(43, 42, 42),rgb(38, 37, 37));
  border-radius: 50%;
  z-index: 1;
  position:absolute;
}
.brand{
  position: relative;
  top: -38px;
  left: 1%;
  font-size: 13px;
  font-weight: bold;
  border: 3px;
  color:rgb(255, 34, 0);
  text-shadow: 0 2px 2px rgba(0, 0, 0, 0.384);
  font-family: Arial, Helvetica, sans-serif;


}
#hr{
  width: 120px;
  height: 120px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#min{
  width: 130px;
  height: 130px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#sec{
  width: 150px;
  height: 150px;
  position:absolute;
  display: flex;
  justify-content: center;
  background-color: transparent;
}
#hr::before{
  content: "";
  width: 8px;
  height: 70px;
  background-color: rgba(0, 0, 0, 0.682);
  position: absolute;
  top:-15;
  border-radius: 3px;
}

#min::before{
  content: "";
  width: 5px;
  height: 70px;
  background-color: rgba(255, 0, 0, 0.682);
  position: absolute;
  top:-25;
  border-radius: 3px;
}
#sec::before{
  content: "";
  width: 2px;
  height: 100px;
  background-color: rgba(234, 77, 4, 0.682);
  position: absolute;
  top:-25;
  border-radius: 3px;
}



.wrapper{
  width: 420px;
  padding: 40px 20px;
  top: 9%;
  position: relative;
  right: 0;
  
}
#show-toast{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-left: 8px solid rgb(255, 149, 0);;
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(500px);
  transition: 1s;
}
.container-1,.container-2{
  align-self: center;
}
.container-1 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #ff9500;
}
.container-2 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
}
.container-2 p:last-child{
  font-size: 15px;
  color: #656565;
  font-weight: 400;
}


.wrapper2{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast2{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast2{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-11,.container-22{
  align-self: center;
}
.container-11 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-22 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-22 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}




.wrapper3{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast3{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast3{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-33,.container-22{
  align-self: center;
}
.container-33 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-33 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-33 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}

.div-tosar{
  margin: 0;
  padding: 0;
}

.wrapper4{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast4{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast4{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-44,.container-22{
  align-self: center;
}
.container-44 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-44 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-44 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}


.wrapper5{
  width: 420px;
  padding: 5px 20px;
  top: 9%;
  position: relative;
  right: 0;
  left: -27px;
  /*margin-bottom: 20px;*/
}
#show-toast5{
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 50%;
  background-color: #101020;
  color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  cursor: pointer;
}
#toast5{
  width: 380px;
  height: 80px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 10px 20px rgba(75, 50, 50, 0.05);
  border-right: 8px solid rgb(3, 188, 77);
  border-radius: 7px;
  display: grid;
  grid-template-columns: 1.2fr 6fr 0.5fr;
  transform: translate(-400px);
  transition: 1s;
}
.container-55,.container-22{
  align-self: center;
}
.container-55 svg{
  margin-bottom: 50px;
  font-size: 40px;
  color: #00a743;
  left: 30px;
}
.container-55 p:first-child{
  margin-top: -42px;
  color: #101020;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.container-55 p:last-child{
  text-align: center;
  font-size: 17px;
  color: #656565;
  font-weight: 400;
}
/*التحقق من الحذف بوب نافذه  */

section {
  position: relative;
  height: 100%;
  width: 100%;
  background: #e3f2fd;
}
.buttons-method {
  display: flex;
  flex-direction: row;
  padding: 20px 10px;
  margin-top: 15px;
}
.close-btn,.remove-btn {
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  padding: 14px 33px;
  border: none;
  background: #f44040;
  border-radius: 6px;
  cursor: pointer;
}
button:hover {
  background-color: #8f8f8f;
}
button.show-modal,
.modal-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}
section.active .show-modal {
  display: none;
}
.overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  pointer-events: none;
}
section.active .overlay {
  opacity: 1;
  pointer-events: auto;
}
.modal-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 380px;
  width: 100%;
  padding: 30px 20px;
  border-radius: 24px;
  background-color: #fff;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%) scale(1.2);
}
section.active .modal-box {
  opacity: 1;
  pointer-events: auto;
  transform: translate(-50%, -50%) scale(1);
  position: fixed;
}
.modal-box i {
  font-size: 70px;
  color: #4070f4;
}
.modal-box h2 {
  margin-top: 20px;
  font-size: 25px;
  font-weight: 500;
  color: #333;
}
.modal-box h3 {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  text-align: center;
}
.modal-box .buttons {
  margin-top: 25px;
}
.modal-box button {
  font-size: 14px;
  padding: 6px 12px;
  margin: 0 10px;
}

div#Table_filter {
  padding: 15px;
}
div#Table_length {
  padding: 15px;
  
}
section.button_excel {
  float: right;
  width: 200px;
  height: fit-content;
  margin-top: 7px;
  /* margin-left: 93%; */
  background: transparent;
  visibility: hidden;
  transition: 3s;
  display: flex;
  gap: 10px;
}
button.bt_execl {
  float: right;
  width: 90px;
  border-radius: 20px;
  height: 65px;
  margin-right: 10px;
  border: transparent;
  color: white;
  background: #2fb82f;
  transition:2s;
  cursor: pointer;
  
}
td.date {
  font-size: 20px;
}

button.bt_execl:hover {
  float: right;
  width: 90px;
  border-radius: 20px;
  height: 65px;
  margin-right: 10px;
  border: transparent;
  color: rgb(17, 17, 17);
  background: #ccf1cc;
  cursor: pointer;
}
button.bt_execl:checked {
  float: right;
  width: 90px;
  border-radius: 20px;
  height: 65px;
  margin-right: 10px;
  border: transparent;
  color: rgb(17, 17, 17);
  background: #ccf1cc;
  cursor: pointer;
}

/* تنسيق زر الطباعة */
button.bt_print {
  float: right;
  width: 90px;
  border-radius: 20px;
  height: 65px;
  margin-right: 10px;
  border: transparent;
  color: white;
  background: #007bff;
  cursor: pointer;
  font-family: "LamaSans-Medium", sans-serif;
  font-size: 14px;
  transition: all 0.3s ease;
}

button.bt_print:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

button.bt_print:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}