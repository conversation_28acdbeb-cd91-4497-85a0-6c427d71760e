<?php
session_start();
try {
    if (isset($_SESSION['user'])) {
        if ($_SESSION['user']->role === "Admin") {
            // Admin logic here
        } else {
            header("location:../login.php", true);
            die("");
            echo "don't work";
        }
    } else {
        header("location:../login.php", true);
        die("");
    }
} catch (Exception $e) {
    echo "Error in session handling: " . $e->getMessage();
}

try {
    $user = 'kidzrcle_rwda';
    $pass = 'kidzrcle_rwda';
    $database = new PDO("mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8;", $user, $pass);
    $getUser = $database->prepare("SELECT * FROM stud_tb");
    $getUser->execute();
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage();
}

try {
    $con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
  $con->set_charset("utf8");
    if ($con->connect_error) {
        throw new Exception("Connection failed: " . $con->connect_error);
    }

    $datenow = date('Y-m-d');
    $Date = date('Y-m-d', strtotime($datenow . ' + 10 days'));
    $Date2 = date('Y-m-d', strtotime($datenow . ' + 1 days'));
    $Date3 = date('Y-m-d', strtotime($datenow . ' + 30 days'));
    $Date4 = date('Y-m-d', strtotime($datenow . ' + 5 days'));
    $Date5 = date('Y-m-d', strtotime($datenow . ' + 365 days'));

    // Queries
    $sql = "SELECT * FROM stud_tb, stud_pay, users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2') AND Date('$Date5') AND stud_tb.id = stud_pay.id_stud AND users_tb.id_user = stud_tb.userID";
    $res = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_pay WHERE DATE(date_exp) <= '$datenow'";
    $res2 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_pay WHERE DATE(date_exp) BETWEEN '$Date2' AND '$Date'";
    $res3 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='روضة'";
    $res4 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='حضانة'";
    $res5 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM depit_tb";
    $res6 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='تمهيدي'";
    $res7 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='تحضيري'";
    $res8 = mysqli_query($con, $sql);
} catch (Exception $e) {
    echo "Error in query execution: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 لوحة التحكم الرئيسية - أكاديمية الأطفال</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <link rel="stylesheet" href="css/navbar_notifications.css">
    <link rel="stylesheet" href="css/admin_navbar.css">
    <link rel="stylesheet" href="css/admin_dashboard.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <?php include "addon/topbar.php"; ?>
</head>
<body>


    <!-- Main Content -->
    <div class="main-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="gradient-text">مرحباً بك في أكاديمية كيدز</span>
                        <div class="title-decoration"></div>
                    </h1>
                    <p class="hero-subtitle">
                        نظام إدارة متطور وشامل لأكاديمية الأطفال
                    </p>
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <span class="stat-number"><?php echo $getUser->rowCount(); ?></span>
                            <span class="stat-label">إجمالي الطلاب</span>
                        </div>
                        <div class="hero-stat">
                            <span class="stat-number"><?php echo mysqli_num_rows($res); ?></span>
                            <span class="stat-label">الطلاب النشطين</span>
                        </div>
                        <div class="hero-stat">
                            <span class="stat-number"><?php echo mysqli_num_rows($res6); ?></span>
                            <span class="stat-label">المعاملات المالية</span>
                        </div>
                    </div>
                </div>

                <!-- Digital Clock Only -->
                <div class="clock-container">
                    <div class="digital-clock-enhanced">
                        <div class="time-display" id="digitalTime"></div>
                        <div class="date-display" id="digitalDate"></div>
                        <div class="day-display" id="digitalDay"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <!-- Priority Cards -->
            <div class="priority-cards">
                <a href="expried_soon.php" class="stat-card priority-card urgent clickable-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <div class="card-icon urgent">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-badge urgent">عاجل</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-number" data-count="<?php echo mysqli_num_rows($res3); ?>">0</h3>
                        <p class="card-title">الطلاب على وشك الانتهاء</p>
                        <p class="card-subtitle">طلاب على وشك انتهاء الاشتراك</p>
                    </div>
                </a>

                <a href="expired_stud.php" class="stat-card priority-card warning clickable-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <div class="card-icon warning">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="card-badge warning">تحذير</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-number" data-count="<?php echo mysqli_num_rows($res2); ?>">0</h3>
                        <p class="card-title">الطلاب منتهي الاشتراك</p>
                        <p class="card-subtitle">طلاب انتهت اشتراكاتهم</p>
                    </div>
                </a>
            </div>

            <!-- Main Statistics -->
            <div class="main-stats">
                <a href="new_stud.php" class="stat-card main-card active clickable-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-header">
                        <div class="card-icon active">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-number" data-count="<?php echo mysqli_num_rows($res); ?>">0</h3>
                        <p class="card-title">الطلاب الفعالين</p>
                        <p class="card-subtitle">الطلاب المسجلين حالياً</p>
                    </div>
                </a>

                <a href="allstud.php" class="stat-card main-card total clickable-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-header">
                        <div class="card-icon total">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-trend stable">
                            <i class="fas fa-minus"></i>
                            <span>مستقر</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-number" data-count="<?php echo $getUser->rowCount(); ?>">0</h3>
                        <p class="card-title">عدد الطلاب الكلي</p>
                        <p class="card-subtitle">العدد الكلي للطلاب</p>
                    </div>
                </a>

                <a href="allDepit.php" class="stat-card main-card financial clickable-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="card-header">
                        <div class="card-icon financial">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-number" data-count="<?php echo mysqli_num_rows($res6); ?>">0</h3>
                        <p class="card-title">عدد المصروفات</p>
                        <p class="card-subtitle">إجمالي المصروفات</p>
                    </div>
                </a>
            </div>
        </div>
        <!-- Categories Section -->
        <div class="categories-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-layer-group"></i>
                    فئات الطلاب
                </h2>
                <p class="section-subtitle">إحصائيات مفصلة حسب الفئات العمرية</p>
            </div>

            <div class="categories-grid">
                <a href="hadanaStud.php" class="category-card kindergarten clickable-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="category-icon">
                        <i class="fas fa-baby"></i>
                    </div>
                    <div class="category-content">
                        <h3 class="category-number" data-count="<?php echo mysqli_num_rows($res5); ?>">0</h3>
                        <h4 class="category-title">عدد طلاب صنف الحضانة</h4>
                        <p class="category-subtitle">الأطفال الصغار</p>
                        <div class="category-progress">
                            <div class="progress-bar" style="--progress: 75%;"></div>
                        </div>
                    </div>
                </a>

                <a href="rodaStud.php" class="category-card preschool clickable-card" data-aos="fade-up" data-aos-delay="700">
                    <div class="category-icon">
                        <i class="fas fa-child"></i>
                    </div>
                    <div class="category-content">
                        <h3 class="category-number" data-count="<?php echo mysqli_num_rows($res4); ?>">0</h3>
                        <h4 class="category-title">عدد طلاب صنف الروضة</h4>
                        <p class="category-subtitle">مرحلة ما قبل المدرسة</p>
                        <div class="category-progress">
                            <div class="progress-bar" style="--progress: 85%;"></div>
                        </div>
                    </div>
                </a>

                <a href="tamhediStud.php" class="category-card preparatory clickable-card" data-aos="fade-up" data-aos-delay="800">
                    <div class="category-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="category-content">
                        <h3 class="category-number" data-count="<?php echo mysqli_num_rows($res7); ?>">0</h3>
                        <h4 class="category-title">عدد طلاب صنف التمهيدي</h4>
                        <p class="category-subtitle">التحضير للمرحلة الابتدائية</p>
                        <div class="category-progress">
                            <div class="progress-bar" style="--progress: 90%;"></div>
                        </div>
                    </div>
                </a>

                <a href="tadeery.php" class="category-card advanced clickable-card" data-aos="fade-up" data-aos-delay="900">
                    <div class="category-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="category-content">
                        <h3 class="category-number" data-count="<?php echo mysqli_num_rows($res8); ?>">0</h3>
                        <h4 class="category-title">عدد طلاب صنف التحضيري</h4>
                        <p class="category-subtitle">المرحلة المتقدمة</p>
                        <div class="category-progress">
                            <div class="progress-bar" style="--progress: 80%;"></div>
                        </div>
                    </div>
                </a>
            </div>
        </div>



        <!-- Simple Footer -->
        <footer class="simple-footer">
            <p>جميع الحقوق محفوظة</p>
        </footer>
    </div>

    <!-- CSS Styles -->
    <style>
        :root {
            /* Soft, eye-friendly color palette */
            --primary-gradient: linear-gradient(135deg, #6c7ce7 0%, #a55eea 100%);
            --success-gradient: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
            --warning-gradient: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --gold-gradient: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            --purple-gradient: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            --orange-gradient: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            --blue-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --green-gradient: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            --soft-gradient: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);

            --shadow-soft: 0 4px 20px rgba(99, 102, 241, 0.1);
            --shadow-medium: 0 8px 30px rgba(99, 102, 241, 0.15);
            --shadow-heavy: 0 15px 40px rgba(99, 102, 241, 0.2);

            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', 'Poppins', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Loading Screen */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Main Container */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }

        /* Hero Section */
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 40px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 20px;
            position: relative;
        }

        .gradient-text {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .title-decoration {
            width: 100px;
            height: 4px;
            background: var(--gold-gradient);
            border-radius: 2px;
            margin-top: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scaleX(1); }
            50% { opacity: 0.7; transform: scaleX(1.1); }
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-stats {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .hero-stat {
            text-align: center;
            color: white;
        }

        .stat-number {
            display: block;
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gold-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        /* Clock Container */
        .clock-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .digital-clock-enhanced {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            padding: 25px;
            text-align: center;
            color: #1e293b;
            box-shadow: var(--shadow-medium);
            min-width: 280px;
        }

        .time-display {
            font-size: 2.2rem;
            font-weight: 700;
            font-family: 'Poppins', monospace;
            margin-bottom: 8px;
            color: #3b82f6;
        }

        .date-display {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 5px;
            color: #64748b;
        }

        .day-display {
            font-size: 0.9rem;
            font-weight: 600;
            color: #8b5cf6;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Clickable Cards */
        .clickable-card {
            text-decoration: none;
            color: inherit;
            display: block;
            cursor: pointer;
        }

        .clickable-card:hover {
            text-decoration: none;
            color: inherit;
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .clickable-card:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
        }

        /* Simple Footer */
        .simple-footer {
            text-align: center;
            padding: 30px;
            margin-top: 50px;
            color: #64748b;
            font-size: 0.9rem;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .analog-clock {
            width: 150px;
            height: 150px;
            position: relative;
        }

        .clock-face {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            position: relative;
            box-shadow: var(--shadow-medium);
        }

        .clock-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .clock-hand {
            position: absolute;
            background: white;
            transform-origin: bottom center;
            border-radius: 2px;
        }

        .hour-hand {
            width: 4px;
            height: 40px;
            top: 35px;
            left: 50%;
            margin-left: -2px;
        }

        .minute-hand {
            width: 3px;
            height: 55px;
            top: 20px;
            left: 50%;
            margin-left: -1.5px;
        }

        .second-hand {
            width: 1px;
            height: 60px;
            top: 15px;
            left: 50%;
            margin-left: -0.5px;
            background: #ff6b6b;
        }

        .clock-numbers {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .number {
            position: absolute;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            transform: rotate(calc(var(--i) * 30deg)) translateY(-60px) rotate(calc(var(--i) * -30deg));
        }
    </style>

    <!-- JavaScript -->
    <script src="js/all.min.js"></script>
    <script src="js/all.js"></script>
    <script src="js/notifications.js"></script>
    <script>
        // Enhanced Clock Functions (English)
        function updateClock() {
            const now = new Date();

            // Digital Clock in English
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const dateString = now.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const dayString = now.toLocaleDateString('en-US', {
                weekday: 'long'
            });

            document.getElementById('digitalTime').textContent = timeString;
            document.getElementById('digitalDate').textContent = dateString;
            document.getElementById('digitalDay').textContent = dayString;
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call

        // Animated Counter
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 30);
        }

        // Initialize counters when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const counters = document.querySelectorAll('.card-number, .category-number');
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-count'));
                    animateCounter(counter, target);
                });
            }, 2000);
        });

        // Parallax Effect
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });

        // Card Hover Effects
        document.querySelectorAll('.stat-card, .category-card, .management-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.zIndex = '10';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.zIndex = '1';
            });
        });

        // Smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add floating particles effect
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1;
                animation: float 6s linear infinite;
                left: ${Math.random() * 100}vw;
                top: 100vh;
            `;

            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 2000);

        // Add CSS for particle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
