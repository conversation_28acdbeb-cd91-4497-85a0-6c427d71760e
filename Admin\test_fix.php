<?php
session_start();

// محاكاة جلسة المدير للاختبار
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = (object) ['role' => 'Admin'];
}

echo "<h2>اختبار الإصلاح - المستخدم رقم 7</h2>";

// محاكاة POST request للمستخدم رقم 7
$_POST['id'] = 7;

echo "<h3>نتيجة ملف infostudF.php للمستخدم رقم 7:</h3>";
echo "<div style='border: 2px solid #007bff; padding: 15px; background: #f8f9fa;'>";

// تضمين الملف وعرض النتيجة
ob_start();
include "addon/infostudF.php";
$output = ob_get_clean();

echo $output;
echo "</div>";

echo "<h3>ما يجب أن تراه:</h3>";
echo "<ul>";
echo "<li>✅ رسالة JavaScript مع الإحصائيات (حتى لو كانت أصفار)</li>";
echo "<li>✅ رسالة 'لا توجد بيانات طلاب لهذا المستخدم'</li>";
echo "<li>✅ أيقونة تحذيرية صفراء</li>";
echo "</ul>";

echo "<h3>الآن جرب الصفحة الرئيسية:</h3>";
echo "<p><a href='infost.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار infost.php</a></p>";

echo "<h3>خطوات الاختبار:</h3>";
echo "<ol>";
echo "<li>افتح الرابط أعلاه</li>";
echo "<li>اختر المستخدم 'A3' من القائمة المنسدلة</li>";
echo "<li>يجب أن تظهر نافذة الإحصائيات على الجانب</li>";
echo "<li>يجب أن تظهر رسالة 'لا توجد بيانات طلاب' في الجدول</li>";
echo "</ol>";
?>
