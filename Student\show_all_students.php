<?php
include "addon/dbcon.php";

echo "<h1>🎓 جميع الطلاب المتاحين للتسجيل</h1>";

// جلب جميع الطلاب من قاعدة البيانات
$sql = "SELECT id, name, age, sex, catg, p_name, p_phone, datein, loc FROM stud_tb ORDER BY id";
$result = mysqli_query($con, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $total_students = mysqli_num_rows($result);
    
    echo "<div class='header-info'>";
    echo "<h2>📊 إجمالي الطلاب: <span class='count'>$total_students</span> طالب</h2>";
    echo "<p class='instruction'>✨ يمكن لأي طالب من القائمة أدناه تسجيل الدخول باستخدام المعلومات المذكورة</p>";
    echo "</div>";
    
    echo "<div class='students-container'>";
    
    $count = 0;
    while ($student = mysqli_fetch_assoc($result)) {
        $count++;
        
        echo "<div class='student-card'>";
        echo "<div class='student-header'>";
        echo "<div class='student-number'>{$student['id']}</div>";
        echo "<div class='student-info'>";
        echo "<h3>{$student['name']}</h3>";
        echo "<p class='student-details'>";
        echo "<span class='detail'><i class='icon'>👤</i> {$student['sex']}</span>";
        echo "<span class='detail'><i class='icon'>🎂</i> {$student['age']} سنة</span>";
        echo "<span class='detail'><i class='icon'>📚</i> {$student['catg']}</span>";
        echo "</p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='parent-info'>";
        echo "<h4><i class='icon'>👨‍👩‍👧‍👦</i> معلومات ولي الأمر:</h4>";
        echo "<p><strong>الاسم:</strong> " . ($student['p_name'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>الهاتف:</strong> " . ($student['p_phone'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>السكن:</strong> " . ($student['loc'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>تاريخ التسجيل:</strong> " . ($student['datein'] ?? 'غير محدد') . "</p>";
        echo "</div>";
        
        echo "<div class='login-info'>";
        echo "<h4><i class='icon'>🔐</i> معلومات تسجيل الدخول:</h4>";
        echo "<div class='login-details'>";
        echo "<div class='login-field'>";
        echo "<label>رقم الطالب:</label>";
        echo "<span class='login-value'>{$student['id']}</span>";
        echo "</div>";
        echo "<div class='login-field'>";
        echo "<label>كلمات المرور المتاحة:</label>";
        echo "<div class='passwords'>";
        echo "<span class='password-option default'>123456</span>";
        echo "<span class='password-option student-id'>{$student['id']}</span>";
        echo "<span class='password-option student-name'>{$student['name']}</span>";
        if (!empty($student['p_phone'])) {
            echo "<span class='password-option phone'>{$student['p_phone']}</span>";
        }
        if (!empty($student['datein'])) {
            echo "<span class='password-option date'>{$student['datein']}</span>";
        }
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='quick-login'>";
        echo "<button class='login-btn' onclick=\"quickLogin('{$student['id']}', '123456', '{$student['name']}')\">🚀 تسجيل دخول سريع</button>";
        echo "</div>";
        
        echo "</div>";
    }
    
    echo "</div>";
    
} else {
    echo "<div class='no-students'>";
    echo "<div class='no-students-icon'>😔</div>";
    echo "<h2>لا توجد بيانات طلاب</h2>";
    echo "<p>لم يتم العثور على أي طلاب في قاعدة البيانات</p>";
    echo "<p>يرجى التأكد من وجود بيانات في جدول stud_tb</p>";
    echo "</div>";
}

echo "<div class='instructions-section'>";
echo "<h2>📋 تعليمات تسجيل الدخول</h2>";
echo "<div class='instructions-grid'>";

echo "<div class='instruction-card'>";
echo "<div class='instruction-icon'>1️⃣</div>";
echo "<h3>اختر طالب</h3>";
echo "<p>اختر أي طالب من القائمة أعلاه</p>";
echo "</div>";

echo "<div class='instruction-card'>";
echo "<div class='instruction-icon'>2️⃣</div>";
echo "<h3>انسخ رقم الطالب</h3>";
echo "<p>انسخ رقم الطالب من البطاقة</p>";
echo "</div>";

echo "<div class='instruction-card'>";
echo "<div class='instruction-icon'>3️⃣</div>";
echo "<h3>اختر كلمة مرور</h3>";
echo "<p>استخدم أي من كلمات المرور المتاحة</p>";
echo "</div>";

echo "<div class='instruction-card'>";
echo "<div class='instruction-icon'>4️⃣</div>";
echo "<h3>سجل الدخول</h3>";
echo "<p>اذهب لصفحة تسجيل الدخول وادخل البيانات</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div class='quick-links'>";
echo "<h2>🔗 روابط سريعة</h2>";
echo "<div class='links-grid'>";
echo "<a href='simple_login.php' class='link-btn primary'>🚀 صفحة تسجيل الدخول</a>";
echo "<a href='complete_setup.php' class='link-btn secondary'>⚙️ إعداد النظام</a>";
echo "<a href='test_leave_request.php' class='link-btn success'>📝 اختبار طلب إجازة</a>";
echo "<a href='../Admin/student_app_control.php' class='link-btn warning'>👨‍💼 تحكم الأدمن</a>";
echo "</div>";
echo "</div>";

mysqli_close($con);
?>

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
        color: white;
    }

    h1 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .header-info {
        background: rgba(255,255,255,0.95);
        color: #2c3e50;
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .header-info h2 {
        margin-bottom: 1rem;
        color: #2c3e50;
        text-shadow: none;
    }

    .count {
        color: #e74c3c;
        font-weight: bold;
        font-size: 1.2em;
    }

    .instruction {
        color: #7f8c8d;
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .students-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .student-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        transition: transform 0.3s ease;
        color: #2c3e50;
    }

    .student-card:hover {
        transform: translateY(-5px);
    }

    .student-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #ecf0f1;
    }

    .student-number {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }

    .student-info h3 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 1.3rem;
        text-shadow: none;
    }

    .student-details {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .detail {
        background: #ecf0f1;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.9rem;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    .icon {
        font-size: 0.8rem;
    }

    .parent-info, .login-info {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .parent-info h4, .login-info h4 {
        color: #2c3e50;
        margin-bottom: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-shadow: none;
    }

    .parent-info p {
        margin-bottom: 0.5rem;
        color: #34495e;
    }

    .login-field {
        margin-bottom: 1rem;
    }

    .login-field label {
        display: block;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .login-value {
        background: #3498db;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .passwords {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .password-option {
        padding: 0.4rem 0.8rem;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .password-option:hover {
        transform: scale(1.05);
    }

    .password-option.default {
        background: #2ecc71;
        color: white;
    }

    .password-option.student-id {
        background: #3498db;
        color: white;
    }

    .password-option.student-name {
        background: #9b59b6;
        color: white;
    }

    .password-option.phone {
        background: #e74c3c;
        color: white;
    }

    .password-option.date {
        background: #f39c12;
        color: white;
    }

    .quick-login {
        text-align: center;
    }

    .login-btn {
        background: linear-gradient(45deg, #27ae60, #2ecc71);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
    }

    .instructions-section {
        background: rgba(255,255,255,0.95);
        color: #2c3e50;
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .instructions-section h2 {
        text-align: center;
        margin-bottom: 2rem;
        color: #2c3e50;
        text-shadow: none;
    }

    .instructions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .instruction-card {
        text-align: center;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 15px;
        transition: transform 0.3s ease;
    }

    .instruction-card:hover {
        transform: translateY(-3px);
    }

    .instruction-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .instruction-card h3 {
        color: #2c3e50;
        margin-bottom: 0.8rem;
        text-shadow: none;
    }

    .instruction-card p {
        color: #7f8c8d;
        line-height: 1.6;
    }

    .quick-links {
        background: rgba(255,255,255,0.95);
        color: #2c3e50;
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .quick-links h2 {
        text-align: center;
        margin-bottom: 2rem;
        color: #2c3e50;
        text-shadow: none;
    }

    .links-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .link-btn {
        padding: 1rem;
        border-radius: 15px;
        text-decoration: none;
        text-align: center;
        font-weight: bold;
        color: white;
        transition: all 0.3s ease;
    }

    .link-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        text-decoration: none;
        color: white;
    }

    .link-btn.primary { background: linear-gradient(45deg, #3498db, #2980b9); }
    .link-btn.secondary { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }
    .link-btn.success { background: linear-gradient(45deg, #27ae60, #2ecc71); }
    .link-btn.warning { background: linear-gradient(45deg, #f39c12, #e67e22); }

    .no-students {
        text-align: center;
        background: rgba(255,255,255,0.95);
        color: #2c3e50;
        padding: 3rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .no-students-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .no-students h2 {
        color: #2c3e50;
        margin-bottom: 1rem;
        text-shadow: none;
    }

    .no-students p {
        color: #7f8c8d;
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    @media (max-width: 768px) {
        body {
            padding: 10px;
        }

        h1 {
            font-size: 2rem;
        }

        .students-container {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .student-card {
            padding: 1rem;
        }

        .student-header {
            flex-direction: column;
            text-align: center;
        }

        .student-details {
            justify-content: center;
        }

        .instructions-grid, .links-grid {
            grid-template-columns: 1fr;
        }

        .passwords {
            justify-content: center;
        }
    }
</style>

<script>
    function quickLogin(studentId, password, studentName) {
        // حفظ البيانات في localStorage للاستخدام في صفحة تسجيل الدخول
        localStorage.setItem('quickLoginId', studentId);
        localStorage.setItem('quickLoginPassword', password);
        localStorage.setItem('quickLoginName', studentName);
        
        // إظهار رسالة تأكيد
        alert(`سيتم تسجيل الدخول باسم: ${studentName}\nرقم الطالب: ${studentId}\nكلمة المرور: ${password}`);
        
        // الانتقال إلى صفحة تسجيل الدخول
        window.location.href = 'simple_login.php';
    }

    // نسخ كلمة المرور عند النقر عليها
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('password-option')) {
            const password = e.target.textContent;
            navigator.clipboard.writeText(password).then(function() {
                e.target.style.transform = 'scale(1.1)';
                e.target.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
                
                setTimeout(() => {
                    e.target.style.transform = 'scale(1)';
                    e.target.style.boxShadow = 'none';
                }, 200);
                
                // إظهار رسالة نجاح صغيرة
                const toast = document.createElement('div');
                toast.textContent = 'تم نسخ كلمة المرور!';
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #27ae60;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 10px;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                `;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            });
        }
    });

    // إضافة CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
</script>
