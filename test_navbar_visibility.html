<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 اختبار ظهور الأزرار</title>
    <link rel="stylesheet" href="Admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="stylesheet" href="Admin/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding-top: 90px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 50px auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 900;
            color: white;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
        
        .button-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .button-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            color: white;
            border-right: 4px solid #38ef7d;
        }
        
        .status-visible {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            color: white;
            line-height: 1.8;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 20px 10px;
            box-shadow: 0 10px 30px rgba(17, 153, 142, 0.3);
        }
        
        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(17, 153, 142, 0.4);
            text-decoration: none;
            color: white;
        }
        
        @media (max-width: 768px) {
            .test-container {
                margin: 20px;
                padding: 20px;
            }
            
            .test-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل المحسن -->
    <nav>
        <div class='logo2'><img src="Admin/css/logooo.png" alt=""></div>
        <div class="logo">
            <button class='btn btn-danger mb-1' id='exit_btn'>تسجيل الخروج</button>
            <label>مرحبا بك أدمن</label>
        </div>
        <input type="checkbox" id="click">
        <label for="click" class="menu-btn" id="caps">
            <i id="capss" class="fa-solid fa-bars"></i>
        </label>
        
        <ul>
            <li><a href="Admin/about_us.php"><i class="fa-solid fa-exclamation"></i> ماذا عنا</a></li>
            <li><a href="Admin/addon/bkF.php"><i class="fa-solid fa-server"></i> BackUP</a></li>
            <li><a href="Admin/attandec.php" style="color:#FFF5CD">التقارير - حضور الطلاب</a></li>
            <li><a href="Admin/reports.php" style="color:#FFF5CD">التقرير - الطلاب الفعالين</a></li>
            <li><a href="Admin/info_employ.php"><i class="fa-solid fa-user-group"></i> الموظفين</a></li>
            <li><a href="Admin/info_user.php"><i class="fa-solid fa-users"></i> المستخدمين</a></li>
            <li><a href="Admin/acounting.php"><i class="fa-solid fa-file-signature"></i> الحسابات</a></li>
            <li><a href="Admin/info_depit.php"><i class="fa-solid fa-money-bill-transfer"></i> المصاريف</a></li>
            <li><a href="Admin/infost.php"><i class="fa-solid fa-graduation-cap"></i> الطلاب</a></li>
            <li><a href="Admin/addstud.php"><i class="fa-solid fa-user-plus"></i> اضافة طالب</a></li>
            <li><a class="active" href="Admin/home.php">الرئيسية</a></li>
        </ul>
    </nav>

    <div class="test-container">
        <h1 class="test-title">
            <i class="fas fa-check-circle"></i>
            اختبار ظهور أزرار التنقل
        </h1>
        
        <div class="instructions">
            <h3 style="color: #38ef7d; margin-bottom: 20px;">
                <i class="fas fa-info-circle"></i> التحسينات المطبقة:
            </h3>
            <ul style="list-style: none; padding: 0;">
                <li>✅ <strong>شريط التنقل ثابت:</strong> position: fixed في أعلى الصفحة</li>
                <li>✅ <strong>مساحة للمحتوى:</strong> padding-top: 90px للـ body</li>
                <li>✅ <strong>أزرار مرئية:</strong> display: flex للكمبيوتر</li>
                <li>✅ <strong>قائمة منسدلة:</strong> للهاتف المحمول</li>
                <li>✅ <strong>تصميم مبهر:</strong> تأثيرات زجاجية وحركية</li>
            </ul>
        </div>
        
        <div class="button-list">
            <h3 style="color: white; text-align: center; margin-bottom: 20px;">
                <i class="fas fa-list"></i> قائمة الأزرار المطلوبة
            </h3>
            
            <div class="button-item">
                <span><i class="fa-solid fa-exclamation"></i> ماذا عنا</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-server"></i> BackUP</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-chart-line"></i> التقارير - حضور الطلاب</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-chart-bar"></i> التقرير - الطلاب الفعالين</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-user-group"></i> الموظفين</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-users"></i> المستخدمين</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-file-signature"></i> الحسابات</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-money-bill-transfer"></i> المصاريف</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-graduation-cap"></i> الطلاب</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-user-plus"></i> اضافة طالب</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
            
            <div class="button-item">
                <span><i class="fa-solid fa-home"></i> الرئيسية</span>
                <span class="status-visible">✅ مرئي</span>
            </div>
        </div>
        
        <div style="background: rgba(255, 255, 255, 0.05); border-radius: 20px; padding: 25px; margin: 30px 0;">
            <h4 style="color: white; text-align: center; margin-bottom: 15px;">
                <i class="fas fa-mobile-alt"></i> تعليمات الاختبار
            </h4>
            <ul style="color: rgba(255, 255, 255, 0.9); line-height: 2;">
                <li>🖥️ <strong>في الكمبيوتر:</strong> يجب أن تظهر جميع الأزرار في شريط أفقي</li>
                <li>📱 <strong>في الهاتف:</strong> اضغط على أيقونة القائمة (☰) لإظهار الأزرار</li>
                <li>🔄 <strong>شريط ثابت:</strong> يبقى في أعلى الصفحة عند التمرير</li>
                <li>✨ <strong>تأثيرات:</strong> تأثيرات حركية عند التمرير على الأزرار</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="Admin/home.php" class="demo-button">
                <i class="fas fa-crown"></i> اختبار لوحة تحكم الأدمن
            </a>
            <a href="Admin/infost.php" class="demo-button" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <i class="fas fa-graduation-cap"></i> اختبار صفحة الطلاب
            </a>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px;">
            <h4 style="color: white; margin-bottom: 15px;">
                <i class="fas fa-check-circle" style="color: #38ef7d;"></i>
                شريط التنقل ثابت ومرئي!
            </h4>
            <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                جميع الأزرار الآن ظاهرة وثابتة في أعلى جميع الصفحات
            </p>
        </div>
    </div>

    <script>
        console.log('🔍 اختبار ظهور الأزرار جاهز!');
        
        // اختبار ظهور الأزرار
        function testButtonVisibility() {
            const navButtons = document.querySelectorAll('nav ul li a');
            console.log(`عدد الأزرار المكتشفة: ${navButtons.length}`);
            
            navButtons.forEach((button, index) => {
                const rect = button.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                console.log(`الزر ${index + 1}: ${button.textContent.trim()} - ${isVisible ? 'مرئي' : 'مخفي'}`);
            });
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', testButtonVisibility);
        
        // اختبار عند تغيير حجم الشاشة
        window.addEventListener('resize', testButtonVisibility);
    </script>
</body>
</html>
