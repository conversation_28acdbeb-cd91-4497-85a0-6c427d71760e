<?php
session_start();
include "addon/dbcon.php";

// إنشاء جلسة طالب تجريبية للاختبار
if (!isset($_SESSION['student'])) {
    $_SESSION['student'] = (object) [
        'id' => 1,
        'name' => 'طالب تجريبي',
        'age' => 10,
        'sex' => 'ذكر',
        'catg' => 'الصف الأول',
        'p_name' => 'ولي أمر تجريبي',
        'p_phone' => '07901234567'
    ];
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $student = $_SESSION['student'];
    $leave_type = trim($_POST['leave_type']);
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    $reason = trim($_POST['reason']);
    
    if (!empty($leave_type) && !empty($start_date) && !empty($end_date) && !empty($reason)) {
        try {
            // إنشاء الجدول إذا لم يكن موجوداً
            $create_table = "CREATE TABLE IF NOT EXISTS `student_leaves` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `student_id` int(11) NOT NULL,
              `student_name` varchar(255) NOT NULL,
              `leave_type` varchar(100) NOT NULL,
              `start_date` date NOT NULL,
              `end_date` date NOT NULL,
              `reason` text NOT NULL,
              `request_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `status` varchar(50) NOT NULL DEFAULT 'قيد المراجعة',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            mysqli_query($con, $create_table);
            
            // إدراج الطلب
            $sql = "INSERT INTO student_leaves (student_id, student_name, leave_type, start_date, end_date, reason, status) 
                    VALUES ('$student->id', '$student->name', '$leave_type', '$start_date', '$end_date', '$reason', 'قيد المراجعة')";
            
            if (mysqli_query($con, $sql)) {
                $message = 'تم إرسال طلب الإجازة بنجاح! ✅';
                $messageType = 'success';
            } else {
                $message = 'خطأ في إرسال الطلب: ' . mysqli_error($con);
                $messageType = 'error';
            }
        } catch (Exception $e) {
            $message = 'خطأ: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى ملء جميع الحقول';
        $messageType = 'error';
    }
}

// جلب الطلبات السابقة
$leaves_sql = "SELECT * FROM student_leaves WHERE student_id = 1 ORDER BY request_date DESC LIMIT 5";
$leaves_result = mysqli_query($con, $leaves_sql);
$leaves = [];
if ($leaves_result) {
    while ($row = mysqli_fetch_assoc($leaves_result)) {
        $leaves[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلب الإجازة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .card h2 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .leaves-list {
            margin-top: 2rem;
        }

        .leave-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            border-right: 4px solid #3498db;
        }

        .leave-item h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .leave-item p {
            margin: 0.3rem 0;
            color: #6c757d;
        }

        .status {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .info-box h4 {
            color: #0066cc;
            margin-bottom: 0.5rem;
        }

        .student-info {
            background: #f0f8ff;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- معلومات الطالب -->
        <div class="card">
            <h2>🧪 اختبار نظام طلب الإجازة</h2>
            <div class="student-info">
                <h4>معلومات الطالب التجريبي:</h4>
                <p><strong>الاسم:</strong> <?php echo $_SESSION['student']->name; ?></p>
                <p><strong>رقم الطالب:</strong> <?php echo $_SESSION['student']->id; ?></p>
                <p><strong>الصف:</strong> <?php echo $_SESSION['student']->catg; ?></p>
            </div>
        </div>

        <!-- نموذج طلب الإجازة -->
        <div class="card">
            <h2>📝 طلب إجازة جديد</h2>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'error'; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="leave_type">نوع الإجازة:</label>
                    <select id="leave_type" name="leave_type" class="form-control" required>
                        <option value="">اختر نوع الإجازة</option>
                        <option value="مرضية">إجازة مرضية</option>
                        <option value="طارئة">إجازة طارئة</option>
                        <option value="عائلية">إجازة عائلية</option>
                        <option value="شخصية">إجازة شخصية</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="start_date">تاريخ البداية:</label>
                    <input type="date" id="start_date" name="start_date" class="form-control" 
                           min="<?php echo date('Y-m-d'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="end_date">تاريخ النهاية:</label>
                    <input type="date" id="end_date" name="end_date" class="form-control" 
                           min="<?php echo date('Y-m-d'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="reason">سبب الإجازة:</label>
                    <textarea id="reason" name="reason" class="form-control" rows="4" 
                              placeholder="اكتب سبب طلب الإجازة..." required></textarea>
                </div>
                
                <button type="submit" class="btn">🚀 إرسال الطلب</button>
            </form>
        </div>

        <!-- الطلبات السابقة -->
        <?php if (!empty($leaves)): ?>
        <div class="card">
            <h2>📋 الطلبات السابقة</h2>
            <div class="leaves-list">
                <?php foreach ($leaves as $leave): ?>
                    <div class="leave-item">
                        <h4><?php echo htmlspecialchars($leave['leave_type']); ?></h4>
                        <p><strong>من:</strong> <?php echo date('Y/m/d', strtotime($leave['start_date'])); ?> 
                           <strong>إلى:</strong> <?php echo date('Y/m/d', strtotime($leave['end_date'])); ?></p>
                        <p><strong>السبب:</strong> <?php echo htmlspecialchars($leave['reason']); ?></p>
                        <p><strong>تاريخ الطلب:</strong> <?php echo date('Y/m/d H:i', strtotime($leave['request_date'])); ?></p>
                        <span class="status status-pending"><?php echo $leave['status']; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- روابط مفيدة -->
        <div class="card">
            <h2>🔗 روابط مفيدة</h2>
            <div style="text-align: center;">
                <p><a href="setup_database.php" style="color: #007bff;">إعداد قاعدة البيانات</a></p>
                <p><a href="simple_login.php" style="color: #007bff;">تسجيل الدخول</a></p>
                <p><a href="index.php" style="color: #007bff;">الصفحة الرئيسية</a></p>
                <p><a href="request_leave.php" style="color: #007bff;">صفحة طلب الإجازة الأصلية</a></p>
            </div>
        </div>
    </div>

    <script>
        // تحديث تاريخ النهاية عند تغيير تاريخ البداية
        document.getElementById('start_date').addEventListener('change', function() {
            document.getElementById('end_date').min = this.value;
        });
    </script>
</body>
</html>
