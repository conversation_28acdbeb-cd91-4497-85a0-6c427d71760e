<?php
session_start();
if (isset($_SESSION['user'])) {
    if ($_SESSION['user']->role === "User") {
    } else {
        header("location:../login.php", true);
        die("");
        echo "dont work";
    }
} else {
    header("location:../login.php", true);
    die("");
}

include("dbcon.php");

// طباعة كل البيانات اللي واصلة للتأكد
error_log("POST Data: " . print_r($_POST, true));

if (isset($_POST['id']) && isset($_POST['stat'])) {
    $id = intval($_POST['id']); // هذا هو id_employ من جدول employ_tb
    $stat = mysqli_real_escape_string($con, $_POST['stat']);
    $datenow = date('Y-m-d');
    
    // معالجة تواريخ الإجازة إذا كانت موجودة
    $leave_start = null;
    $leave_end = null;
    if (isset($_POST['leave_start']) && isset($_POST['leave_end'])) {
        $leave_start = mysqli_real_escape_string($con, $_POST['leave_start']);
        $leave_end = mysqli_real_escape_string($con, $_POST['leave_end']);
    }
    
    // طباعة للتأكد من القيم (يمكن حذفها لاحقاً)
    error_log("Employee ID: " . $id . ", Status: " . $stat . ", Date: " . $datenow);
    if ($leave_start && $leave_end) {
        error_log("Leave Start: " . $leave_start . ", Leave End: " . $leave_end);
    }
    
    // تحقق من وجود الموظف أولاً
    $employee_check = mysqli_query($con, "SELECT id_employ FROM employ_tb WHERE id_employ = $id");
    if (mysqli_num_rows($employee_check) == 0) {
        echo "employee_not_found";
        exit;
    }
    
    // تحقق من وجود تسجيل مسبق لهذا الموظف اليوم
    $check = mysqli_query($con, "SELECT * FROM stat2 WHERE id_employee = $id AND data_stat = '$datenow'");
    
    if (mysqli_num_rows($check) > 0) {
        echo 1; // تم تسجيل الموظف اليوم
        exit;
    }
    
    // تحقق من حالة الإجازة الحالية أو المستقبلية
    $leave_check = mysqli_query($con, "SELECT * FROM stat2 WHERE id_employee = $id 
                                      AND stat_employee = 'إجازة' 
                                      AND (leave_start_date <= '$datenow' AND leave_end_date >= '$datenow')");
    
    if (mysqli_num_rows($leave_check) > 0) {
        if ($stat == 'غائب' || $stat == 'حاضر') {
            echo "has_leave"; // عنده إجازة حالياً، ما ينفعش يسجل حاضر أو غياب
            exit;
        }
    }
    
    // تسجيل الحالة الجديدة مع تواريخ الإجازة إذا كانت موجودة
    if ($stat == 'إجازة' && $leave_start && $leave_end) {
        $insert = mysqli_query($con, "INSERT INTO stat2 (id_employee, stat_employee, data_stat, leave_start_date, leave_end_date) 
                                     VALUES ($id, '$stat', '$datenow', '$leave_start', '$leave_end')");
    } else {
        $insert = mysqli_query($con, "INSERT INTO stat2 (id_employee, stat_employee, data_stat) 
                                     VALUES ($id, '$stat', '$datenow')");
    }
    
    if ($insert) {
        echo "done";
    } else {
        echo "error: " . mysqli_error($con);
    }
} else {
    echo "missing_data";
}
?>