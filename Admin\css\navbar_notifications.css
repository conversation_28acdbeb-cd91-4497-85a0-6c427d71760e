/* تحسينات شريط التنقل مع الإشعارات */
nav ul {
    position: relative;
}

/* تحسين زر الإشعارات في الشريط الجانبي */
.notifications-nav-item a {
    position: relative !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.notifications-nav-item a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px) !important;
}

.notifications-nav-item a i {
    margin-left: 8px !important;
    font-size: 1.1rem !important;
}

/* تحسين الشارة */
.notification-badge-nav {
    position: absolute !important;
    top: -8px !important;
    right: 10px !important;
    background: #ff4757 !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 3px 7px !important;
    font-size: 0.7rem !important;
    font-weight: bold !important;
    min-width: 20px !important;
    text-align: center !important;
    animation: pulse 2s infinite !important;
    border: 2px solid white !important;
    z-index: 10 !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3) !important;
}

/* تحسين موقع الترحيب */
.top-user-section {
    position: absolute;
    top: 15px;
    right: 20px;
    z-index: 1001;
}

.user-form {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.welcome-label {
    color: white;
    font-weight: 600;
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    white-space: nowrap;
}

#exit_btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#exit_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .top-user-section {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px 20px;
        text-align: center;
    }
    
    .user-form {
        flex-direction: column;
        gap: 10px;
    }
    
    .welcome-label {
        font-size: 0.9rem;
    }
    
    #exit_btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    .notification-badge-nav {
        top: -5px !important;
        right: 5px !important;
        padding: 2px 5px !important;
        font-size: 0.6rem !important;
        min-width: 16px !important;
    }
}

/* تحسين النافذة المنبثقة للهواتف */
@media (max-width: 480px) {
    .notifications-modal-content {
        width: 98% !important;
        margin: 1% !important;
        max-height: 95vh !important;
    }
    
    .notifications-modal-header {
        padding: 12px 15px !important;
    }
    
    .notifications-modal-header h3 {
        font-size: 1rem !important;
    }
    
    .notification-item {
        padding: 12px 15px !important;
    }
    
    .notification-title {
        font-size: 0.9rem !important;
    }
    
    .notification-message {
        font-size: 0.8rem !important;
    }
}

/* تأثيرات إضافية */
.notifications-nav-item a.active {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تحسين الانيميشن */
@keyframes pulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 4px 10px rgba(255, 71, 87, 0.5);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
}

/* إخفاء الشارة عند عدم وجود إشعارات */
.notification-badge-nav[style*='display: none'] {
    display: none !important;
}

/* تحسين التباعد في الشريط الجانبي */
nav ul li {
    margin-bottom: 2px;
}

nav ul li a {
    padding: 12px 20px !important;
    border-radius: 5px !important;
    margin: 0 10px !important;
}
