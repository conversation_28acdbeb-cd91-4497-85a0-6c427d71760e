<?php
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if(isset($_SESSION['user'])){
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه إلى الصفحة المناسبة
    if($_SESSION['user']->role === "Admin"){
        header("location: Admin/home.php");
        exit();
    } elseif($_SESSION['user']->role === "User"){
        header("location: Users/home.php");
        exit();
    } elseif($_SESSION['user']->role === "Mod"){
        header("location: Moda/home.php");
        exit();
    }
}

$loginMessage = '';
$loginStatus = '';

if(isset($_POST['login'])){
    $user='kidzrcle_rwda';
    $pass='kidzrcle_rwda';
    $pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda',$user,$pass);
    $login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
    $login->bindParam('u_user',$_POST['u_user']);
    $login->bindParam('u_pass',$_POST['u_pass']);
    $login->execute();
    
    if($login->rowCount()===1){
        $user=$login->fetchObject();
        $_SESSION['user']=$user;
        if($user->role==='Admin'){
            $loginMessage = "مرحباً بك أدمن! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Admin/home.php'; }, 2000);</script>";
        }elseif($user->role==='User'){
            $loginMessage = "مرحباً بك مستخدم! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Users/home.php'; }, 2000);</script>";
        }elseif($user->role==="Mod"){
            $loginMessage = "مرحباً بك مدقق! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Moda/home.php'; }, 2000);</script>";
        }
    }else{
        $loginMessage = "خطأ في اسم المستخدم أو كلمة المرور";
        $loginStatus = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="stylesheet" href="css/login-style.css">
    <link rel="icon" href="icon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <title>تسجيل دخول - الروضة الأصلي</title>
    
    <style>
        /* تأثيرات إضافية خاصة */
        .login-container {
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: rotate 10s linear infinite;
            z-index: -1;
        }
        
        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }
        
        .welcome-text {
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .form-control {
            position: relative;
        }
        
        .form-control::placeholder {
            color: #999;
            transition: all 0.3s ease;
        }
        
        .form-control:focus::placeholder {
            color: transparent;
        }
        
        /* تأثير الموجات */
        .wave-effect {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" fill="rgba(255,255,255,0.1)"></path></svg>') repeat-x;
            animation: wave 3s ease-in-out infinite;
            pointer-events: none;
        }
        
        @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-25px); }
        }
    </style>
</head>
<body>
    <!-- عناصر متحركة للخلفية -->
    <div class="sparkle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="sparkle" style="top: 60%; left: 80%; animation-delay: 1s;"></div>
    <div class="sparkle" style="top: 80%; left: 20%; animation-delay: 2s;"></div>
    <div class="sparkle" style="top: 30%; left: 70%; animation-delay: 1.5s;"></div>
    <div class="sparkle" style="top: 10%; left: 50%; animation-delay: 0.5s;"></div>
    <div class="sparkle" style="top: 70%; left: 30%; animation-delay: 2.5s;"></div>
    
    <div class="login-container">
        <div class="wave-effect"></div>
        
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1 class="welcome-text">أهلاً وسهلاً</h1>
            <p class="subtitle">مرحباً بك في الروضة الأصلي</p>
        </div>
        
        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <div class="input-wrapper">
                    <input type="text" id="username" name="u_user" class="form-control" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-wrapper">
                    <input type="password" id="password" name="u_pass" class="form-control" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>
            
            <button type="submit" name="login" class="login-btn">
                <i class="fas fa-sign-in-alt" style="margin-left: 10px;"></i>
                تسجيل الدخول
            </button>
        </form>
    </div>
    
    <footer class="footer">
        <p>جميع الحقوق محفوظة لمؤسسة الروضة الأصلي © 2024</p>
    </footer>

    <script src="js/login-effects.js"></script>
    <script>
        // عرض الإشعار إذا كان هناك رسالة
        <?php if($loginMessage): ?>
            document.addEventListener('DOMContentLoaded', function() {
                <?php if($loginStatus === 'success'): ?>
                    showNotification('<?php echo $loginMessage; ?>', 'success', 'تم بنجاح!');
                <?php else: ?>
                    showNotification('<?php echo $loginMessage; ?>', 'error', 'خطأ!');
                    // إضافة تأثير الاهتزاز للنموذج عند الخطأ
                    setTimeout(() => {
                        const container = document.querySelector('.login-container');
                        shakeElement(container);
                    }, 100);
                <?php endif; ?>
            });
        <?php endif; ?>
        
        // تأثيرات إضافية خاصة بهذه الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الضوء المتحرك
            const container = document.querySelector('.login-container');
            container.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                this.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(255,255,255,0.98) 0%, rgba(255,255,255,0.95) 100%)`;
            });
            
            container.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(255, 255, 255, 0.95)';
            });
        });
    </script>
</body>
</html>
