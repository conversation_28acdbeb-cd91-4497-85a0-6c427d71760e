<?php
session_start();
include "addon/dbcon.php";

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || ($_SESSION['user']->role !== "Admin" && $_SESSION['user']->role !== "Mod")) {
    echo "غير مصرح لك بالوصول";
    exit();
}

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "معرف الطلب مطلوب";
    exit();
}

$need_id = intval($_GET['id']);

// جلب بيانات الطلب مع اسم المستخدم مباشرة
$sql = "SELECT nr.*, u.user_name
        FROM needs_requests nr
        LEFT JOIN users_tb u ON nr.user_id = u.id_user
        WHERE nr.id_need = $need_id";
$result = mysqli_query($con, $sql);

if (!$result || mysqli_num_rows($result) == 0) {
    echo "الطلب غير موجود";
    exit();
}

$need = mysqli_fetch_assoc($result);
$user_name = $need['user_name'] ?? 'غير محدد';

// جلب اسم المدير الذي رد على الطلب
$admin_name = 'غير محدد';
if (!empty($need['response_by'])) {
    $admin_sql = "SELECT user_name FROM users_tb WHERE id_user = " . $need['response_by'];
    $admin_result = mysqli_query($con, $admin_sql);
    if ($admin_result && mysqli_num_rows($admin_result) > 0) {
        $admin_row = mysqli_fetch_assoc($admin_result);
        $admin_name = $admin_row['user_name'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب احتياج - <?php echo htmlspecialchars($need['need_name']); ?></title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: 10px;
                font-size: 12px;
                line-height: 1.3;
            }
            .no-print { display: none; }
            .print-container {
                box-shadow: none;
                padding: 0;
                margin: 0;
            }
            .header {
                border-bottom: 2px solid #000;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }
            .info-section {
                margin-bottom: 15px;
                page-break-inside: avoid;
            }
            .info-grid {
                gap: 8px;
            }
            .info-item {
                padding: 6px;
                margin-bottom: 4px;
            }
        }

        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            line-height: 1.4;
            color: #000;
            max-width: 210mm;
            margin: 0 auto;
            padding: 15px;
            background: white;
            font-size: 14px;
        }

        .print-container {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #000;
            margin: 0 0 5px 0;
            font-size: 22px;
            font-weight: bold;
        }

        .header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }

        .info-section {
            margin-bottom: 18px;
        }

        .info-title {
            background: #000;
            color: white;
            padding: 8px 12px;
            margin: 0 0 10px 0;
            font-weight: bold;
            font-size: 14px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 10px;
        }

        .info-item {
            background: #f9f9f9;
            padding: 8px;
            border: 1px solid #ddd;
            border-right: 3px solid #000;
        }
        
        .info-label {
            font-weight: bold;
            color: #000;
            display: block;
            margin-bottom: 3px;
            font-size: 12px;
        }

        .info-value {
            color: #333;
            font-size: 13px;
        }

        .description-box {
            background: #f9f9f9;
            padding: 12px;
            border: 1px solid #ddd;
            margin: 10px 0;
            font-size: 13px;
            line-height: 1.4;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 10px;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
            border: 1px solid #000;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }

        .print-btn {
            background: #000;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 15px 5px;
        }

        .print-btn:hover {
            background: #333;
        }

        .footer {
            text-align: center;
            margin-top: 25px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="header">
            <h1>طلب احتياج</h1>
            <p>نظام إدارة الحضانة</p>
        </div>

        <!-- معلومات أساسية في صف واحد -->
        <div class="info-section">
            <h3 class="info-title">معلومات الطلب الأساسية</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">رقم الطلب:</span>
                    <span class="info-value"><?php echo $need['id_need']; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">اسم المستخدم:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user_name); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الطلب:</span>
                    <span class="info-value"><?php echo date('Y/m/d H:i', strtotime($need['request_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="status-badge status-<?php
                        echo $need['status'] == 'قيد المراجعة' ? 'pending' :
                            ($need['status'] == 'تم التوفير' ? 'approved' : 'rejected');
                    ?>">
                        <?php echo $need['status']; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- تفاصيل الاحتياج -->
        <div class="info-section">
            <h3 class="info-title">تفاصيل الاحتياج</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">اسم الاحتياج:</span>
                    <span class="info-value"><?php echo htmlspecialchars($need['need_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع الاحتياج:</span>
                    <span class="info-value"><?php echo htmlspecialchars($need['need_type']); ?></span>
                </div>
            </div>
        </div>

        <?php if (!empty($need['description'])): ?>
        <div class="info-section">
            <h3 class="info-title">وصف الاحتياج</h3>
            <div class="description-box">
                <?php echo nl2br(htmlspecialchars($need['description'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($need['admin_response'])): ?>
        <div class="info-section">
            <h3 class="info-title">رد الإدارة</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">تاريخ الرد:</span>
                    <span class="info-value"><?php echo date('Y/m/d H:i', strtotime($need['response_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">رد بواسطة:</span>
                    <span class="info-value"><?php echo htmlspecialchars($admin_name); ?></span>
                </div>
            </div>
            <div class="description-box">
                <?php echo nl2br(htmlspecialchars($need['admin_response'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>تم طباعة هذا التقرير في: <?php echo date('Y/m/d H:i:s'); ?></p>
        </div>
    </div>

    <div class="no-print" style="text-align: center;">
        <button class="print-btn" onclick="window.print()">طباعة</button>
        <button class="print-btn" onclick="window.close()" style="background: #6c757d;">إغلاق</button>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        }
    </script>
</body>
</html>
