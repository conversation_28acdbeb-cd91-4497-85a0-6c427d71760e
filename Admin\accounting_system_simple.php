<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    header("location: ../login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include "db_connection.php";

// التحقق من الاتصال
if (!$conn) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// معالجة الفلاتر مع التحقق من صحة البيانات
$filter_type = isset($_GET['filter_type']) ? trim($_GET['filter_type']) : 'dashboard';

// التحقق من صحة نوع الفلتر
$allowed_filters = ['dashboard', 'revenue', 'expenses', 'salaries'];
if (!in_array($filter_type, $allowed_filters)) {
    $filter_type = 'dashboard';
}

// حساب الإحصائيات الأساسية مع معالجة الأخطاء
$stats = [
    'revenue' => ['total' => 0, 'count' => 0],
    'expenses' => ['total' => 0, 'count' => 0],
    'salaries' => ['total' => 0, 'count' => 0],
    'net_profit' => 0
];

try {
    // إجمالي الإيرادات
    $revenue_query = "SELECT COUNT(*) as count, COALESCE(SUM(cash_stud), 0) as total FROM stud_pay WHERE cash_stud > 0";
    $revenue_result = $conn->query($revenue_query);
    if ($revenue_result && $row = $revenue_result->fetch_assoc()) {
        $stats['revenue']['total'] = intval($row['total']);
        $stats['revenue']['count'] = intval($row['count']);
    }

    // إجمالي المصروفات
    $expenses_query = "SELECT COUNT(*) as count, COALESCE(SUM(depit_cash), 0) as total FROM depit_tb WHERE depit_cash > 0";
    $expenses_result = $conn->query($expenses_query);
    if ($expenses_result && $row = $expenses_result->fetch_assoc()) {
        $stats['expenses']['total'] = intval($row['total']);
        $stats['expenses']['count'] = intval($row['count']);
    }

    // إجمالي الرواتب
    $salaries_query = "SELECT COUNT(*) as count, COALESCE(SUM(salary), 0) as total FROM employ_tb WHERE salary > 0";
    $salaries_result = $conn->query($salaries_query);
    if ($salaries_result && $row = $salaries_result->fetch_assoc()) {
        $stats['salaries']['total'] = intval($row['total']);
        $stats['salaries']['count'] = intval($row['count']);
    }

    // حساب صافي الربح
    $stats['net_profit'] = $stats['revenue']['total'] - $stats['expenses']['total'] - $stats['salaries']['total'];

} catch (Exception $e) {
    error_log("خطأ في النظام المحاسبي: " . $e->getMessage());
    // في حالة الخطأ، استخدم قيم افتراضية
}

// جلب المستخدمين
$users_result = null;
try {
    $users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
    $users_result = $conn->query($users_query);
} catch (Exception $e) {
    error_log("خطأ في جلب المستخدمين: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المحاسبي - روضة الأطفال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            padding: 30px;
            max-width: 1400px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 25px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-card.revenue { border-left-color: #28a745; }
        .stat-card.expenses { border-left-color: #dc3545; }
        .stat-card.salaries { border-left-color: #ffc107; }
        .stat-card.profit { border-left-color: #007bff; }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-title {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .content-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .table th {
            background: #343a40;
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-calculator"></i> النظام المحاسبي المتكامل</h1>
            <p class="mb-0">روضة الأطفال - إدارة متكاملة للحسابات والمالية</p>
        </div>

        <!-- Navigation -->
        <div class="nav-buttons">
            <a href="?filter_type=dashboard" class="nav-btn btn-primary <?= $filter_type == 'dashboard' ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="?filter_type=revenue" class="nav-btn btn-success <?= $filter_type == 'revenue' ? 'active' : '' ?>">
                <i class="fas fa-arrow-up"></i> الإيرادات
            </a>
            <a href="?filter_type=expenses" class="nav-btn btn-danger <?= $filter_type == 'expenses' ? 'active' : '' ?>">
                <i class="fas fa-arrow-down"></i> المصروفات
            </a>
            <a href="?filter_type=salaries" class="nav-btn btn-warning <?= $filter_type == 'salaries' ? 'active' : '' ?>">
                <i class="fas fa-users"></i> الرواتب
            </a>
            <a href="acounting.php" class="nav-btn btn-secondary">
                <i class="fas fa-chart-bar"></i> النظام القديم
            </a>
            <a href="home.php" class="nav-btn btn-info">
                <i class="fas fa-home"></i> الرئيسية
            </a>
        </div>

        <?php if ($filter_type == 'dashboard'): ?>
        <!-- Dashboard Statistics -->
        <div class="stats-grid">
            <div class="stat-card revenue" onclick="location.href='?filter_type=revenue'">
                <i class="fas fa-arrow-up stat-icon text-success"></i>
                <div class="stat-value text-success"><?= number_format($stats['revenue']['total']) ?></div>
                <div class="stat-title">إجمالي الإيرادات</div>
                <small><?= $stats['revenue']['count'] ?> معاملة</small>
            </div>

            <div class="stat-card expenses" onclick="location.href='?filter_type=expenses'">
                <i class="fas fa-arrow-down stat-icon text-danger"></i>
                <div class="stat-value text-danger"><?= number_format($stats['expenses']['total']) ?></div>
                <div class="stat-title">إجمالي المصروفات</div>
                <small><?= $stats['expenses']['count'] ?> معاملة</small>
            </div>

            <div class="stat-card salaries" onclick="location.href='?filter_type=salaries'">
                <i class="fas fa-users stat-icon text-warning"></i>
                <div class="stat-value text-warning"><?= number_format($stats['salaries']['total']) ?></div>
                <div class="stat-title">إجمالي الرواتب</div>
                <small><?= $stats['salaries']['count'] ?> موظف</small>
            </div>

            <div class="stat-card profit">
                <i class="fas fa-chart-line stat-icon <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"></i>
                <div class="stat-value <?= $stats['net_profit'] >= 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($stats['net_profit']) ?></div>
                <div class="stat-title">صافي الربح</div>
                <small><?= $stats['net_profit'] >= 0 ? 'ربح' : 'خسارة' ?></small>
            </div>
        </div>

        <div class="content-area">
            <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
            <div class="alert alert-info">
                <h6>مرحباً بك في النظام المحاسبي المتكامل!</h6>
                <p class="mb-0">
                    يمكنك الآن الضغط على أي من الإحصائيات أعلاه لعرض التفاصيل، أو استخدام الأزرار في الأعلى للتنقل بين الأقسام المختلفة.
                </p>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">الإيرادات</h6>
                        </div>
                        <div class="card-body">
                            <p>إجمالي: <strong><?= number_format($stats['revenue']['total']) ?></strong> دينار</p>
                            <p>عدد المعاملات: <strong><?= $stats['revenue']['count'] ?></strong></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">المصروفات والرواتب</h6>
                        </div>
                        <div class="card-body">
                            <p>المصروفات: <strong><?= number_format($stats['expenses']['total']) ?></strong> دينار</p>
                            <p>الرواتب: <strong><?= number_format($stats['salaries']['total']) ?></strong> دينار</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'revenue'): ?>
        <!-- Revenue Section -->
        <div class="content-area">
            <h4><i class="fas fa-arrow-up text-success"></i> تفاصيل الإيرادات</h4>
            
            <?php
            try {
                $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name, stud_pay.id_pay
                                  FROM stud_tb 
                                  INNER JOIN stud_pay ON stud_pay.id_stud = stud_tb.id
                                  INNER JOIN users_tb ON stud_tb.userID = users_tb.id_user
                                  WHERE stud_pay.cash_stud > 0
                                  ORDER BY stud_pay.datein DESC
                                  LIMIT 50";

                $revenue_result = $conn->query($revenue_query);

                if ($revenue_result && $revenue_result->num_rows > 0) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th>اسم الطالب</th>';
                    echo '<th>المبلغ</th>';
                    echo '<th>التاريخ</th>';
                    echo '<th>المستخدم</th>';
                    echo '<th>رقم الوصل</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    $total = 0;
                    while ($row = $revenue_result->fetch_assoc()) {
                        $total += $row['cash_stud'];
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($row['name']) . '</td>';
                        echo '<td><span class="badge bg-success">' . number_format($row['cash_stud']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '<td>' . $row['id_pay'] . '</td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-success">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th>' . number_format($total) . '</th>';
                    echo '<th colspan="3">' . $revenue_result->num_rows . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning">لا توجد إيرادات</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض الإيرادات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'expenses'): ?>
        <!-- Expenses Section -->
        <div class="content-area">
            <h4><i class="fas fa-arrow-down text-danger"></i> تفاصيل المصروفات</h4>
            
            <?php
            try {
                $expenses_query = "SELECT depit_tb.*, users_tb.user_name
                                   FROM depit_tb
                                   INNER JOIN users_tb ON depit_tb.userID = users_tb.id_user
                                   WHERE depit_tb.depit_cash > 0
                                   ORDER BY depit_tb.depit_date DESC
                                   LIMIT 50";

                $expenses_result = $conn->query($expenses_query);

                if ($expenses_result && $expenses_result->num_rows > 0) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th>وصف المصروف</th>';
                    echo '<th>المبلغ</th>';
                    echo '<th>التاريخ</th>';
                    echo '<th>المستخدم</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    $total = 0;
                    while ($row = $expenses_result->fetch_assoc()) {
                        $total += $row['depit_cash'];
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($row['depit_note']) . '</td>';
                        echo '<td><span class="badge bg-danger">' . number_format($row['depit_cash']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-danger">';
                    echo '<th>الإجمالي:</th>';
                    echo '<th>' . number_format($total) . '</th>';
                    echo '<th colspan="2">' . $expenses_result->num_rows . ' معاملة</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning">لا توجد مصروفات</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض المصروفات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <?php if ($filter_type == 'salaries'): ?>
        <!-- Salaries Section -->
        <div class="content-area">
            <h4><i class="fas fa-users text-warning"></i> تفاصيل الرواتب</h4>
            
            <?php
            try {
                $salaries_query = "SELECT employ_tb.*, users_tb.user_name
                                   FROM employ_tb
                                   INNER JOIN users_tb ON employ_tb.userID = users_tb.id_user
                                   WHERE employ_tb.salary > 0
                                   ORDER BY employ_tb.salary DESC";

                $salaries_result = $conn->query($salaries_query);

                if ($salaries_result && $salaries_result->num_rows > 0) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th>اسم الموظف</th>';
                    echo '<th>المنصب</th>';
                    echo '<th>الراتب</th>';
                    echo '<th>تاريخ البداية</th>';
                    echo '<th>المستخدم</th>';
                    echo '</tr>';
                    echo '</thead><tbody>';

                    $total = 0;
                    while ($row = $salaries_result->fetch_assoc()) {
                        $total += $row['salary'];
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($row['f_name']) . '</td>';
                        echo '<td>' . htmlspecialchars($row['job']) . '</td>';
                        echo '<td><span class="badge bg-warning text-dark">' . number_format($row['salary']) . '</span></td>';
                        echo '<td>' . date('Y-m-d', strtotime($row['date_start'])) . '</td>';
                        echo '<td><span class="badge bg-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '<tfoot><tr class="table-warning">';
                    echo '<th colspan="2">الإجمالي:</th>';
                    echo '<th>' . number_format($total) . '</th>';
                    echo '<th colspan="2">' . $salaries_result->num_rows . ' موظف</th>';
                    echo '</tr></tfoot>';
                    echo '</table></div>';
                } else {
                    echo '<div class="alert alert-warning">لا توجد رواتب</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">خطأ في عرض الرواتب: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="text-center mt-4">
            <small class="text-muted">
                © 2024 نظام إدارة روضة الأطفال - النظام المحاسبي المتكامل
                <br>
                آخر تحديث: <?= date('Y-m-d H:i:s') ?>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
if ($conn) {
    $conn->close();
}
?>
