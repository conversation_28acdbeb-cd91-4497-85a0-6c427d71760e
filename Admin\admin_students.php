<?php
error_reporting(0);
ini_set('display_errors', 0);
header('Content-Type: text/html; charset=UTF-8');
session_start();

include "addon/dbcon.php";
@mysqli_query($con, "SET NAMES utf8");
@mysqli_query($con, "SET CHARACTER SET utf8");
@mysqli_query($con, "SET character_set_connection=utf8");

$message = '';
$messageType = 'success';

// معالجة البحث
$search = '';
if (isset($_GET['search'])) {
    $search = mysqli_real_escape_string($con, $_GET['search']);
}

// جلب الطلاب
$where_clause = '';
if (!empty($search)) {
    $where_clause = "WHERE name LIKE '%$search%' OR email LIKE '%$search%' OR phone LIKE '%$search%'";
}

$students = @mysqli_query($con, "SELECT * FROM stud_tb $where_clause ORDER BY name ASC");
if (!$students) {
    $students = false;
}

// إحصائيات سريعة
$total_students = 0;
$active_students = 0;

$stats_result = @mysqli_query($con, "SELECT COUNT(*) as total FROM stud_tb");
if ($stats_result) {
    $row = mysqli_fetch_assoc($stats_result);
    $total_students = $row['total'];
}

$active_result = @mysqli_query($con, "SELECT COUNT(*) as active FROM stud_tb WHERE status = 'active'");
if ($active_result) {
    $row = mysqli_fetch_assoc($active_result);
    $active_students = $row['active'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 إدارة الطلاب - أكاديمية كيدز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 2rem 0;
        }

        .container-fluid {
            max-width: 1400px;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
        }

        .header-card h1 {
            color: #2c3e50;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #667eea;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .stat-card .label {
            color: #7f8c8d;
            font-weight: 600;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .content-card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .search-box {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .btn-search {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .table th {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            font-weight: bold;
            border: none;
            padding: 1rem;
            color: #2c3e50;
        }

        .table td {
            padding: 1rem;
            border: none;
            border-bottom: 1px solid #f1f2f6;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .badge-custom {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .status-suspended { background: #dc3545; color: white; }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 0 1rem;
            }
            
            .header-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }
            
            .content-card-body {
                padding: 1.5rem;
            }
            
            .table-responsive {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <a href="student_app_control.php" class="back-btn">
        <i class="fas fa-arrow-right"></i>
    </a>

    <div class="container-fluid">
        <!-- Header -->
        <div class="header-card">
            <h1>👥 إدارة الطلاب</h1>
            <p style="color: #7f8c8d; margin: 0;">عرض وإدارة بيانات الطلاب المسجلين</p>
        </div>

        <!-- Stats -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="number"><?php echo $total_students; ?></div>
                <div class="label">إجمالي الطلاب</div>
            </div>
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="number"><?php echo $active_students; ?></div>
                <div class="label">الطلاب النشطون</div>
            </div>
            <div class="stat-card">
                <div class="icon">
                    <i class="fas fa-user-clock"></i>
                </div>
                <div class="number"><?php echo $total_students - $active_students; ?></div>
                <div class="label">غير نشطين</div>
            </div>
        </div>

        <!-- Search -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-9">
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn-search w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- Students List -->
        <div class="content-card">
            <div class="content-card-header">
                <i class="fas fa-list"></i> قائمة الطلاب
                <?php if (!empty($search)): ?>
                    <span style="font-weight: normal; opacity: 0.8;">- نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"</span>
                <?php endif; ?>
            </div>
            <div class="content-card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الطالب</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($students && mysqli_num_rows($students) > 0): ?>
                                <?php while ($row = mysqli_fetch_assoc($students)): 
                                    $status_classes = [
                                        'active' => 'status-active',
                                        'inactive' => 'status-inactive',
                                        'suspended' => 'status-suspended'
                                    ];
                                    $status_names = [
                                        'active' => 'نشط',
                                        'inactive' => 'غير نشط',
                                        'suspended' => 'معلق'
                                    ];
                                ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="student-avatar me-3">
                                                    <?php echo strtoupper(substr($row['name'] ?? 'ط', 0, 1)); ?>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($row['name'] ?? 'غير محدد'); ?></strong>
                                                    <?php if (!empty($row['student_id'])): ?>
                                                        <br><small class="text-muted">ID: <?php echo htmlspecialchars($row['student_id']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if (!empty($row['email'])): ?>
                                                <a href="mailto:<?php echo htmlspecialchars($row['email']); ?>" style="color: #667eea; text-decoration: none;">
                                                    <?php echo htmlspecialchars($row['email']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($row['phone'])): ?>
                                                <a href="tel:<?php echo htmlspecialchars($row['phone']); ?>" style="color: #667eea; text-decoration: none;">
                                                    <?php echo htmlspecialchars($row['phone']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($row['created_at'])): ?>
                                                <?php echo date('Y-m-d', strtotime($row['created_at'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge-custom <?php echo $status_classes[$row['status'] ?? 'active'] ?? 'status-active'; ?>">
                                                <?php echo $status_names[$row['status'] ?? 'active'] ?? 'نشط'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center" style="padding: 3rem;">
                                        <i class="fas fa-users" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                                        <h4 style="color: #7f8c8d;">
                                            <?php if (!empty($search)): ?>
                                                لا توجد نتائج للبحث
                                            <?php else: ?>
                                                لا يوجد طلاب مسجلون حتى الآن
                                            <?php endif; ?>
                                        </h4>
                                        <p style="color: #95a5a6;">
                                            <?php if (!empty($search)): ?>
                                                جرب البحث بكلمات مختلفة أو تحقق من الإملاء
                                            <?php else: ?>
                                                عندما يسجل الطلاب في التطبيق، ستظهر بياناتهم هنا
                                            <?php endif; ?>
                                        </p>
                                        <?php if (!empty($search)): ?>
                                            <a href="admin_students.php" class="btn-search" style="text-decoration: none;">
                                                <i class="fas fa-list"></i> عرض جميع الطلاب
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus search input if there's a search term
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });

        // Add loading state to search button
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.btn-search');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
