<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        // المستخدم مسموح له
    }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "dbcon.php";
$datenow = date('Y-m-d');

// استقبال البيانات من POST بدلاً من GET
$id = $_POST['id'];

// التحقق من وجود سجل حضور لهذا الطالب في نفس اليوم
$check = "SELECT * FROM `stat` WHERE id_stud='$id' AND DATE(data_stat)='$datenow'";
$res = mysqli_query($con, $check);
$ch = mysqli_num_rows($res);

if($ch > 0){
    // حذف سجل الحضور أو الغياب للطالب في هذا اليوم
    $delete = "DELETE FROM `stat` WHERE id_stud='$id' AND DATE(data_stat)='$datenow'";
    $res = mysqli_query($con, $delete);
    
    if($res){
        echo 1; // تم الحذف بنجاح
    }else{
        echo 0; // خطأ في الحذف
    }
}else{
    echo 0; // لا يوجد سجل للحذف
}
?>