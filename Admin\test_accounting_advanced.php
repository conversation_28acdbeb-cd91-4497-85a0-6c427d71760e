<?php
session_start();

// التحقق من صلاحيات المستخدم
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    die("غير مصرح لك بالوصول لهذه الصفحة");
}

echo "<h1>🎉 تم إنشاء نظام الحسابات المتقدم بنجاح!</h1>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>💰 المميزات الجديدة:</h2>";
echo "<ul>";
echo "<li>✅ <strong>تصميم حديث ومتطور:</strong> واجهة عصرية وجذابة</li>";
echo "<li>✅ <strong>فلاتر متقدمة:</strong> اختيار المستخدم، النوع، التاريخ، البحث النصي</li>";
echo "<li>✅ <strong>إحصائيات سريعة:</strong> الإيرادات، المصروفات، الرواتب، صافي الربح</li>";
echo "<li>✅ <strong>أزرار فلترة تلقائية:</strong> تحديث فوري عند التغيير</li>";
echo "<li>✅ <strong>تحكم كامل:</strong> عرض، تعديل، حذف المعاملات</li>";
echo "<li>✅ <strong>إجراءات شاملة:</strong> تصدير Excel، طباعة، ملخص مالي</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🎯 الوظائف المتوفرة:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px;'>";
echo "<h3>🔍 فلاتر البحث</h3>";
echo "<ul>";
echo "<li><strong>اختيار المستخدم:</strong> قائمة منسدلة لجميع المستخدمين</li>";
echo "<li><strong>نوع الحساب:</strong> إيرادات، مصروفات، رواتب</li>";
echo "<li><strong>فترة زمنية:</strong> من تاريخ - إلى تاريخ</li>";
echo "<li><strong>البحث النصي:</strong> البحث في الأسماء</li>";
echo "<li><strong>تطبيق تلقائي:</strong> تحديث فوري عند التغيير</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px;'>";
echo "<h3>📊 الإحصائيات السريعة</h3>";
echo "<ul>";
echo "<li><strong>إجمالي الإيرادات:</strong> مع تأثير العد التصاعدي</li>";
echo "<li><strong>إجمالي المصروفات:</strong> بألوان مميزة</li>";
echo "<li><strong>إجمالي الرواتب:</strong> حسابات دقيقة</li>";
echo "<li><strong>صافي الربح:</strong> حساب تلقائي للأرباح</li>";
echo "<li><strong>عدد المعاملات:</strong> إحصائية شاملة</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
echo "<h3>⚡ الإجراءات السريعة</h3>";
echo "<ul>";
echo "<li><strong>تصدير Excel:</strong> تحميل البيانات كملف Excel</li>";
echo "<li><strong>طباعة التقرير:</strong> تقرير مالي شامل</li>";
echo "<li><strong>إضافة مصروف:</strong> رابط سريع للإضافة</li>";
echo "<li><strong>ملخص مالي:</strong> مخطط دائري تفاعلي</li>";
echo "<li><strong>تحديث تلقائي:</strong> كل 5 دقائق</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px;'>";
echo "<h3>🛠️ إدارة المعاملات</h3>";
echo "<ul>";
echo "<li><strong>عرض التفاصيل:</strong> نافذة منبثقة مع كامل التفاصيل</li>";
echo "<li><strong>تعديل المصروفات:</strong> رابط مباشر للتعديل</li>";
echo "<li><strong>حذف المعاملات:</strong> حذف آمن مع تأكيد</li>";
echo "<li><strong>تصنيف بالألوان:</strong> ألوان مختلفة لكل نوع</li>";
echo "<li><strong>جدول متجاوب:</strong> يعمل على جميع الأجهزة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h2>🎨 التصميم والواجهة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<ul>";
echo "<li>✅ <strong>تصميم متجاوب:</strong> يعمل على الهواتف والحاسوب</li>";
echo "<li>✅ <strong>ألوان مميزة:</strong> أخضر للإيرادات، أحمر للمصروفات، برتقالي للرواتب</li>";
echo "<li>✅ <strong>تأثيرات حركية:</strong> عد تصاعدي وحركات ناعمة</li>";
echo "<li>✅ <strong>أيقونات واضحة:</strong> FontAwesome للوضوح</li>";
echo "<li>✅ <strong>نوافذ منبثقة:</strong> Bootstrap Modal للتفاعل</li>";
echo "<li>✅ <strong>مخططات بيانية:</strong> Chart.js للرسوم البيانية</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📋 الملفات المنشأة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<ol>";
echo "<li><strong>accounting_advanced.php</strong> - الصفحة الرئيسية للحسابات</li>";
echo "<li><strong>get_transaction_details.php</strong> - عرض تفاصيل المعاملات</li>";
echo "<li><strong>delete_transaction.php</strong> - حذف المعاملات</li>";
echo "<li><strong>test_accounting_advanced.php</strong> - صفحة الاختبار هذه</li>";
echo "</ol>";
echo "</div>";

// اختبار الاتصال بقاعدة البيانات
include "addon/dbcon.php";

echo "<h2>🔍 اختبار الاتصال:</h2>";
if ($con) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار الجداول المطلوبة
    $tables = ['users_tb', 'stud_tb', 'stud_pay', 'depit_tb', 'employ_tb'];
    
    foreach ($tables as $table) {
        $test_query = "SELECT COUNT(*) as count FROM $table";
        $test_result = mysqli_query($con, $test_query);
        
        if ($test_result) {
            $count = mysqli_fetch_assoc($test_result)['count'];
            echo "<p style='color: green;'>✅ جدول $table: $count سجل</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في جدول $table: " . mysqli_error($con) . "</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

echo "<h2>🚀 خطوات الاستخدام:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>1. الوصول للصفحة:</h3>";
echo "<p>انقر على الرابط أدناه للوصول لنظام الحسابات المتقدم</p>";

echo "<h3>2. استخدام الفلاتر:</h3>";
echo "<ul>";
echo "<li>اختر مستخدم محدد أو اتركه على 'جميع المستخدمين'</li>";
echo "<li>اختر نوع الحساب (إيرادات، مصروفات، رواتب) أو اتركه فارغ للكل</li>";
echo "<li>حدد فترة زمنية باستخدام 'من تاريخ' و 'إلى تاريخ'</li>";
echo "<li>استخدم البحث النصي للبحث في الأسماء</li>";
echo "</ul>";

echo "<h3>3. استخدام الإجراءات:</h3>";
echo "<ul>";
echo "<li>انقر على 'عرض التفاصيل' لرؤية تفاصيل المعاملة</li>";
echo "<li>استخدم 'تصدير Excel' لتحميل البيانات</li>";
echo "<li>انقر على 'طباعة التقرير' للحصول على تقرير مطبوع</li>";
echo "<li>اعرض 'الملخص المالي' للحصول على مخطط دائري</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='accounting_advanced.php' target='_blank' style='background: #2c3e50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 1.1rem; margin: 0 10px;'>💰 نظام الحسابات المتقدم</a>";
echo "<a href='acounting.php' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 1.1rem; margin: 0 10px;'>📊 الصفحة القديمة</a>";
echo "<a href='home.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 1.1rem; margin: 0 10px;'>🏠 العودة للرئيسية</a>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 20px; background: #d4edda; border-radius: 10px; text-align: center;'>";
echo "<h3 style='color: #155724; margin: 0;'>🎊 نظام الحسابات المتقدم جاهز!</h3>";
echo "<p style='color: #155724; margin: 10px 0 0 0;'>نظام شامل ومتطور لإدارة جميع الحسابات المالية مع فلاتر متقدمة وإحصائيات تفاعلية</p>";
echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار نظام الحسابات المتقدم</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
