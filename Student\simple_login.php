<?php
session_start();
include "addon/dbcon.php";

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $student_id = trim($_POST['student_id']);
    $password = trim($_POST['password']);
    
    if (!empty($student_id) && !empty($password)) {
        // البحث عن الطالب
        $sql = "SELECT * FROM stud_tb WHERE id = '$student_id' OR id_note = '$student_id'";
        $result = mysqli_query($con, $sql);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $student = mysqli_fetch_assoc($result);
            
            // كلمات مرور بسيطة للاختبار
            if ($password == '123456' || $password == $student['id'] || $password == $student['name']) {
                // إنشاء جلسة الطالب
                $_SESSION['student'] = (object) [
                    'id' => $student['id'],
                    'name' => $student['name'],
                    'age' => $student['age'],
                    'sex' => $student['sex'],
                    'catg' => $student['catg'],
                    'p_name' => $student['p_name'],
                    'p_phone' => $student['p_phone']
                ];
                
                header("location: index.php");
                exit();
            } else {
                $error_message = 'كلمة المرور غير صحيحة. جرب: 123456 أو رقم الطالب أو اسم الطالب';
            }
        } else {
            $error_message = 'رقم الطالب غير موجود';
        }
    } else {
        $error_message = 'يرجى ملء جميع الحقول';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الطالب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #7f8c8d;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #3498db, #2c3e50);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
        }

        .alert {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }

        .help-text {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .test-info {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }

        .test-info h4 {
            margin-bottom: 0.5rem;
        }

        .test-info ul {
            margin: 0;
            padding-right: 1rem;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🎓 تسجيل دخول الطالب</h1>
            <p>نظام إدارة الحضانة</p>
        </div>
        
        <div class="test-info">
            <h4>🧪 للاختبار:</h4>
            <ul>
                <li><strong>رقم الطالب:</strong> أي رقم موجود في قاعدة البيانات</li>
                <li><strong>كلمة المرور:</strong> 123456 أو رقم الطالب أو اسم الطالب</li>
            </ul>
            <div style="text-align: center; margin-top: 15px;">
                <a href="show_all_students.php" style="background: #17a2b8; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s ease;">
                    👥 عرض جميع الطلاب المتاحين
                </a>
            </div>
        </div>
        
        <?php if (!empty($error_message)): ?>
            <div class="alert">
                ⚠️ <?php echo $error_message; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="student_id">🆔 رقم الطالب:</label>
                <input type="text" id="student_id" name="student_id" class="form-control" 
                       placeholder="أدخل رقم الطالب" required>
            </div>
            
            <div class="form-group">
                <label for="password">🔒 كلمة المرور:</label>
                <input type="password" id="password" name="password" class="form-control" 
                       placeholder="أدخل كلمة المرور" required>
                <div class="help-text">
                    جرب: 123456 أو رقم الطالب أو اسم الطالب
                </div>
            </div>
            
            <button type="submit" class="btn-login">
                🚀 تسجيل الدخول
            </button>
        </form>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="test_login.php" style="color: #3498db; text-decoration: none;">
                📋 عرض بيانات الاختبار
            </a>
        </div>
    </div>

    <script>
        // التحقق من وجود بيانات تسجيل دخول سريع
        document.addEventListener('DOMContentLoaded', function() {
            const quickLoginId = localStorage.getItem('quickLoginId');
            const quickLoginPassword = localStorage.getItem('quickLoginPassword');
            const quickLoginName = localStorage.getItem('quickLoginName');

            if (quickLoginId && quickLoginPassword) {
                // ملء الحقول تلقائياً
                document.getElementById('student_id').value = quickLoginId;
                document.getElementById('password').value = quickLoginPassword;

                // إظهار رسالة ترحيب
                const welcomeMsg = document.createElement('div');
                welcomeMsg.style.cssText = `
                    background: #d4edda;
                    color: #155724;
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    border: 1px solid #c3e6cb;
                    text-align: center;
                    font-weight: bold;
                `;
                welcomeMsg.innerHTML = `🎉 مرحباً ${quickLoginName}!<br>تم ملء البيانات تلقائياً - اضغط تسجيل الدخول`;

                const form = document.querySelector('form');
                form.parentNode.insertBefore(welcomeMsg, form);

                // مسح البيانات المحفوظة
                localStorage.removeItem('quickLoginId');
                localStorage.removeItem('quickLoginPassword');
                localStorage.removeItem('quickLoginName');

                // تركيز على زر تسجيل الدخول
                document.querySelector('button[type="submit"]').focus();
            }
        });

        // تأثيرات بصرية للروابط
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
            });

            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
