<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['student'])) {
    header("location: login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍎 عد الفواكه - أكاديمية كيدز</title>
    <style>
        body {
            font-family: 'Comic Sans MS', Arial, sans-serif;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            margin: 0;
            padding: 20px;
            text-align: center;
            direction: rtl;
            min-height: 100vh;
        }
        
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .game-title {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .question-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 3px solid #3498db;
        }
        
        .question-text {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .fruits-display {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
            min-height: 100px;
            align-items: center;
        }
        
        .fruit {
            font-size: 3rem;
            animation: fruitBounce 2s infinite;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .fruit:hover {
            transform: scale(1.2);
        }
        
        .answers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 15px;
            margin: 30px 0;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .answer-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 20px;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .answer-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.5);
        }
        
        .answer-btn.correct {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            animation: correctAnswer 0.5s ease;
        }
        
        .answer-btn.wrong {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            animation: wrongAnswer 0.5s ease;
        }
        
        .score-board {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
        }
        
        .back-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            font-weight: bold;
            display: inline-block;
            margin-top: 1rem;
        }
        
        .celebration {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 4rem;
            z-index: 1000;
            animation: celebrate 1s ease-out;
            pointer-events: none;
        }
        
        @keyframes fruitBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }
        
        @keyframes celebrate {
            0% { transform: translate(-50%, -50%) scale(0) rotate(0deg); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.2) rotate(180deg); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">🍎 تعلم العد مع الفواكه</h1>
        
        <div class="score-board">
            🏆 النقاط: <span id="score">0</span> | 🎯 المستوى: <span id="level">1</span>
        </div>
        
        <div class="question-area">
            <div class="question-text" id="questionText">كم عدد الفواكه؟</div>
            <div class="fruits-display" id="fruitsDisplay"></div>
        </div>
        
        <div class="answers-grid" id="answersGrid"></div>
        
        <div>
            <button class="btn" onclick="nextQuestion()">
                ➡️ السؤال التالي
            </button>
            <button class="btn" onclick="resetGame()">
                🔄 بداية جديدة
            </button>
        </div>
        
        <a href="games.php" class="back-btn">العودة للألعاب</a>
    </div>
    
    <script>
        let score = 0;
        let level = 1;
        let currentAnswer = 0;
        let gameData = {
            fruits: ['🍎', '🍊', '🍌', '🍇', '🍓', '🥝', '🍑', '🥭', '🍍', '🥥'],
            maxCount: 5
        };
        
        function generateQuestion() {
            // تحديد عدد الفواكه بناءً على المستوى
            const maxFruits = Math.min(gameData.maxCount + Math.floor(level / 3), 10);
            const fruitCount = Math.floor(Math.random() * maxFruits) + 1;
            const selectedFruit = gameData.fruits[Math.floor(Math.random() * gameData.fruits.length)];
            
            currentAnswer = fruitCount;
            
            // عرض الفواكه
            const fruitsDisplay = document.getElementById('fruitsDisplay');
            fruitsDisplay.innerHTML = '';
            
            for (let i = 0; i < fruitCount; i++) {
                const fruit = document.createElement('span');
                fruit.className = 'fruit';
                fruit.textContent = selectedFruit;
                fruit.style.animationDelay = (i * 0.1) + 's';
                fruit.onclick = () => countFruit(fruit);
                fruitsDisplay.appendChild(fruit);
            }
            
            // إنشاء خيارات الإجابة
            generateAnswerOptions(fruitCount);
            
            // تحديث نص السؤال
            document.getElementById('questionText').textContent = `كم عدد ${getFruitName(selectedFruit)}؟`;
        }
        
        function getFruitName(emoji) {
            const fruitNames = {
                '🍎': 'التفاح',
                '🍊': 'البرتقال',
                '🍌': 'الموز',
                '🍇': 'العنب',
                '🍓': 'الفراولة',
                '🥝': 'الكيوي',
                '🍑': 'الكرز',
                '🥭': 'المانجو',
                '🍍': 'الأناناس',
                '🥥': 'جوز الهند'
            };
            return fruitNames[emoji] || 'الفواكه';
        }
        
        function generateAnswerOptions(correctAnswer) {
            const answersGrid = document.getElementById('answersGrid');
            answersGrid.innerHTML = '';
            
            // إنشاء مجموعة من الخيارات
            const options = new Set();
            options.add(correctAnswer);
            
            // إضافة خيارات خاطئة
            while (options.size < 4) {
                const wrongAnswer = Math.floor(Math.random() * 10) + 1;
                if (wrongAnswer !== correctAnswer) {
                    options.add(wrongAnswer);
                }
            }
            
            // تحويل إلى مصفوفة وخلطها
            const optionsArray = Array.from(options).sort(() => Math.random() - 0.5);
            
            // إنشاء أزرار الإجابة
            optionsArray.forEach(option => {
                const btn = document.createElement('button');
                btn.className = 'answer-btn';
                btn.textContent = option;
                btn.onclick = () => checkAnswer(option, btn);
                answersGrid.appendChild(btn);
            });
        }
        
        function checkAnswer(selectedAnswer, button) {
            const allButtons = document.querySelectorAll('.answer-btn');
            
            if (selectedAnswer === currentAnswer) {
                // إجابة صحيحة
                button.classList.add('correct');
                score += 10 * level;
                showCelebration('🎉');
                
                // تأثير على الفواكه
                document.querySelectorAll('.fruit').forEach(fruit => {
                    fruit.style.animation = 'fruitBounce 0.5s ease';
                });
                
                setTimeout(() => {
                    nextQuestion();
                }, 1500);
                
            } else {
                // إجابة خاطئة
                button.classList.add('wrong');
                showCelebration('😔');
                
                // إظهار الإجابة الصحيحة
                allButtons.forEach(btn => {
                    if (parseInt(btn.textContent) === currentAnswer) {
                        btn.classList.add('correct');
                    }
                });
            }
            
            // تعطيل جميع الأزرار
            allButtons.forEach(btn => {
                btn.disabled = true;
            });
            
            updateScore();
        }
        
        function countFruit(fruitElement) {
            // تأثير عند النقر على الفاكهة
            fruitElement.style.transform = 'scale(1.3)';
            fruitElement.style.filter = 'brightness(1.5)';
            
            setTimeout(() => {
                fruitElement.style.transform = 'scale(1)';
                fruitElement.style.filter = 'brightness(1)';
            }, 300);
        }
        
        function showCelebration(emoji) {
            const celebration = document.createElement('div');
            celebration.className = 'celebration';
            celebration.textContent = emoji;
            document.body.appendChild(celebration);
            
            setTimeout(() => {
                document.body.removeChild(celebration);
            }, 1000);
        }
        
        function nextQuestion() {
            // زيادة المستوى كل 5 أسئلة صحيحة
            if (score > 0 && score % 50 === 0) {
                level++;
                showCelebration('⭐');
            }
            
            generateQuestion();
        }
        
        function updateScore() {
            document.getElementById('score').textContent = score;
            document.getElementById('level').textContent = level;
        }
        
        function resetGame() {
            score = 0;
            level = 1;
            updateScore();
            generateQuestion();
        }
        
        // بدء اللعبة
        document.addEventListener('DOMContentLoaded', function() {
            generateQuestion();
            
            // ترحيب صوتي
            if ('speechSynthesis' in window) {
                setTimeout(() => {
                    const utterance = new SpeechSynthesisUtterance('مرحباً! لنتعلم العد مع الفواكه اللذيذة');
                    utterance.lang = 'ar-SA';
                    speechSynthesis.speak(utterance);
                }, 1000);
            }
        });
    </script>
</body>
</html>
