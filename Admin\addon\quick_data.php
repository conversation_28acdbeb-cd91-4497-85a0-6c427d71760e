<?php
session_start();

// التحقق من صلاحية المدير
if (!isset($_SESSION['user']) || $_SESSION['user']->role !== "Admin") {
    echo '<div class="alert alert-danger">غير مصرح</div>';
    exit();
}

include "dbcon.php";

if (!$con) {
    echo '<div class="alert alert-danger">خطأ في الاتصال بقاعدة البيانات</div>';
    exit();
}

// معالجة البيانات المرسلة
$input = isset($_POST['myinput']) ? $_POST['myinput'] : (isset($_GET['myinput']) ? $_GET['myinput'] : '');
$user_id = isset($_POST['user_id']) ? $_POST['user_id'] : (isset($_GET['user_id']) ? $_GET['user_id'] : '0');
$dates = isset($_POST['dates']) ? $_POST['dates'] : '';
$datee = isset($_POST['datee']) ? $_POST['datee'] : '';

if ($input == 'الكل' || empty($input)) {
    // عرض جميع البيانات
    ?>
    <div class="alert alert-info">
        <h5><i class="fas fa-chart-bar"></i> عرض شامل للمعاملات المالية</h5>
        <p class="mb-0">آخر المعاملات في النظام</p>
    </div>
    
    <div class="row">
        <!-- الإيرادات -->
        <div class="col-md-4 mb-4">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-arrow-up"></i> آخر الإيرادات</h6>
                </div>
                <div class="card-body">
                    <?php
                    $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
                    $revenue_query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name 
                                      FROM stud_tb, stud_pay, users_tb 
                                      WHERE stud_pay.id_stud = stud_tb.id 
                                      AND stud_tb.userID = users_tb.id_user 
                                      $user_condition 
                                      ORDER BY stud_pay.datein DESC LIMIT 5";
                    $revenue_result = mysqli_query($con, $revenue_query);
                    
                    if ($revenue_result && mysqli_num_rows($revenue_result) > 0) {
                        $total = 0;
                        while ($item = mysqli_fetch_assoc($revenue_result)) {
                            $total += $item['cash_stud'];
                            echo '<div class="mb-2 p-2 border-left border-success bg-light">';
                            echo '<strong>' . htmlspecialchars($item['name']) . '</strong><br>';
                            echo '<span class="text-success">IQD ' . number_format($item['cash_stud']) . '</span><br>';
                            echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['datein'])) . '</small>';
                            echo '</div>';
                        }
                        echo '<div class="mt-2 p-2 bg-success text-white text-center rounded">';
                        echo '<strong>المجموع: IQD ' . number_format($total) . '</strong>';
                        echo '</div>';
                    } else {
                        echo '<p class="text-muted text-center">لا توجد إيرادات</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <!-- المصروفات -->
        <div class="col-md-4 mb-4">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-arrow-down"></i> آخر المصروفات</h6>
                </div>
                <div class="card-body">
                    <?php
                    $user_condition = ($user_id != '0') ? " AND depit_tb.userID = '$user_id'" : "";
                    $expenses_query = "SELECT depit_tb.depit_note, depit_tb.depit_cash, depit_tb.depit_date, users_tb.user_name 
                                       FROM users_tb, depit_tb 
                                       WHERE depit_tb.userID = users_tb.id_user 
                                       $user_condition 
                                       ORDER BY depit_tb.depit_date DESC LIMIT 5";
                    $expenses_result = mysqli_query($con, $expenses_query);
                    
                    if ($expenses_result && mysqli_num_rows($expenses_result) > 0) {
                        $total = 0;
                        while ($item = mysqli_fetch_assoc($expenses_result)) {
                            $total += $item['depit_cash'];
                            echo '<div class="mb-2 p-2 border-left border-danger bg-light">';
                            echo '<strong>' . htmlspecialchars($item['depit_note']) . '</strong><br>';
                            echo '<span class="text-danger">IQD ' . number_format($item['depit_cash']) . '</span><br>';
                            echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['depit_date'])) . '</small>';
                            echo '</div>';
                        }
                        echo '<div class="mt-2 p-2 bg-danger text-white text-center rounded">';
                        echo '<strong>المجموع: IQD ' . number_format($total) . '</strong>';
                        echo '</div>';
                    } else {
                        echo '<p class="text-muted text-center">لا توجد مصروفات</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <!-- الرواتب -->
        <div class="col-md-4 mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-users"></i> آخر الرواتب</h6>
                </div>
                <div class="card-body">
                    <?php
                    $user_condition = ($user_id != '0') ? " AND employ_tb.userID = '$user_id'" : "";
                    $salaries_query = "SELECT employ_tb.f_name, employ_tb.job, employ_tb.salary, employ_tb.date_start, users_tb.user_name 
                                       FROM users_tb, employ_tb 
                                       WHERE employ_tb.userID = users_tb.id_user 
                                       $user_condition 
                                       ORDER BY employ_tb.date_start DESC LIMIT 5";
                    $salaries_result = mysqli_query($con, $salaries_query);
                    
                    if ($salaries_result && mysqli_num_rows($salaries_result) > 0) {
                        $total = 0;
                        while ($item = mysqli_fetch_assoc($salaries_result)) {
                            $total += $item['salary'];
                            echo '<div class="mb-2 p-2 border-left border-warning bg-light">';
                            echo '<strong>' . htmlspecialchars($item['f_name']) . '</strong><br>';
                            echo '<small>' . htmlspecialchars($item['job']) . '</small><br>';
                            echo '<span class="text-warning">IQD ' . number_format($item['salary']) . '</span><br>';
                            echo '<small class="text-muted">' . date('Y-m-d', strtotime($item['date_start'])) . '</small>';
                            echo '</div>';
                        }
                        echo '<div class="mt-2 p-2 bg-warning text-dark text-center rounded">';
                        echo '<strong>المجموع: IQD ' . number_format($total) . '</strong>';
                        echo '</div>';
                    } else {
                        echo '<p class="text-muted text-center">لا توجد رواتب</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <div class="alert alert-light">
            <p class="mb-0"><i class="fas fa-info-circle"></i> استخدم الفلاتر أعلاه لعرض تفاصيل أكثر لكل نوع من المعاملات</p>
        </div>
    </div>
    
    <?php
} elseif ($input == 'ايرادات') {
    // عرض الإيرادات
    ?>
    <div class="alert alert-success">
        <h5><i class="fas fa-arrow-up"></i> الإيرادات</h5>
    </div>
    
    <?php
    $user_condition = ($user_id != '0') ? " AND stud_tb.userID = '$user_id'" : "";
    $date_condition = ($dates && $datee) ? " AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'" : "";
    
    $query = "SELECT stud_tb.name, stud_pay.cash_stud, stud_pay.datein, users_tb.user_name, stud_pay.id_pay
              FROM stud_tb, stud_pay, users_tb 
              WHERE stud_pay.id_stud = stud_tb.id 
              AND stud_tb.userID = users_tb.id_user 
              $date_condition $user_condition 
              ORDER BY stud_pay.datein DESC";
    
    $result = mysqli_query($con, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo '<div class="table-responsive">';
        echo '<table class="table table-striped table-hover">';
        echo '<thead class="thead-dark">';
        echo '<tr>';
        echo '<th><i class="fas fa-child"></i> اسم الطالب</th>';
        echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
        echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
        echo '<th><i class="fas fa-user"></i> المستخدم</th>';
        echo '<th><i class="fas fa-receipt"></i> رقم الوصل</th>';
        echo '</tr>';
        echo '</thead><tbody>';
        
        $total = 0;
        while ($row = mysqli_fetch_assoc($result)) {
            $total += $row['cash_stud'];
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($row['name']) . '</strong></td>';
            echo '<td><span class="badge badge-success">IQD ' . number_format($row['cash_stud']) . '</span></td>';
            echo '<td>' . date('Y-m-d', strtotime($row['datein'])) . '</td>';
            echo '<td><span class="badge badge-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
            echo '<td><span class="badge badge-info">' . $row['id_pay'] . '</span></td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '<tfoot><tr class="table-success">';
        echo '<th>الإجمالي:</th>';
        echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
        echo '<th colspan="3">' . mysqli_num_rows($result) . ' معاملة</th>';
        echo '</tr></tfoot>';
        echo '</table></div>';
    } else {
        echo '<div class="alert alert-warning text-center">';
        echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
        echo '<h5>لا توجد إيرادات</h5>';
        echo '<p>لم يتم العثور على إيرادات للفترة المحددة</p>';
        echo '</div>';
    }
    
} elseif ($input == 'مصروفات') {
    // عرض المصروفات
    ?>
    <div class="alert alert-danger">
        <h5><i class="fas fa-arrow-down"></i> المصروفات</h5>
    </div>
    
    <?php
    $user_condition = ($user_id != '0') ? " AND depit_tb.userID = '$user_id'" : "";
    $date_condition = ($dates && $datee) ? " AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'" : "";
    
    $query = "SELECT depit_tb.*, users_tb.user_name 
              FROM users_tb, depit_tb 
              WHERE depit_tb.userID = users_tb.id_user 
              $date_condition $user_condition 
              ORDER BY depit_tb.depit_date DESC";
    
    $result = mysqli_query($con, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo '<div class="table-responsive">';
        echo '<table class="table table-striped table-hover">';
        echo '<thead class="thead-dark">';
        echo '<tr>';
        echo '<th><i class="fas fa-sticky-note"></i> وصف المصروف</th>';
        echo '<th><i class="fas fa-money-bill"></i> المبلغ</th>';
        echo '<th><i class="fas fa-calendar"></i> التاريخ</th>';
        echo '<th><i class="fas fa-user"></i> المستخدم</th>';
        echo '</tr>';
        echo '</thead><tbody>';
        
        $total = 0;
        while ($row = mysqli_fetch_assoc($result)) {
            $total += $row['depit_cash'];
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($row['depit_note']) . '</strong></td>';
            echo '<td><span class="badge badge-danger">IQD ' . number_format($row['depit_cash']) . '</span></td>';
            echo '<td>' . date('Y-m-d', strtotime($row['depit_date'])) . '</td>';
            echo '<td><span class="badge badge-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '<tfoot><tr class="table-danger">';
        echo '<th>الإجمالي:</th>';
        echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
        echo '<th colspan="2">' . mysqli_num_rows($result) . ' معاملة</th>';
        echo '</tr></tfoot>';
        echo '</table></div>';
    } else {
        echo '<div class="alert alert-warning text-center">';
        echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
        echo '<h5>لا توجد مصروفات</h5>';
        echo '<p>لم يتم العثور على مصروفات للفترة المحددة</p>';
        echo '</div>';
    }
    
} elseif ($input == 'رواتب') {
    // عرض الرواتب
    ?>
    <div class="alert alert-warning">
        <h5><i class="fas fa-users"></i> الرواتب</h5>
    </div>
    
    <?php
    $user_condition = ($user_id != '0') ? " AND employ_tb.userID = '$user_id'" : "";
    
    $query = "SELECT employ_tb.*, users_tb.user_name 
              FROM users_tb, employ_tb 
              WHERE employ_tb.userID = users_tb.id_user 
              $user_condition 
              ORDER BY employ_tb.date_start DESC";
    
    $result = mysqli_query($con, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo '<div class="table-responsive">';
        echo '<table class="table table-striped table-hover">';
        echo '<thead class="thead-dark">';
        echo '<tr>';
        echo '<th><i class="fas fa-user"></i> اسم الموظف</th>';
        echo '<th><i class="fas fa-briefcase"></i> المنصب</th>';
        echo '<th><i class="fas fa-money-bill"></i> الراتب</th>';
        echo '<th><i class="fas fa-calendar"></i> تاريخ البداية</th>';
        echo '<th><i class="fas fa-user-tie"></i> المستخدم</th>';
        echo '</tr>';
        echo '</thead><tbody>';
        
        $total = 0;
        while ($row = mysqli_fetch_assoc($result)) {
            $total += $row['salary'];
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($row['f_name']) . '</strong></td>';
            echo '<td>' . htmlspecialchars($row['job']) . '</td>';
            echo '<td><span class="badge badge-warning">IQD ' . number_format($row['salary']) . '</span></td>';
            echo '<td>' . date('Y-m-d', strtotime($row['date_start'])) . '</td>';
            echo '<td><span class="badge badge-primary">' . htmlspecialchars($row['user_name']) . '</span></td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '<tfoot><tr class="table-warning">';
        echo '<th colspan="2">الإجمالي:</th>';
        echo '<th><strong>IQD ' . number_format($total) . '</strong></th>';
        echo '<th colspan="2">' . mysqli_num_rows($result) . ' موظف</th>';
        echo '</tr></tfoot>';
        echo '</table></div>';
    } else {
        echo '<div class="alert alert-warning text-center">';
        echo '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i>';
        echo '<h5>لا توجد رواتب</h5>';
        echo '<p>لم يتم العثور على رواتب</p>';
        echo '</div>';
    }
    
} else {
    echo '<div class="alert alert-info text-center">';
    echo '<i class="fas fa-info-circle fa-3x mb-3"></i>';
    echo '<h5>نوع البيانات غير مدعوم</h5>';
    echo '<p>نوع البيانات المطلوب: ' . htmlspecialchars($input) . '</p>';
    echo '</div>';
}

mysqli_close($con);
?>
