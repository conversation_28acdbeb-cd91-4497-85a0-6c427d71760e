<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تجربة تسجيل الدخول</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            color: white;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .step-card {
            background: rgba(255,255,255,0.95);
            color: #2c3e50;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .step-card:hover {
            transform: translateY(-10px);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 1.5rem;
        }

        .step-card h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .step-card p {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .demo-examples {
            background: rgba(255,255,255,0.95);
            color: #2c3e50;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 3rem;
        }

        .demo-examples h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .example-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .example-card:hover {
            border-color: #3498db;
            transform: scale(1.02);
        }

        .example-card h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .example-data {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            border-right: 4px solid #3498db;
        }

        .example-data .label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .example-data .value {
            background: #3498db;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
        }

        .passwords-list {
            margin-top: 1rem;
        }

        .password-item {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 0.5rem 1rem;
            margin: 0.3rem 0;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .password-item:hover {
            background: #d4edda;
            transform: scale(1.05);
        }

        .try-button {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .try-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .quick-link {
            background: rgba(255,255,255,0.95);
            color: #2c3e50;
            padding: 1.5rem;
            border-radius: 15px;
            text-decoration: none;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quick-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            text-decoration: none;
            color: #2c3e50;
        }

        .quick-link i {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
        }

        .highlight-box {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .highlight-box h3 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }

            .demo-steps {
                grid-template-columns: 1fr;
            }

            .examples-grid {
                grid-template-columns: 1fr;
            }

            .quick-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- رأس الصفحة -->
        <div class="demo-header">
            <h1>🎓 تجربة تسجيل الدخول</h1>
            <p>تعلم كيفية تسجيل الدخول بحساب أي طالب في النظام</p>
        </div>

        <!-- تنبيه مهم -->
        <div class="highlight-box">
            <h3>🔥 جميع الطلاب يمكنهم التسجيل الآن!</h3>
            <p>لا حاجة لإنشاء حسابات جديدة - استخدم بيانات أي طالب موجود في النظام</p>
        </div>

        <!-- خطوات التسجيل -->
        <div class="demo-steps">
            <div class="step-card">
                <div class="step-number">1</div>
                <h3>اختر طالب</h3>
                <p>اختر أي طالب من قائمة الطلاب المتاحين في النظام</p>
            </div>

            <div class="step-card">
                <div class="step-number">2</div>
                <h3>احصل على رقم الطالب</h3>
                <p>انسخ رقم الطالب من بيانات الطالب المختار</p>
            </div>

            <div class="step-card">
                <div class="step-number">3</div>
                <h3>اختر كلمة مرور</h3>
                <p>استخدم أي من كلمات المرور المتاحة للطالب</p>
            </div>

            <div class="step-card">
                <div class="step-number">4</div>
                <h3>سجل الدخول</h3>
                <p>ادخل البيانات في صفحة تسجيل الدخول واستمتع!</p>
            </div>
        </div>

        <!-- أمثلة عملية -->
        <div class="demo-examples">
            <h2>📝 أمثلة عملية للتسجيل</h2>
            <div class="examples-grid">
                <!-- مثال 1 -->
                <div class="example-card">
                    <h4>👦 مثال الطالب الأول</h4>
                    <div class="example-data">
                        <div class="label">رقم الطالب:</div>
                        <div class="value">1</div>
                    </div>
                    <div class="passwords-list">
                        <div class="label">كلمات المرور المتاحة:</div>
                        <div class="password-item" onclick="copyPassword('123456')">123456 (افتراضية)</div>
                        <div class="password-item" onclick="copyPassword('1')">1 (رقم الطالب)</div>
                        <div class="password-item" onclick="copyPassword('اسم الطالب')">اسم الطالب</div>
                    </div>
                    <button class="try-button" onclick="quickLogin('1', '123456')">🚀 جرب الآن</button>
                </div>

                <!-- مثال 2 -->
                <div class="example-card">
                    <h4>👧 مثال الطالب الثاني</h4>
                    <div class="example-data">
                        <div class="label">رقم الطالب:</div>
                        <div class="value">2</div>
                    </div>
                    <div class="passwords-list">
                        <div class="label">كلمات المرور المتاحة:</div>
                        <div class="password-item" onclick="copyPassword('123456')">123456 (افتراضية)</div>
                        <div class="password-item" onclick="copyPassword('2')">2 (رقم الطالب)</div>
                        <div class="password-item" onclick="copyPassword('اسم الطالب')">اسم الطالب</div>
                    </div>
                    <button class="try-button" onclick="quickLogin('2', '123456')">🚀 جرب الآن</button>
                </div>

                <!-- مثال 3 -->
                <div class="example-card">
                    <h4>🎯 أي طالب آخر</h4>
                    <div class="example-data">
                        <div class="label">رقم الطالب:</div>
                        <div class="value">أي رقم</div>
                    </div>
                    <div class="passwords-list">
                        <div class="label">كلمات المرور المتاحة:</div>
                        <div class="password-item">123456 (تعمل مع الجميع)</div>
                        <div class="password-item">رقم الطالب نفسه</div>
                        <div class="password-item">اسم الطالب</div>
                        <div class="password-item">رقم هاتف ولي الأمر</div>
                    </div>
                    <button class="try-button" onclick="window.location.href='show_all_students.php'">👥 عرض جميع الطلاب</button>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="quick-links">
            <a href="show_all_students.php" class="quick-link">
                <i>👥</i>
                عرض جميع الطلاب
            </a>

            <a href="simple_login.php" class="quick-link">
                <i>🚀</i>
                تسجيل الدخول
            </a>

            <a href="complete_setup.php" class="quick-link">
                <i>⚙️</i>
                إعداد النظام
            </a>

            <a href="../Admin/student_app_control.php" class="quick-link">
                <i>👨‍💼</i>
                تحكم الأدمن
            </a>
        </div>
    </div>

    <script>
        function copyPassword(password) {
            navigator.clipboard.writeText(password).then(function() {
                // إظهار رسالة نجاح
                const toast = document.createElement('div');
                toast.textContent = `تم نسخ: ${password}`;
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #27ae60;
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    z-index: 1000;
                    font-weight: bold;
                    animation: slideIn 0.3s ease;
                `;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            });
        }

        function quickLogin(studentId, password) {
            // حفظ البيانات للتسجيل السريع
            localStorage.setItem('quickLoginId', studentId);
            localStorage.setItem('quickLoginPassword', password);
            localStorage.setItem('quickLoginName', `طالب رقم ${studentId}`);
            
            // إظهار رسالة تأكيد
            alert(`سيتم تسجيل الدخول برقم الطالب: ${studentId}\nكلمة المرور: ${password}`);
            
            // الانتقال إلى صفحة تسجيل الدخول
            window.location.href = 'simple_login.php';
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.step-card, .example-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
